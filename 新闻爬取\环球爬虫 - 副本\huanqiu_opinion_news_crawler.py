#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环球网评论新闻爬虫
按照新闻爬虫开发规范实现
"""

import requests
import json
import time
from datetime import datetime
from bs4 import BeautifulSoup
from fake_useragent import UserAgent


class HuanqiuOpinionNewsCrawler:
    def __init__(self):
        self.base_url = "https://opinion.huanqiu.com"
        self.api_url = "https://opinion.huanqiu.com/api/list"
        
        # 评论板块的节点ID
        self.nodes = [
            "/e3pmub6h5/e3pmub75a",
            "/e3pmub6h5/e3pn00if8",
            "/e3pmub6h5/e3pn03vit",
            "/e3pmub6h5/e3pn4bi4t",
            "/e3pmub6h5/e3pr9baf6",
            "/e3pmub6h5/e3prafm0g",
            "/e3pmub6h5/e3prcgifj",
            "/e3pmub6h5/e81curi71",
            "/e3pmub6h5/e81cv14rf",
            "/e3pmub6h5/e81cv14rf/e81cv52ha",
            "/e3pmub6h5/e81cv14rf/e81cvaa3q",
            "/e3pmub6h5/e81cv14rf/e81cvcd7e",
            "/e3pmub6h5/e81cv14rf/fiqa8nr3d",
            "/e3pmub6h5/e81cv14rf/fkmm2pfjb",
            "/e3pmub6h5/eo1dckao0",
            "/e3pmub6h5/f1nptsfsh",
            "/e3pmub6h5/fdark2mrt",
            "/e3pmub6h5/e81cvuabi"
        ]
        
        self.ua = UserAgent()
        self.session = requests.Session()
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://opinion.huanqiu.com/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        
        self.session.headers.update(self.headers)
    
    def get_news_list(self, max_news=10):
        """
        获取新闻列表
        
        Args:
            max_news (int): 最大新闻数量
            
        Returns:
            list: 新闻基本信息列表
        """
        news_list = []
        offset = 0
        limit = 24
        
        while len(news_list) < max_news:
            params = {
                'node': ','.join([f'"{node}"' for node in self.nodes]),
                'offset': offset,
                'limit': limit
            }
            
            try:
                response = self.session.get(self.api_url, params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                articles = data.get('list', [])
                
                if not articles:
                    break
                
                # 只获取今天的文章
                today = datetime.now().date()
                
                for article in articles:
                    if len(news_list) >= max_news:
                        break
                    
                    # 检查是否为今日文章
                    ctime = article.get('ctime', 0)
                    if ctime and str(ctime).isdigit():
                        try:
                            if len(str(ctime)) > 10:
                                timestamp = int(ctime) / 1000
                            else:
                                timestamp = int(ctime)
                            
                            article_date = datetime.fromtimestamp(timestamp).date()
                            if article_date != today:
                                continue
                        except:
                            continue
                    
                    article_id = article.get('aid')
                    title = article.get('title', '')
                    
                    if article_id and title:
                        news_item = {
                            'id': article_id,
                            'title': title,
                            'url': f"https://opinion.huanqiu.com/article/{article_id}",
                            'summary': article.get('summary', ''),
                            'source_name': article.get('source', {}).get('name', '环球网'),
                            'ctime': ctime
                        }
                        news_list.append(news_item)
                
                offset += limit
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"获取新闻列表失败: {e}")
                break
        
        return news_list[:max_news]
    
    def get_news_detail(self, url):
        """
        获取新闻详细内容
        
        Args:
            url (str): 新闻URL
            
        Returns:
            str: 新闻正文内容
        """
        try:
            # 随机更换User-Agent
            headers = {
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://opinion.huanqiu.com/',
                'Connection': 'keep-alive',
            }
            
            response = self.session.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取文章内容
            content = ""
            content_div = soup.find('div', class_='content')
            if content_div:
                article_tag = content_div.find('article')
                if article_tag:
                    # 提取所有段落，过滤掉广告等元素
                    paragraphs = article_tag.find_all('p')
                    content_parts = []
                    for p in paragraphs:
                        # 跳过包含广告的段落
                        if p.find('adv-loader'):
                            continue
                        # 跳过图片说明文字
                        if p.find('i', class_='pic-con'):
                            continue
                        # 跳过视频相关内容
                        if p.find('video') or p.find('div', class_='video-con'):
                            continue
                        
                        text = p.get_text(strip=True)
                        if text and len(text) > 5:  # 过滤掉太短的文本
                            content_parts.append(text)
                    content = '\n\n'.join(content_parts)
            
            # 如果上面的方法没找到内容，尝试其他选择器
            if not content:
                selectors = [
                    '.la-content',
                    '.article-content', 
                    '.content article',
                    '.main-content',
                    '[data-type="rtext"]'
                ]
                
                for selector in selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        paragraphs = content_elem.find_all(['p', 'div'])
                        content_parts = []
                        for p in paragraphs:
                            # 跳过广告相关内容
                            if (p.find('adv-loader') or p.find('i', class_='pic-con') or 
                                p.find('video') or p.find('div', class_='video-con')):
                                continue
                            
                            text = p.get_text(strip=True)
                            if text and len(text) > 10:
                                content_parts.append(text)
                        if content_parts:
                            content = '\n\n'.join(content_parts)
                            break
            
            return content
            
        except Exception as e:
            print(f"获取文章内容失败 {url}: {e}")
            return ""
    
    def format_time(self, timestamp):
        """
        格式化时间为 MM-DD HH:MM 格式
        
        Args:
            timestamp: 时间戳
            
        Returns:
            str: 格式化后的时间字符串
        """
        try:
            if len(str(timestamp)) > 10:
                timestamp = int(timestamp) / 1000
            else:
                timestamp = int(timestamp)
            
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%m-%d %H:%M')
        except:
            return datetime.now().strftime('%m-%d %H:%M')
    
    def get_news(self, max_news=10, get_detail=True):
        """
        获取新闻数据 - 主入口函数
        
        Args:
            max_news (int): 最大新闻数量，默认10
            get_detail (bool): 是否获取详细内容，默认True
            
        Returns:
            list: 新闻数据列表，符合规范格式
        """
        print(f"开始爬取环球网评论新闻，目标数量: {max_news}")
        
        # 获取新闻列表
        news_list = self.get_news_list(max_news)
        
        if not news_list:
            print("未获取到新闻列表")
            return []
        
        print(f"获取到 {len(news_list)} 条新闻基本信息")
        
        # 构建返回数据
        result = []
        
        for i, news_item in enumerate(news_list, 1):
            print(f"正在处理第 {i} 条新闻: {news_item['title'][:30]}...")
            
            # 获取详细内容
            content = ""
            if get_detail:
                content = self.get_news_detail(news_item['url'])
            
            # 按照规范格式构建数据
            news_data = {
                'title': news_item['title'],
                'url': news_item['url'],
                'content': content,
                'category': '评论',  # 评论板块
                'publish_time': self.format_time(news_item['ctime']),
                'source': 'huanqiu_opinion'  # 来源标识
            }
            
            result.append(news_data)
            
            # 避免请求过快
            if get_detail:
                time.sleep(1.5)
        
        print(f"爬取完成！共获取 {len(result)} 条新闻")
        return result


def get_news(max_news=10, get_detail=True):
    """
    获取环球网评论新闻数据 - 外部调用接口
    
    Args:
        max_news (int): 最大新闻数量，默认10
        get_detail (bool): 是否获取详细内容，默认True
        
    Returns:
        list: 新闻数据列表
    """
    crawler = HuanqiuOpinionNewsCrawler()
    return crawler.get_news(max_news, get_detail)


if __name__ == "__main__":
    # 测试爬虫
    news_data = get_news(max_news=5, get_detail=True)
    
    print(f"\n=== 测试结果 ===")
    print(f"爬取到 {len(news_data)} 条新闻")
    
    for i, news in enumerate(news_data, 1):
        print(f"\n第{i}条:")
        print(f"标题: {news['title']}")
        print(f"链接: {news['url']}")
        print(f"分类: {news['category']}")
        print(f"时间: {news['publish_time']}")
        print(f"内容长度: {len(news['content'])}字")
        print(f"来源: {news['source']}")
        if news['content']:
            print(f"内容预览: {news['content'][:100]}...")
        print("-" * 50)
