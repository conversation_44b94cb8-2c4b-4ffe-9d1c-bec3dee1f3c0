# 新闻爬虫开发规范 - 扩展爬取范围

## 📋 目标
按照现有4个新闻爬虫的格式和风格，开发新的新闻网站爬虫，直接集成到 `../新闻爬取/` 系统中。

## 🎯 参考现有爬虫
请严格按照以下现有爬虫的实现方式开发：
- `../新闻爬取/netease_news_crawler.py` - 网易新闻
- `../新闻爬取/sina_news_crawler.py` - 新浪新闻
- `../新闻爬取/ifeng_news_crawler.py` - 凤凰网
- `../新闻爬取/jiemian_news_crawler.py` - 界面新闻

## 📊 输出数据格式要求

### 必须返回的数据结构

每个爬虫必须返回一个包含新闻数据的列表，每条新闻包含以下字段：

```python
# 返回格式示例 (参考网易新闻爬虫)
[
    {
        'title': '习近平在山西考察时强调 奋力谱写三晋大地推进中国式现代化新篇章',
        'url': 'https://www.163.com/news/article/K3VKV3T5000189FH.html',
        'content': '完整的新闻正文内容...',
        'category': '要闻',
        'publish_time': '07-08 13:10',
        'source': 'netease'
    },
    # 更多新闻...
]
```

## 📝 字段说明

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| `title` | string | ✅ | 新闻标题 | "习近平在山西考察..." |
| `url` | string | ✅ | 新闻完整URL | "https://www.163.com/..." |
| `content` | string | ✅ | 新闻正文内容 | "完整的新闻内容..." |
| `category` | string | ✅ | 新闻分类 | "要闻" |
| `publish_time` | string | ✅ | 发布时间 | "07-08 13:10" |
| `source` | string | ✅ | 来源标识 | "netease" |

## 🔧 开发要求

### 1. 文件命名规范
- 文件名: `{网站名}_news_crawler.py`
- 示例: `people_news_crawler.py`, `xinhua_news_crawler.py`

### 2. 主函数规范
```python
def get_news(max_news=10, get_detail=True):
    """
    获取新闻数据

    Args:
        max_news (int): 最大新闻数量，默认10
        get_detail (bool): 是否获取详细内容，默认True

    Returns:
        list: 新闻数据列表
    """
    pass
```

### 3. 类结构规范 (可选，但推荐)
```python
class YourSiteNewsCrawler:
    def __init__(self):
        self.base_url = "https://your-site.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

    def get_news_list(self, max_news=10):
        """获取新闻列表"""
        pass

    def get_news_detail(self, url):
        """获取新闻详细内容"""
        pass

    def get_news(self, max_news=10, get_detail=True):
        """主入口函数"""
        pass
```
```

## � 现有爬虫分析

### 网易新闻爬虫 (`netease_news_crawler.py`)
```python
# 输出示例
[
    {
        'title': '一旦关注"红爷事件"，脑子就被定型了',
        'url': 'https://www.163.com/news/article/K3VLASSM000181BR.html',
        'content': '各位秋裤，你最近参与了各大外卖的"商战"没？...',
        'category': '要闻',
        'publish_time': '07-08 13:10',
        'source': 'netease'
    }
]
```

### 新浪新闻爬虫 (`sina_news_crawler.py`)
```python
# 输出示例
[
    {
        'title': '美媒感叹：让台湾干这件事，代价巨大！',
        'url': 'https://news.sina.com.cn/w/2025-07-08/doc-infesnuy7286956.shtml',
        'content': '当地时间 7 月 7 日，美国《纽约时报》刊登了一篇...',
        'category': '新闻总排行',
        'publish_time': '07-08 13:10',
        'source': 'sina'
    }
]
```

### 凤凰网爬虫 (`ifeng_news_crawler.py`)
```python
# 输出示例
[
    {
        'title': '漠河北极村出现36℃高温天气，当地民宿开始装空调',
        'url': 'https://news.ifeng.com/c/8kpPYiuZavW',
        'content': '记者 张皓 近日，北方高温引发全国关注...',
        'category': '综合',
        'publish_time': '07-08 13:10',
        'source': 'ifeng'
    }
]
```

### 界面新闻爬虫 (`jiemian_news_crawler.py`)
```python
# 输出示例
[
    {
        'title': '甲骨文将收购TikTok美国业务？字节跳动：信息不实',
        'url': 'https://www.jiemian.com/article/13003930.html',
        'content': '界面新闻记者 | 彭新 界面新闻编辑 | 宋佳楠...',
        'category': '科技',
        'publish_time': '07-08 13:10',
        'source': 'jiemian'
    }
]
```

## 🚀 集成到系统

### 1. 文件放置位置
将开发好的爬虫文件放到: `../新闻爬取/` 目录下

### 2. 修改主控制器
在 `../新闻爬取/master_pipeline.py` 中添加您的爬虫:

```python
# 在 master_pipeline.py 中添加
from your_site_news_crawler import get_news as get_your_site_news

# 在爬取函数中添加
def crawl_all_news():
    # 现有的爬虫
    netease_news = get_netease_news(max_news=10)
    sina_news = get_sina_news(max_news=10)
    ifeng_news = get_ifeng_news(max_news=10)
    jiemian_news = get_jiemian_news(max_news=10)

    # 添加您的新爬虫
    your_site_news = get_your_site_news(max_news=10)

    # 合并所有新闻
    all_news = netease_news + sina_news + ifeng_news + jiemian_news + your_site_news
    return all_news
```

## ✅ 开发检查清单

- [ ] 按照现有4个爬虫的格式开发
- [ ] 返回包含6个必填字段的字典列表
- [ ] 实现 `get_news(max_news=10, get_detail=True)` 主函数
- [ ] 处理异常情况，确保不会崩溃
- [ ] 测试爬取10条新闻数据
- [ ] 确保内容字段包含完整正文
- [ ] 时间格式统一为 "MM-DD HH:MM"

## 🔧 测试方法

```python
# 测试您的爬虫
if __name__ == "__main__":
    news_data = get_news(max_news=5, get_detail=True)

    print(f"爬取到 {len(news_data)} 条新闻")
    for i, news in enumerate(news_data):
        print(f"\n第{i+1}条:")
        print(f"标题: {news['title']}")
        print(f"链接: {news['url']}")
        print(f"分类: {news['category']}")
        print(f"时间: {news['publish_time']}")
        print(f"内容长度: {len(news['content'])}字")
        print(f"来源: {news['source']}")
```

## 📞 提交要求

开发完成后，请提供:
1. **爬虫文件**: `{网站名}_news_crawler.py`
2. **测试结果**: 至少5条新闻的完整数据
3. **网站信息**: 目标网站URL和特点说明

**🎯 目标: 扩展新闻爬取范围，增加更多优质新闻源，提升文章生成的数据基础！**

---

**文档版本**: v1.0
**最后更新**: 2025-07-08
**联系方式**: 请参考现有爬虫代码实现



---

**文档版本**: v1.0  
**最后更新**: 2025-07-08  
**维护者**: 集成新闻系统开发团队

**📞 技术支持**: 如有疑问请参考现有爬虫实现或联系开发团队
