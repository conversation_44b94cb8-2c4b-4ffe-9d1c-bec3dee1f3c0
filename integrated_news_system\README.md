# 集成新闻系统 (Integrated News System)

一个完整的新闻热点分析和文章生成系统，集成了热点爬取、话题合并、新闻检索和智能文章生成功能。

## 🚀 系统特性

### 核心功能
- **🕷️ 多平台热点爬取**: 支持微博、百度、知乎、今日头条、B站、V2EX等主流平台
- **🔄 智能话题合并**: 使用LLM技术自动识别和合并相似话题
- **🔍 向量新闻检索**: 基于向量数据库的语义新闻搜索
- **📝 智能文章生成**: 结合LLM知识和新闻要点生成深度分析文章
- **💾 数据库存储**: MySQL数据库存储文章和处理记录

### 技术架构
- **前端爬虫**: 多平台新闻热点抓取
- **话题处理**: LLM驱动的话题分析和合并
- **向量检索**: 基于embedding的新闻相似度搜索
- **文章生成**: 深度分析报告自动生成
- **数据存储**: MySQL + 向量数据库双重存储

## 📋 系统要求

### 环境依赖
- Python 3.8+
- MySQL 5.7+ 或 8.0+
- 4GB+ 内存
- 稳定的网络连接

### Python依赖包
```bash
pip install requests beautifulsoup4 feedparser numpy mysql-connector-python
```

## 🛠️ 安装配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd integrated_news_system
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境变量
复制配置文件模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下参数：

```env
# LLM API 配置 (支持多个API Key)
LLM_API_KEY_1=sk-your-api-key-1
LLM_API_KEY_2=sk-your-api-key-2
LLM_API_KEY_3=sk-your-api-key-3

# Embedding API 配置
EMBEDDING_API_KEY=your-embedding-api-key
EMBEDDING_PROJECT_ID=your-project-id
EMBEDDING_EASYLLM_ID=your-easyllm-id

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=news_system

# 邮件配置 (可选)
EMAIL_FROM_ADDR=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_TO_ADDR=<EMAIL>
```

### 4. 初始化数据库
```bash
python init_database.py
```

## 🧪 测试系统 (首次使用推荐)

### 步骤1：检查配置
```bash
# 检查所有配置是否正确
python check_config.py
```

### 步骤2：快速功能测试
```bash
# 使用模拟数据测试核心功能 (约30秒)
python quick_test.py
```

### 步骤3：完整流水线测试
```bash
# 使用少量真实数据测试完整流程 (约2-3分钟)
python test_pipeline.py
```

## 🚀 正式运行

### 方式一：交互模式 (推荐)
```bash
python quick_start.py
```

### 方式二：命令行模式
```bash
# 检查环境配置
python quick_start.py --check

# 初始化数据库
python quick_start.py --init-db

# 运行完整流水线
python quick_start.py --run

# 自定义参数运行
python quick_start.py --run --topics 10 --articles 8
```

### 方式三：直接运行主流水线
```bash
python main_pipeline.py
```

## 📊 系统流程

### 完整流水线
```
📰 热点爬取 → 🔄 话题合并 → 🔍 话题分析 → 🔍 新闻检索 → 📝 文章生成 → 💾 数据库存储
```

### 详细步骤

1. **热点爬取** (`news_crawler.py`)
   - 从多个平台抓取热门话题
   - 支持微博、百度、知乎、今日头条、B站、V2EX
   - 自动去重和数据清洗

2. **话题合并** (`topic_merger.py`)
   - 使用LLM识别相似话题
   - 智能合并和分类
   - 重要性评分

3. **话题分析** (`topic_processor.py`)
   - 提取关键词和搜索查询词
   - 分析背景信息和关键要点
   - 识别相关实体

4. **新闻检索** (`news_search.py`)
   - 基于向量数据库的语义搜索
   - 多查询词并发检索
   - 相似度排序和去重

5. **文章生成** (`article_generator.py`)
   - 结合LLM知识和新闻要点
   - 生成结构化深度分析文章
   - 自动生成标题和摘要

6. **数据存储** (`database_manager.py`)
   - MySQL存储文章和元数据
   - 完整的处理记录追踪
   - 支持状态管理

## 📁 项目结构

```
integrated_news_system/
├── config.py              # 配置管理
├── database_manager.py    # 数据库管理
├── news_crawler.py        # 新闻爬虫
├── topic_merger.py        # 话题合并
├── topic_processor.py     # 话题处理
├── news_search.py         # 新闻搜索
├── article_generator.py   # 文章生成
├── vector_database.py     # 向量数据库
├── llm_client.py          # LLM客户端
├── main_pipeline.py       # 主流水线
├── quick_start.py         # 快速启动
├── init_database.py       # 数据库初始化
├── .env.example           # 配置模板
└── README.md              # 说明文档
```

## 🔧 配置说明

### LLM配置
- 支持多个API Key轮询使用
- 自动错误处理和重试
- 并发请求支持

### 数据库配置
- MySQL存储文章和元数据
- 向量数据库存储embedding
- 自动索引和优化

### 爬虫配置
- 支持代理设置
- 请求频率控制
- 自动重试机制

## 📈 使用示例

### 基本使用
```python
from main_pipeline import IntegratedNewsPipeline

# 创建流水线实例
pipeline = IntegratedNewsPipeline()

# 运行完整流水线
result = pipeline.run_full_pipeline(
    max_topics=8,    # 最多处理8个话题
    max_articles=5   # 最多生成5篇文章
)

print(f"执行结果: {result['success']}")
print(f"生成文章: {result['final_stats']['articles_generated']} 篇")
```

### 单独使用组件
```python
# 只爬取新闻
from news_crawler import NewsCrawler
crawler = NewsCrawler()
news_data = crawler.get_all_news()

# 只合并话题
from topic_merger import TopicMerger
merger = TopicMerger()
merged_topics = merger.merge_topics(topics_data)

# 只生成文章
from article_generator import ArticleGenerator
generator = ArticleGenerator()
article = generator.generate_article(topic_analysis, news_results)
```

## 🧪 测试脚本详解

### `check_config.py` - 配置检查
- ✅ 检查 `.env` 配置文件完整性
- ✅ 验证依赖包安装状态
- ✅ 测试数据库连接
- ✅ 检查向量数据库路径
- ✅ 验证LLM API配置

### `quick_test.py` - 快速功能测试
- 🧪 使用模拟数据测试核心功能
- ⚡ 执行时间约30秒
- 📊 测试话题合并、深度分析、新闻搜索、文章生成
- 💾 验证数据库保存功能

### `test_pipeline.py` - 完整流水线测试
- 🔄 使用少量真实数据测试完整流程
- ⏱️ 执行时间约2-3分钟
- 📰 爬取少量新闻进行测试
- 🎯 限制处理数量以加快测试速度

## 🔍 监控和日志

### 执行日志
系统会自动记录详细的执行日志，包括：
- 各步骤执行时间
- 处理数据量统计
- 错误信息和重试记录
- API调用统计
- 并发处理进度

### 数据库监控
```python
from database_manager import DatabaseManager

db = DatabaseManager()
db.connect()
stats = db.get_database_stats()
print(f"文章总数: {stats['total_articles']}")
print(f"话题总数: {stats['total_topics']}")
```

## ⚠️ 注意事项

1. **API配额**: 确保LLM和Embedding API有足够的配额
2. **网络稳定**: 爬虫需要稳定的网络连接
3. **数据库权限**: 确保MySQL用户有创建数据库和表的权限
4. **存储空间**: 向量数据库需要足够的磁盘空间
5. **并发控制**: 根据API限制调整并发数量

## 🐛 故障排除

### 🔧 诊断工具

1. **配置检查**
   ```bash
   # 全面检查配置文件、依赖包、数据库连接等
   python check_config.py
   ```

2. **功能测试**
   ```bash
   # 快速测试核心功能
   python quick_test.py

   # 完整流水线测试
   python test_pipeline.py
   ```

3. **系统检查**
   ```bash
   # 检查环境配置
   python quick_start.py --check

   # 测试数据库连接
   python quick_start.py --test-db
   ```

### 常见问题

1. **配置文件错误**
   - 运行 `python check_config.py` 检查配置
   - 确保 `.env` 文件中所有必要配置项都已填写

2. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 确认数据库用户权限
   - 验证连接参数 (host, port, user, password)

3. **API调用失败**
   - 检查API Key是否正确
   - 确认API配额是否充足
   - 检查网络连接和防火墙设置

4. **向量数据库为空**
   - 首次运行时向量数据库为空是正常的
   - 系统会自动创建和填充向量数据库
   - 检查embedding API配置是否正确

5. **依赖包问题**
   - 运行 `pip install -r requirements.txt` 重新安装
   - 检查Python版本是否为3.8+

## 📞 技术支持

### 🔍 问题诊断步骤
遇到问题时，请按以下顺序进行诊断：

1. **运行配置检查**
   ```bash
   python check_config.py
   ```

2. **运行快速测试**
   ```bash
   python quick_test.py
   ```

3. **检查具体问题**
   - 配置文件是否正确
   - 依赖包是否完整安装
   - 数据库是否正常运行
   - API服务是否可用

### 💡 常见解决方案
- **首次使用**: 建议先运行测试脚本确保系统正常
- **配置问题**: 参考 `.env.example` 检查配置格式
- **网络问题**: 检查防火墙和代理设置
- **性能问题**: 根据硬件配置调整并发数量

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
