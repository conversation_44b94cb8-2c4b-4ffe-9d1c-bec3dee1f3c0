#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置检查脚本
检查系统配置是否正确
"""

import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_env_file():
    """检查.env文件"""
    print("🔍 检查配置文件...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env 文件不存在")
        return False
    
    print("✅ .env 文件存在")
    
    # 检查必要的配置项
    required_configs = [
        'LLM_API_KEYS',
        'EMBEDDING_API_KEY', 
        'DB_HOST',
        'DB_USER',
        'DB_PASSWORD',
        'DB_NAME',
        'VECTOR_DB_PATH'
    ]
    
    missing_configs = []
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        for config in required_configs:
            if config not in content or f"{config}=" not in content:
                missing_configs.append(config)
            else:
                print(f"✅ {config} 已配置")
    
    except Exception as e:
        print(f"❌ 读取.env文件失败: {e}")
        return False
    
    if missing_configs:
        print(f"❌ 缺少配置项: {', '.join(missing_configs)}")
        return False
    
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查依赖包...")
    
    required_packages = [
        'requests',
        'mysql-connector-python',
        'numpy',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n💡 安装缺失的包:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_database_connection():
    """检查数据库连接"""
    print("\n🔍 检查数据库连接...")
    
    try:
        from config_loader import ConfigLoader
        config = ConfigLoader()
        db_config = config.get_database_config()
        
        if not db_config:
            print("❌ 数据库配置加载失败")
            return False
        
        print(f"✅ 数据库配置: {db_config['user']}@{db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # 尝试连接
        import mysql.connector
        conn = mysql.connector.connect(**db_config)
        conn.close()
        print("✅ 数据库连接成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def check_vector_db():
    """检查向量数据库"""
    print("\n🔍 检查向量数据库...")
    
    try:
        from config_loader import ConfigLoader
        config = ConfigLoader()
        vector_path = config.get_vector_db_path()
        
        if not vector_path:
            print("❌ 向量数据库路径未配置")
            return False
        
        vector_dir = Path(vector_path)
        if not vector_dir.exists():
            print(f"❌ 向量数据库目录不存在: {vector_path}")
            return False
        
        # 检查是否有向量文件
        vector_files = list(vector_dir.glob("*.pkl")) + list(vector_dir.glob("*.json"))
        if not vector_files:
            print(f"⚠️ 向量数据库目录为空: {vector_path}")
            print("   提示: 首次运行时会自动创建")
        else:
            print(f"✅ 向量数据库: {len(vector_files)} 个文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 向量数据库检查失败: {e}")
        return False

def check_llm_config():
    """检查LLM配置"""
    print("\n🔍 检查LLM配置...")
    
    try:
        from config_loader import ConfigLoader
        config = ConfigLoader()
        llm_config = config.get_llm_config()
        
        if not llm_config:
            print("❌ LLM配置加载失败")
            return False
        
        api_keys = llm_config.get('api_keys', [])
        if not api_keys:
            print("❌ 没有配置LLM API Keys")
            return False
        
        print(f"✅ LLM配置: {len(api_keys)} 个API Key")
        print(f"   基础URL: {llm_config.get('base_url', 'default')}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM配置检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 集成新闻系统配置检查")
    print("="*50)
    
    checks = [
        ("配置文件", check_env_file),
        ("依赖包", check_dependencies), 
        ("数据库连接", check_database_connection),
        ("向量数据库", check_vector_db),
        ("LLM配置", check_llm_config)
    ]
    
    results = []
    
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}检查出错: {e}")
            results.append((name, False))
    
    # 总结
    print("\n" + "="*50)
    print("📊 检查结果总结:")
    print("="*50)
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有检查通过! 系统配置正确")
        print("💡 可以运行 python quick_test.py 进行功能测试")
    else:
        print("❌ 部分检查失败，请修复配置后重试")
        print("💡 参考 README.md 进行配置")

if __name__ == "__main__":
    main()
