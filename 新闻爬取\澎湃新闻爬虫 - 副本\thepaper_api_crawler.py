#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
澎湃新闻API爬虫 - 基于JSON API的高效版本
"""

import requests
import json
import time
import csv
import re
from datetime import datetime
from bs4 import BeautifulSoup

class ThepaperAPICrawler:
    def __init__(self):
        self.base_url = "https://m.thepaper.cn/"
        self.list_api_url = "https://api.thepaper.cn/contentapi/nodeCont/getByChannelId"
        self.detail_api_url = "https://m.thepaper.cn/_next/data/eiysMZXyiUe6qMsiQkGMm/detail/{}.json"
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://m.thepaper.cn/',
            'Accept': 'application/json, text/plain, */*',
            'Origin': 'https://m.thepaper.cn'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
    def get_first_page_from_html(self):
        """获取首页HTML中的初始新闻数据"""
        try:
            response = self.session.get(self.base_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            news_items = []
            
            # 查找所有新闻项
            wrappers = soup.find_all('div', class_='index_wrapper__9rz3z')
            
            for wrapper in wrappers:
                news_item = self.parse_html_news_item(wrapper)
                if news_item:
                    news_items.append(news_item)
                    
            return news_items
            
        except Exception as e:
            print(f"获取首页HTML数据失败: {e}")
            return []
    
    def parse_html_news_item(self, wrapper):
        """解析HTML中的单个新闻项"""
        try:
            news_item = {}
            
            # 获取新闻链接和ID
            title_link = wrapper.find('a', href=True)
            if title_link:
                news_item['url'] = title_link['href']
                if news_item['url'].startswith('/'):
                    news_item['url'] = self.base_url.rstrip('/') + news_item['url']
                
                # 提取新闻ID
                match = re.search(r'/newsDetail_forward_(\d+)', news_item['url'])
                if match:
                    news_item['news_id'] = match.group(1)
            
            # 获取标题
            title_elem = wrapper.find('h3', class_='index_title__aGAqD')
            if title_elem:
                news_item['title'] = title_elem.get_text().strip()
            
            # 获取图片
            img_elem = wrapper.find('img')
            if img_elem:
                news_item['image_url'] = img_elem.get('src', '')
                news_item['image_alt'] = img_elem.get('alt', '')
            
            # 获取来源
            extra_elem = wrapper.find('div', class_='index_extra__M2kfN')
            if extra_elem:
                source_link = extra_elem.find('a')
                if source_link:
                    news_item['source'] = source_link.get_text().strip()
                    news_item['source_url'] = source_link.get('href', '')
            
            # 获取发布时间和评论数
            time_spans = wrapper.find_all('span')
            for span in time_spans:
                text = span.get_text().strip()
                if '前' in text or '小时' in text or '分钟' in text:
                    news_item['pub_time'] = text
                elif '评' in text:
                    news_item['comment_count'] = text
            
            # 获取推荐标签
            label_elem = wrapper.find('span', class_='index_item_label__RwoaG')
            if label_elem and label_elem.get_text().strip():
                news_item['label'] = label_elem.get_text().strip()
            
            # 检查是否为视频
            video_elem = wrapper.find('span', class_='watermark_duration')
            if video_elem:
                duration_elem = video_elem.find('i', class_='watermark_num')
                if duration_elem:
                    news_item['video_duration'] = duration_elem.get_text().strip()
                    news_item['content_type'] = 'video'
            else:
                news_item['content_type'] = 'article'
            
            news_item['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return news_item
            
        except Exception as e:
            print(f"解析HTML新闻项失败: {e}")
            return None
    
    def get_news_list_by_api(self, page_num=2, start_time=None, exclude_ids=None, recommend_ids=None):
        """通过API获取新闻列表"""
        try:
            if start_time is None:
                start_time = int(time.time() * 1000)
            
            if exclude_ids is None:
                exclude_ids = []
            
            if recommend_ids is None:
                recommend_ids = []
            
            payload = {
                "channelId": "",  # 要闻板块为空字符串
                "excludeContIds": exclude_ids,
                "listRecommendIds": recommend_ids,
                "pageSize": 10,
                "startTime": start_time,
                "pageNum": page_num
            }
            
            print(f"API请求参数: pageNum={page_num}, excludeIds数量={len(exclude_ids)}")
            
            response = self.session.post(self.list_api_url, json=payload)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('code') == 200:
                return data.get('data', {})
            else:
                print(f"API返回错误: {data}")
                return None
                
        except Exception as e:
            print(f"获取新闻列表失败: {e}")
            return None
    
    def parse_api_news_item(self, news_data):
        """解析API返回的新闻数据"""
        try:
            news_item = {
                'news_id': news_data.get('contId'),
                'title': news_data.get('name', ''),
                'url': f"https://m.thepaper.cn/newsDetail_forward_{news_data.get('contId')}",
                'image_url': news_data.get('smallPic', ''),
                'image_alt': news_data.get('name', ''),
                'pub_time': news_data.get('pubTime', ''),
                'pub_time_new': news_data.get('pubTimeNew', ''),
                'pub_time_long': news_data.get('pubTimeLong', ''),
                'praise_times': news_data.get('praiseTimes', '0'),
                'interaction_num': news_data.get('interactionNum', ''),
                'content_type': 'video' if news_data.get('contType') == 1 else 'article',
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 获取来源信息
            node_info = news_data.get('nodeInfo', {})
            if node_info:
                news_item['source'] = node_info.get('name', '')
                news_item['source_id'] = node_info.get('nodeId', '')
                news_item['source_desc'] = node_info.get('desc', '')
                news_item['source_url'] = f"/list_{node_info.get('nodeId', '')}" if node_info.get('nodeId') else ''
            
            # 获取标签信息
            tag_list = news_data.get('tagList', [])
            if tag_list:
                news_item['tags'] = [tag.get('tag') for tag in tag_list if tag.get('tag')]
            
            return news_item
            
        except Exception as e:
            print(f"解析API新闻数据失败: {e}")
            return None
    
    def get_news_detail_by_api(self, news_id):
        """通过API获取新闻详细内容"""
        try:
            detail_url = self.detail_api_url.format(news_id)
            
            # 设置特定的headers用于详情API
            detail_headers = self.headers.copy()
            detail_headers.update({
                'Accept': 'application/json',
                'Referer': f'https://m.thepaper.cn/newsDetail_forward_{news_id}'
            })
            
            response = self.session.get(detail_url, headers=detail_headers)
            response.raise_for_status()
            
            data = response.json()
            
            # 解析详情数据
            page_props = data.get('pageProps', {})
            detail_data = page_props.get('detailData', {})
            content_detail = detail_data.get('contentDetail', {})
            
            if content_detail:
                detail_info = {
                    'content_html': content_detail.get('content', ''),
                    'summary': content_detail.get('summary', ''),
                    'author': content_detail.get('author', ''),
                    'publish_time': content_detail.get('pubTime', ''),
                    'publish_timestamp': content_detail.get('publishTime', ''),
                    'image_editor': content_detail.get('imageEditor', ''),
                    'respon_editor': content_detail.get('responEditor', ''),
                    'track_keyword': content_detail.get('trackKeyword', ''),
                    'tags': content_detail.get('tags', ''),
                }
                
                # 清理HTML内容，提取纯文本
                if detail_info['content_html']:
                    soup = BeautifulSoup(detail_info['content_html'], 'html.parser')
                    # 移除图片标签，只保留文本
                    for img in soup.find_all('img'):
                        img.decompose()
                    
                    # 提取所有段落文本
                    paragraphs = soup.find_all('p')
                    content_text = []
                    for p in paragraphs:
                        text = p.get_text().strip()
                        if text:
                            content_text.append(text)
                    
                    detail_info['content'] = '\n\n'.join(content_text)
                    detail_info['content_length'] = len(detail_info['content'])
                    detail_info['paragraph_count'] = len(content_text)
                else:
                    # 如果没有HTML内容，使用摘要
                    detail_info['content'] = detail_info['summary']
                    detail_info['content_length'] = len(detail_info['content'])
                    detail_info['paragraph_count'] = 1 if detail_info['content'] else 0
                
                # 获取语音信息
                voice_info = content_detail.get('voiceInfo', {})
                if voice_info and voice_info.get('isHaveVoice'):
                    detail_info['voice_src'] = voice_info.get('voiceSrc', '')
                    detail_info['voice_duration'] = voice_info.get('duration', '')
                
                # 获取图片信息
                images = content_detail.get('images', [])
                if images:
                    detail_info['image_count'] = len(images)
                    detail_info['images'] = [img.get('src', '') for img in images]
                
                return detail_info
            else:
                print(f"未找到详情数据: {news_id}")
                return None
                
        except Exception as e:
            print(f"获取新闻详情失败 {news_id}: {e}")
            return None
