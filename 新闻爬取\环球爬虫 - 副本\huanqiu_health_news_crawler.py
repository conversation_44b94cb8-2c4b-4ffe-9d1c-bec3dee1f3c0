#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环球网健康新闻爬虫
按照新闻爬虫开发规范实现
"""

import requests
import json
import time
import csv
from datetime import datetime
from bs4 import BeautifulSoup
from fake_useragent import UserAgent


class HuanqiuHealthNewsCrawler:
    def __init__(self):
        self.base_url = "https://health.huanqiu.com"
        self.api_url = "https://health.huanqiu.com/api/list"
        
        # 健康板块的节点ID（数量较多）
        self.nodes = [
            "/e3pmt7dq2/e3pmt7edc", "/e3pmt7dq2/e3pmt904n", "/e3pmt7dq2/e3pmt9htm",
            "/e3pmt7dq2/e3pmtbedk", "/e3pmt7dq2/e3pmtvmsa", "/e3pmt7dq2/e3pn49kc7",
            "/e3pmt7dq2/e3pn4cagl", "/e3pmt7dq2/e3pn4f81k", "/e3pmt7dq2/e3pn50ich",
            "/e3pmt7dq2/e3pn61f01", "/e3pmt7dq2/e3pn6edle", "/e3pmt7dq2/e3pn6gvs7",
            "/e3pmt7dq2/e3prd5mi2", "/e3pmt7dq2/e3ptds3rp", "/e3pmt7dq2/e3ptds3rp/e3ptds4r8",
            "/e3pmt7dq2/e3ptt66fi", "/e3pmt7dq2/f6kod14h4", "/e3pmt7dq2/f6kod3u5b",
            "/e3pmt7dq2/frev2r92c", "/e3pmt7dq2/frev2tuti", "/e3pmt7dq2/frev30n6u",
            "/e3pmt7dq2/e3pmt7jnh", "/e3pmt7dq2/e3pmt9js9", "/e3pmt7dq2/e3pmta0hk",
            "/e3pmt7dq2/e3pmta0hk/e3pmta145", "/e3pmt7dq2/e7qvvltgi"
        ]
        
        self.ua = UserAgent()
        self.session = requests.Session()
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://health.huanqiu.com/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        
        self.session.headers.update(self.headers)
    
    def get_news_list(self, max_news=None):
        """获取新闻列表"""
        news_list = []
        offset = 0
        limit = 24
        max_pages = 50
        page = 1
        
        print(f"开始获取今日健康新闻列表...")
        
        while page <= max_pages:
            if max_news and len(news_list) >= max_news:
                break
                
            params = {
                'node': ','.join([f'"{node}"' for node in self.nodes]),
                'offset': offset,
                'limit': limit
            }
            
            try:
                response = self.session.get(self.api_url, params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                articles = data.get('list', [])
                
                if not articles:
                    print(f"第{page}页没有更多文章，停止获取")
                    break
                
                # 只获取今天的文章
                today = datetime.now().date()
                today_articles = []
                non_today_count = 0
                
                for article in articles:
                    ctime = article.get('ctime', 0)
                    if ctime and str(ctime).isdigit():
                        try:
                            if len(str(ctime)) > 10:
                                timestamp = int(ctime) / 1000
                            else:
                                timestamp = int(ctime)
                            
                            article_date = datetime.fromtimestamp(timestamp).date()
                            if article_date != today:
                                non_today_count += 1
                                continue
                        except:
                            continue
                    
                    article_id = article.get('aid')
                    title = article.get('title', '')
                    
                    if article_id and title:
                        news_item = {
                            'id': article_id,
                            'title': title,
                            'url': f"https://health.huanqiu.com/article/{article_id}",
                            'summary': article.get('summary', ''),
                            'source_name': article.get('source', {}).get('name', '环球网'),
                            'ctime': ctime
                        }
                        today_articles.append(news_item)
                
                print(f"第{page}页: 共{len(articles)}篇文章，今日文章{len(today_articles)}篇")
                
                if not today_articles:
                    print("本页没有今日文章，停止获取")
                    break
                
                if non_today_count > len(today_articles) * 2 and page > 3:
                    print("非今日文章较多，可能已接近今日文章末尾")
                    news_list.extend(today_articles)
                    break
                
                news_list.extend(today_articles)
                
                if max_news and len(news_list) >= max_news:
                    news_list = news_list[:max_news]
                    break
                
                offset += limit
                page += 1
                time.sleep(1)
                
            except Exception as e:
                print(f"获取新闻列表失败: {e}")
                break
        
        print(f"共获取到 {len(news_list)} 条今日健康新闻")
        return news_list
    
    def get_news_detail(self, url):
        """获取新闻详细内容"""
        try:
            headers = {
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://health.huanqiu.com/',
                'Connection': 'keep-alive',
            }
            
            response = self.session.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            content = ""
            content_div = soup.find('div', class_='content')
            if content_div:
                article_tag = content_div.find('article')
                if article_tag:
                    paragraphs = article_tag.find_all('p')
                    content_parts = []
                    for p in paragraphs:
                        if (p.find('adv-loader') or p.find('i', class_='pic-con') or 
                            p.find('video') or p.find('div', class_='video-con')):
                            continue
                        
                        text = p.get_text(strip=True)
                        if text and len(text) > 5:
                            content_parts.append(text)
                    content = '\n\n'.join(content_parts)
            
            if not content:
                selectors = ['.la-content', '.article-content', '.content article', '.main-content', '[data-type="rtext"]']
                for selector in selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        paragraphs = content_elem.find_all(['p', 'div'])
                        content_parts = []
                        for p in paragraphs:
                            if (p.find('adv-loader') or p.find('i', class_='pic-con') or 
                                p.find('video') or p.find('div', class_='video-con')):
                                continue
                            
                            text = p.get_text(strip=True)
                            if text and len(text) > 10:
                                content_parts.append(text)
                        if content_parts:
                            content = '\n\n'.join(content_parts)
                            break
            
            return content
            
        except Exception as e:
            print(f"获取文章内容失败 {url}: {e}")
            return ""
    
    def format_time(self, timestamp):
        """格式化时间为 MM-DD HH:MM 格式"""
        try:
            if len(str(timestamp)) > 10:
                timestamp = int(timestamp) / 1000
            else:
                timestamp = int(timestamp)
            
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%m-%d %H:%M')
        except:
            return datetime.now().strftime('%m-%d %H:%M')
    
    def get_news(self, max_news=10, get_detail=True):
        """获取新闻数据 - 主入口函数"""
        print(f"开始爬取环球网健康新闻，目标数量: {max_news if max_news else '全部'}")
        
        news_list = self.get_news_list(max_news)
        
        if not news_list:
            print("未获取到新闻列表")
            return []
        
        print(f"获取到 {len(news_list)} 条新闻基本信息")
        
        result = []
        
        for i, news_item in enumerate(news_list, 1):
            print(f"正在处理第 {i} 条新闻: {news_item['title'][:30]}...")
            
            content = ""
            if get_detail:
                content = self.get_news_detail(news_item['url'])
            
            news_data = {
                'title': news_item['title'],
                'url': news_item['url'],
                'content': content,
                'category': '健康',
                'publish_time': self.format_time(news_item['ctime']),
                'source': 'huanqiu_health'
            }
            
            result.append(news_data)
            
            if get_detail:
                time.sleep(1.5)
        
        print(f"爬取完成！共获取 {len(result)} 条新闻")
        return result


def get_news(max_news=10, get_detail=True):
    """获取环球网健康新闻数据 - 外部调用接口"""
    crawler = HuanqiuHealthNewsCrawler()
    return crawler.get_news(max_news, get_detail)


if __name__ == "__main__":
    print("=== 环球网健康新闻爬虫 ===")
    print("正在爬取当天全部健康新闻...")
    
    news_data = get_news(max_news=None, get_detail=True)
    
    print(f"\n=== 爬取结果 ===")
    print(f"爬取到 {len(news_data)} 条今日健康新闻")
    
    if news_data:
        json_filename = f"huanqiu_health_news_{datetime.now().strftime('%Y%m%d')}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(news_data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {json_filename}")
        
        csv_filename = f"huanqiu_health_news_{datetime.now().strftime('%Y%m%d')}.csv"
        with open(csv_filename, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['标题', '链接', '分类', '发布时间', '来源', '内容长度', '内容'])
            for news in news_data:
                writer.writerow([
                    news['title'], news['url'], news['category'],
                    news['publish_time'], news['source'], len(news['content']), news['content']
                ])
        print(f"数据已保存到: {csv_filename}")
        
        valid_content = [news for news in news_data if news['content']]
        if valid_content:
            avg_content_len = sum(len(news['content']) for news in valid_content) / len(valid_content)
            print(f"\n=== 统计信息 ===")
            print(f"平均内容长度: {avg_content_len:.0f} 字")
            print(f"有效内容文章数: {len(valid_content)}")
        
        print(f"\n=== 前3篇文章预览 ===")
        for i, news in enumerate(news_data[:3], 1):
            print(f"\n第{i}条:")
            print(f"标题: {news['title']}")
            print(f"时间: {news['publish_time']}")
            print(f"内容长度: {len(news['content'])}字")
            if news['content']:
                print(f"内容预览: {news['content'][:100]}...")
            print("-" * 50)
    else:
        print("未获取到任何新闻数据")
