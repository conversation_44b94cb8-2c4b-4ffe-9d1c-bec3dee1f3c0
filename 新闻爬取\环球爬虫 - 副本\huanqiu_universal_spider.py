#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环球网通用新闻爬虫
支持多个板块的文章爬取，只需修改配置即可
"""

import requests
import json
import time
import csv
from datetime import datetime
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from tqdm import tqdm


# 板块配置
SECTIONS = {
    'world': {
        'name': '国际',
        'base_url': 'https://world.huanqiu.com',
        'api_url': 'https://world.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh22ph/e3pmh2398",
            "/e3pmh22ph/e3pmh26vv",
            "/e3pmh22ph/e3pn6efsl",
            "/e3pmh22ph/efp8fqe21"
        ]
    },
    'china': {
        'name': '国内',
        'base_url': 'https://china.huanqiu.com',
        'api_url': 'https://china.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh1nnq/e3pmh1obd",
            "/e3pmh1nnq/e3pn61c2g",
            "/e3pmh1nnq/e3pn6eiep",
            "/e3pmh1nnq/e3pra70uk",
            "/e3pmh1nnq/e5anm31jb",
            "/e3pmh1nnq/e7tl4e309"
        ]
    },
    'military': {
        'name': '军事',
        'base_url': 'https://mil.huanqiu.com',
        'api_url': 'https://mil.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh1dm8/e3pmt7hva",
            "/e3pmh1dm8/e3pmtdr2r",
            "/e3pmh1dm8/e3pn62l96",
            "/e3pmh1dm8/e3pn6f3oh"
        ]
    },
    'taiwan': {
        'name': '台海',
        'base_url': 'https://taiwan.huanqiu.com',
        'api_url': 'https://taiwan.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh1fmg/e3pmh1g6o",
            "/e3pmh1fmg/e3pmt8gfv",
            "/e3pmh1fmg/e3pmt8i3n",
            "/e3pmh1fmg/e3pmt8lic",
            "/e3pmh1fmg/e3pmunk13",
            "/e3pmh1fmg/e3pn6elbj"
        ]
    },
    'opinion': {
        'name': '评论',
        'base_url': 'https://opinion.huanqiu.com',
        'api_url': 'https://opinion.huanqiu.com/api/list',
        'nodes': [
            "/e3pmub6h5/e3pmub75a", "/e3pmub6h5/e3pn00if8", "/e3pmub6h5/e3pn03vit",
            "/e3pmub6h5/e3pn4bi4t", "/e3pmub6h5/e3pr9baf6", "/e3pmub6h5/e3prafm0g",
            "/e3pmub6h5/e3prcgifj", "/e3pmub6h5/e81curi71", "/e3pmub6h5/e81cv14rf",
            "/e3pmub6h5/e81cv14rf/e81cv52ha", "/e3pmub6h5/e81cv14rf/e81cvaa3q",
            "/e3pmub6h5/e81cv14rf/e81cvcd7e", "/e3pmub6h5/e81cv14rf/fiqa8nr3d",
            "/e3pmub6h5/e81cv14rf/fkmm2pfjb", "/e3pmub6h5/eo1dckao0",
            "/e3pmub6h5/f1nptsfsh", "/e3pmub6h5/fdark2mrt", "/e3pmub6h5/e81cvuabi"
        ]
    },
    'finance': {
        'name': '财经',
        'base_url': 'https://finance.huanqiu.com',
        'api_url': 'https://finance.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh1hmp/e3pmh28kq", "/e3pmh1hmp/e3pn61chp", "/e3pmh1hmp/e3ptkencb",
            "/e3pmh1hmp/e3pn47c56", "/e3pmh1hmp/e3pn61an9", "/e3pmh1hmp/e7i6qafud",
            "/e3pmh1hmp/e3pn47igg", "/e3pmh1hmp/e3pmh1iab", "/e3pmh1hmp/e3pn61831",
            "/e3pmh1hmp/e3pn62ihu", "/e3pmh1hmp/e3pmh2bsv", "/e3pmh1hmp/e3pmh2bsv/e3pn603rc",
            "/e3pmh1hmp/e3pn61fkq", "/e3pmh1hmp/fu13fv4i5"
        ]
    },
    'tech': {
        'name': '科技',
        'base_url': 'https://tech.huanqiu.com',
        'api_url': 'https://tech.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh164r/e3pmh33i9", "/e3pmh164r/e3pmtm015", "/e3pmh164r/e3pn60k1f",
            "/e3pmh164r/e3pmh3dh4", "/e3pmh164r/e3pn46ot6", "/e3pmh164r/e3pmtmdvg",
            "/e3pmh164r/e3pmh2hq8", "/e3pmh164r/e3pn4sfhb", "/e3pmh164r/e3pmtod3t",
            "/e3pmh164r/e3pn4gh77", "/e3pmh164r/e3pmtlao3"
        ]
    },
    'auto': {
        'name': '汽车',
        'base_url': 'https://auto.huanqiu.com',
        'api_url': 'https://auto.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh24qk/e3pmh25cs", "/e3pmh24qk/e3pmh3bo0", "/e3pmh24qk/e3pss28ab",
            "/e3pmh24qk/e3pmtj57c", "/e3pmh24qk/e3pmtju0i", "/e3pmh24qk/e3pn4eqdt",
            "/e3pmh24qk/e3pn4el6u", "/e3pmh24qk/e3pn02mp3", "/e3pmh24qk/e3pn0asn6",
            "/e3pmh24qk/e3pmtkgc2", "/e3pmh24qk/e3pofth7r", "/e3pmh24qk/e3prcrrt0",
            "/e3pmh24qk/e3pmtj0sq", "/e3pmh24qk/e3pmtj3a3", "/e3pmh24qk/e3pn4ens6",
            "/e3pmh24qk/e3pn6fl79"
        ]
    },
    'capital': {
        'name': '产业',
        'base_url': 'https://capital.huanqiu.com',
        'api_url': 'https://capital.huanqiu.com/api/list',
        'nodes': [
            "/e5d59phvs/e5d5m10mv", "/e5d59phvs/e63j24g2m", "/e5d59phvs/evq0lg1h3",
            "/e5d59phvs/evq0lkmga", "/e5d59phvs/evq1mo2qf", "/e5d59phvs/evq1mo2qf/evq1msmdn",
            "/e5d59phvs/evq1mv740", "/e5d59phvs/f29f7sbm1"
        ]
    },
    'health': {
        'name': '健康',
        'base_url': 'https://health.huanqiu.com',
        'api_url': 'https://health.huanqiu.com/api/list',
        'nodes': [
            "/e3pmt7dq2/e3pmt7edc", "/e3pmt7dq2/e3pmt904n", "/e3pmt7dq2/e3pmt9htm",
            "/e3pmt7dq2/e3pmtbedk", "/e3pmt7dq2/e3pmtvmsa", "/e3pmt7dq2/e3pn49kc7",
            "/e3pmt7dq2/e3pn4cagl", "/e3pmt7dq2/e3pn4f81k", "/e3pmt7dq2/e3pn50ich",
            "/e3pmt7dq2/e3pn61f01", "/e3pmt7dq2/e3pn6edle", "/e3pmt7dq2/e3pn6gvs7",
            "/e3pmt7dq2/e3prd5mi2", "/e3pmt7dq2/e3ptds3rp", "/e3pmt7dq2/e3ptds3rp/e3ptds4r8",
            "/e3pmt7dq2/e3ptt66fi", "/e3pmt7dq2/f6kod14h4", "/e3pmt7dq2/f6kod3u5b",
            "/e3pmt7dq2/frev2r92c", "/e3pmt7dq2/frev2tuti", "/e3pmt7dq2/frev30n6u",
            "/e3pmt7dq2/e3pmt7jnh", "/e3pmt7dq2/e3pmt9js9", "/e3pmt7dq2/e3pmta0hk",
            "/e3pmt7dq2/e3pmta0hk/e3pmta145", "/e3pmt7dq2/e7qvvltgi"
        ]
    },
    'agriculture': {
        'name': '农业',
        'base_url': 'https://xy.huanqiu.com',
        'api_url': 'https://xy.huanqiu.com/api/list',
        'nodes': [
            "/em2d4g1am/ff5n85c7h", "/em2d4g1am/em2l29ib2", "/em2d4g1am/ff5n8uatb",
            "/em2d4g1am/ff5n8doit", "/em2d4g1am/ff5n8b0qt", "/em2d4g1am/ff5n88env",
            "/em2d4g1am/ff5n90m26", "/em2d4g1am/ff5n9cpuf"
        ]
    },
    'uav': {
        'name': '低空',
        'base_url': 'https://uav.huanqiu.com',
        'api_url': 'https://uav.huanqiu.com/api/list',
        'nodes': [
            "/e3pn5tffs/e3pn5tg0p", "/e3pn5tffs/e3pn5tg0p/e3ptjsfnn", "/e3pn5tffs/e3pn6156o",
            "/e3pn5tffs/e3poit9nf", "/e3pn5tffs/e3poit9nf/e3ptjtt1d", "/e3pn5tffs/e3pshm06b",
            "/e3pn5tffs/e3pthq9rg", "/e3pn5tffs/e3ptkjts8", "/e3pn5tffs/e5ar1t85q",
            "/e3pn5tffs/e7rugtglo", "/e3pn5tffs/e7ruia4rh", "/e3pn5tffs/e7siujt5c"
        ]
    },
    'quality': {
        'name': '消费',
        'base_url': 'https://quality.huanqiu.com',
        'api_url': 'https://quality.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh1m0l/fq194bivt", "/e3pmh1m0l/fq194dv4m", "/e3pmh1m0l/fq194gd9b",
            "/e3pmh1m0l/fq194imit", "/e3pmh1m0l/fq194lq1n", "/e3pmh1m0l/fq194pa97",
            "/e3pmh1m0l/fq194rklq", "/e3pmh1m0l/fq194ueft", "/e3pmh1m0l/fq1950ves",
            "/e3pmh1m0l/fqbffjhiv", "/e3pmh1m0l/fqbffga3j"
        ]
    },
    'fashion': {
        'name': '时尚',
        'base_url': 'https://fashion.huanqiu.com',
        'api_url': 'https://fashion.huanqiu.com/api/list',
        'nodes': [
            "/e3pn4vu2g/e3pn4vuih", "/e3pn4vu2g/e3pn61569", "/e3pn4vu2g/e3pn7fph9",
            "/e3pn4vu2g/e3pn7v7f6", "/e3pn4vu2g/e3poftkqs", "/e3pn4vu2g/e3pright6",
            "/e3pn4vu2g/e3prijklc", "/e3pn4vu2g/e3ptq4t9b"
        ]
    },
    'entertainment': {
        'name': '大文娱',
        'base_url': 'https://ent.huanqiu.com',
        'api_url': 'https://ent.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh1jtb/e3pmth0vk", "/e3pmh1jtb/fs9nk39gk", "/e3pmh1jtb/fs9nk6km6",
            "/e3pmh1jtb/fs9ktntqt", "/e3pmh1jtb/fs9kttq3t", "/e3pmh1jtb/fs9ku0sl3",
            "/e3pmh1jtb/fs9ku3uhr", "/e3pmh1jtb/e3pn46tmo", "/e3pmh1jtb/fs9ku70tj"
        ]
    },
    'sports': {
        'name': '体育',
        'base_url': 'https://sports.huanqiu.com',
        'api_url': 'https://sports.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh3jvm/e3pn4vk37", "/e3pmh3jvm/e3pn4vk37/e3pn61aah", "/e3pmh3jvm/e3pn4vk37/e3pn62b3q",
            "/e3pmh3jvm/e3pn4vk37/e3pn638jv", "/e3pmh3jvm/e3pn4vk37/e3pn669vr", "/e3pmh3jvm/e3pn4vk37/e82e6tcpo",
            "/e3pmh3jvm/e3pn61psg", "/e3pmh3jvm/e3pn61psg/e3pn61qfv", "/e3pmh3jvm/e3pn61psg/e7tn9k8oi",
            "/e3pmh3jvm/e3pn61psg/e7tn9o6uo", "/e3pmh3jvm/e3pn61psg/e7tn9rf8b", "/e3pmh3jvm/e3pn61psg/e7tna015g",
            "/e3pmh3jvm/e3pn62e6c", "/e3pmh3jvm/e3pn62e6c/e3pn62euk", "/e3pmh3jvm/e3pn62e6c/e3prbvcgu",
            "/e3pmh3jvm/e3pn62e6c/e82e138l9", "/e3pmh3jvm/e3pn7fhub", "/e3pmh3jvm/e3pn7fhub/e3pn7fif4",
            "/e3pmh3jvm/e80lb2feu"
        ]
    },
    'media': {
        'name': '融媒',
        'base_url': 'https://media.huanqiu.com',
        'api_url': 'https://media.huanqiu.com/api/list',
        'nodes': [
            "/eo2uaijn2/eo2uap7fa", "/eo2uaijn2/eo2uatkg8", "/eo2uaijn2/eo2ub1ndd",
            "/eo2uaijn2/eo2ube74k", "/eo2uaijn2/eo2ubl5h5", "/eo2uaijn2/eo2ubp0sa",
            "/eo2uaijn2/eo2ubtb1u", "/eo2uaijn2/eo2uc0em6", "/eo2uaijn2/eo2uc366i",
            "/eo2uaijn2/eo2uc5tua"
        ]
    },
    'education': {
        'name': '教育',
        'base_url': 'https://lx.huanqiu.com',
        'api_url': 'https://lx.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh20mi/e3pn60gah", "/e3pmh20mi/e3pt1kh3i", "/e3pmh20mi/e7ru9r4mj",
            "/e3pmh20mi/ectnin83p", "/e3pmh20mi/ectnissc9", "/e3pmh20mi/ectnj650m",
            "/e3pmh20mi/ectnjgbot"
        ]
    },
    'hope': {
        'name': '公益',
        'base_url': 'https://hope.huanqiu.com',
        'api_url': 'https://hope.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh4858/e3pttusns", "/e3pmh4858/e3ptucljl", "/e3pmh4858/e7o7pkup4",
            "/e3pmh4858/e7o8kso2r", "/e3pmh4858/ekr3m9a4m", "/e3pmh4858/ekr3metri",
            "/e3pmh4858/ekr3mia18", "/e3pmh4858/ekr3mllli", "/e3pmh4858/ekr3mobku",
            "/e3pmh4858/eqngbhuat"
        ]
    },
    'society': {
        'name': '社会',
        'base_url': 'https://society.huanqiu.com',
        'api_url': 'https://society.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh19vt/e3pmh1ar3", "/e3pmh19vt/e3pn7fivc", "/e3pmh19vt/e3prv5gfn",
            "/e3pmh19vt/e3ps21dgq", "/e3pmh19vt/e3ps2ueac", "/e3pmh19vt/e3ps46nfp"
        ]
    },
    'digital': {
        'name': '数码',
        'base_url': 'https://tech.huanqiu.com',
        'api_url': 'https://tech.huanqiu.com/api/list2',  # 注意：使用list2接口
        'nodes': ["/e3pmh164r/e3pmh3dh4"],  # 单个节点，不需要引号包装
        'special_api': True  # 标记特殊API
    },
    'game': {
        'name': '互动娱乐',
        'base_url': 'https://game.huanqiu.com',
        'api_url': 'https://game.huanqiu.com/api/list',
        'nodes': [
            "/e3pn52oi7/e3pn52p27", "/e3pn52oi7/e3pn60vdp", "/e3pn52oi7/e3pn617tp",
            "/e3pn52oi7/e3pn61n4j", "/e3pn52oi7/e3pn6254l", "/e3pn52oi7/e3pofunie"
        ]
    },
    'safety': {
        'name': '消防',
        'base_url': 'https://anquan.huanqiu.com',
        'api_url': 'https://anquan.huanqiu.com/api/list',
        'nodes': [
            "/e3pn61f78/e3pu0t2j5", "/e3pn61f78/e3ptteg0n", "/e3pn61f78/e5arssef1",
            "/e3pn61f78/fa7vnbmeu", "/e3pn61f78/fa7vndrc3", "/e3pn61f78/fa7vng6li",
            "/e3pn61f78/fa7vnio9f", "/e3pn61f78/fa7vnlbfp", "/e3pn61f78/fa7vnnhr3",
            "/e3pn61f78/fa7vnq561", "/e3pn61f78/fackfgm5b", "/e3pn61f78/fao3f7s2q",
            "/e3pn61f78/fao3faqde"
        ]
    },
    'business': {
        'name': '商业',
        'base_url': 'https://biz.huanqiu.com',
        'api_url': 'https://biz.huanqiu.com/api/list',
        'nodes': [
            "/e3pmh1rv3/e3pmh1sfd", "/e3pmh1rv3/e3pmh2e3h", "/e3pmh1rv3/e3pn60lt0",
            "/e3pmh1rv3/e3pn60rqk", "/e3pmh1rv3/e3pn60urv", "/e3pmh1rv3/e3pn614r6",
            "/e3pmh1rv3/e3pn61ned", "/e3pmh1rv3/e3pn62hv9", "/e3pmh1rv3/e3pn63m00",
            "/e3pmh1rv3/e3pok2bl1", "/e3pmh1rv3/e3pu1lrsc", "/e3pmh1rv3/e3pu1okro",
            "/e3pmh1rv3/e6a3d64vd", "/e3pmh1rv3/e6a3d6rr6", "/e3pmh1rv3/e7nopb37v",
            "/e3pmh1rv3/e7qcj6l0g"
        ]
    },
    'oversea': {
        'name': '海外',
        'base_url': 'https://oversea.huanqiu.com',
        'api_url': 'https://oversea.huanqiu.com/api/list',
        'nodes': [
            "/e3pmt7bdh/e3pn4bc7q", "/e3pmt7bdh/e3pn5250c", "/e3pmt7bdh/e3pn6g8t8"
        ]
    },
    'culture': {
        'name': '文化',
        'base_url': 'https://cul.huanqiu.com',
        'api_url': 'https://cul.huanqiu.com/api/list',
        'nodes': [
            "/e3pn677q4/e3ptvpbdi", "/e3pn677q4/e7n7nshou", "/e3pn677q4/e7n7nshou/e7n7o5sgv",
            "/e3pn677q4/e7n7nshou/e7n7oddi8", "/e3pn677q4/e7n7nshou/e7n7oj8js", "/e3pn677q4/e7n7nshou/e7n7opklk",
            "/e3pn677q4/e7n7nshou/e80schtc2", "/e3pn677q4/e7n7qip93", "/e3pn677q4/e7n7qip93/e7n7quqav",
            "/e3pn677q4/e7n7qip93/e7n7r604h", "/e3pn677q4/e7n7qip93/e7n7rlouf", "/e3pn677q4/e7n7s45fv",
            "/e3pn677q4/e7n7s45fv/e7n7safa1", "/e3pn677q4/e7n7s45fv/e7n7silkh", "/e3pn677q4/e7n7s45fv/e7n7sqsjs",
            "/e3pn677q4/e7n836lt3", "/e3pn677q4/e7n83ff31", "/e3pn677q4/e7n83ff31/e7n83ll7b",
            "/e3pn677q4/e7n83ff31/e7n83scbg", "/e3pn677q4/e7n83ff31/e7n8421o7", "/e3pn677q4/e7n84b0u1",
            "/e3pn677q4/e7n84le0h", "/e3pn677q4/e7n856422"
        ]
    }
}


class HuanqiuUniversalSpider:
    def __init__(self, section='world'):
        """
        初始化爬虫
        :param section: 板块名称，可选: world, china, military, taiwan
        """
        if section not in SECTIONS:
            raise ValueError(f"不支持的板块: {section}，支持的板块: {list(SECTIONS.keys())}")
        
        self.section = section
        self.config = SECTIONS[section]
        
        self.session = requests.Session()
        self.ua = UserAgent()
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': f"{self.config['base_url']}/",
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        
        self.session.headers.update(self.headers)
        
        # 存储数据
        self.articles = []
        
    def get_article_list(self, offset=0, limit=24):
        """
        获取文章列表
        """
        # 检查是否为特殊API（如数码板块）
        if self.config.get('special_api'):
            # 数码板块使用不同的参数格式
            params = {
                'node': self.config['nodes'][0],  # 不需要引号包装
                'offset': offset,
                'limit': limit
            }
        else:
            # 标准API格式
            params = {
                'node': ','.join([f'"{node}"' for node in self.config['nodes']]),
                'offset': offset,
                'limit': limit
            }

        try:
            response = self.session.get(self.config['api_url'], params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            return data.get('list', [])

        except requests.RequestException as e:
            print(f"获取文章列表失败: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return []
    
    def get_article_content(self, article_id):
        """
        获取文章详细内容
        """
        article_url = f"{self.config['base_url']}/article/{article_id}"
        
        try:
            # 随机更换User-Agent
            headers = {
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': f"{self.config['base_url']}/",
                'Connection': 'keep-alive',
            }
            
            response = self.session.get(article_url, headers=headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取文章内容 - 根据HTML结构
            content = ""
            content_div = soup.find('div', class_='content')
            if content_div:
                article_tag = content_div.find('article')
                if article_tag:
                    # 提取所有段落，过滤掉广告等元素
                    paragraphs = article_tag.find_all('p')
                    content_parts = []
                    for p in paragraphs:
                        # 跳过包含广告的段落
                        if p.find('adv-loader'):
                            continue
                        # 跳过图片说明文字
                        if p.find('i', class_='pic-con'):
                            continue
                        # 跳过视频相关内容
                        if p.find('video') or p.find('div', class_='video-con'):
                            continue
                        
                        text = p.get_text(strip=True)
                        if text and len(text) > 5:  # 过滤掉太短的文本
                            content_parts.append(text)
                    content = '\n\n'.join(content_parts)
            
            # 如果上面的方法没找到内容，尝试其他选择器
            if not content:
                # 尝试其他可能的内容选择器
                selectors = [
                    '.la-content',
                    '.article-content', 
                    '.content article',
                    '.main-content',
                    '[data-type="rtext"]'
                ]
                
                for selector in selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        paragraphs = content_elem.find_all(['p', 'div'])
                        content_parts = []
                        for p in paragraphs:
                            # 跳过广告相关内容
                            if (p.find('adv-loader') or p.find('i', class_='pic-con') or 
                                p.find('video') or p.find('div', class_='video-con')):
                                continue
                            
                            text = p.get_text(strip=True)
                            if text and len(text) > 10:
                                content_parts.append(text)
                        if content_parts:
                            content = '\n\n'.join(content_parts)
                            break
            
            # 提取发布时间
            publish_time = ""
            time_selectors = [
                '.time',
                'time',
                '.publish-time',
                '.article-time'
            ]
            
            for selector in time_selectors:
                time_elem = soup.select_one(selector)
                if time_elem:
                    publish_time = time_elem.get_text(strip=True)
                    break
            
            # 提取作者/来源
            source = ""
            source_selectors = [
                '.source',
                '.article-source',
                '.author'
            ]
            
            for selector in source_selectors:
                source_elem = soup.select_one(selector)
                if source_elem:
                    source = source_elem.get_text(strip=True)
                    break
            
            return {
                'content': content,
                'publish_time': publish_time,
                'source': source,
                'url': article_url
            }
            
        except requests.RequestException as e:
            print(f"获取文章内容失败 {article_id}: {e}")
            return None
        except Exception as e:
            print(f"解析文章内容失败 {article_id}: {e}")
            return None
    
    def is_today_article(self, ctime):
        """
        判断文章是否为今天发布
        """
        if not ctime or not str(ctime).isdigit():
            return False
        
        try:
            # 时间戳可能是毫秒或秒，先尝试毫秒
            if len(str(ctime)) > 10:
                timestamp = int(ctime) / 1000
            else:
                timestamp = int(ctime)
            
            article_date = datetime.fromtimestamp(timestamp).date()
            today = datetime.now().date()
            
            return article_date == today
        except:
            return False
    
    def get_news_list(self, max_news=10):
        """
        获取新闻列表

        Args:
            max_news (int): 最大新闻数量

        Returns:
            list: 新闻基本信息列表
        """
        print(f"开始获取{self.config['name']}板块的今日新闻列表...")

        news_list = []
        offset = 0
        limit = 24
        max_pages = 20
        page = 1

        while page <= max_pages and len(news_list) < max_news:
            print(f"正在获取第 {page} 页文章列表 (offset: {offset})...")

            # 获取文章列表
            article_list = self.get_article_list(offset, limit)

            if not article_list:
                print("没有更多文章了")
                break

            # 只获取今天的文章
            today = datetime.now().date()
            today_count = 0

            for article in article_list:
                if len(news_list) >= max_news:
                    break

                # 检查是否为今日文章
                ctime = article.get('ctime', 0)
                if ctime and str(ctime).isdigit():
                    try:
                        if len(str(ctime)) > 10:
                            timestamp = int(ctime) / 1000
                        else:
                            timestamp = int(ctime)

                        article_date = datetime.fromtimestamp(timestamp).date()
                        if article_date != today:
                            continue
                    except:
                        continue

                article_id = article.get('aid')
                title = article.get('title', '')

                if article_id and title:
                    news_item = {
                        'id': article_id,
                        'title': title,
                        'url': f"{self.config['base_url']}/article/{article_id}",
                        'summary': article.get('summary', ''),
                        'source_name': article.get('source', {}).get('name', '环球网'),
                        'ctime': ctime
                    }
                    news_list.append(news_item)
                    today_count += 1

            print(f"第 {page} 页: 共 {len(article_list)} 篇文章，今日文章 {today_count} 篇")

            if today_count == 0:
                print("本页没有今日文章，停止获取")
                break

            offset += limit
            page += 1
            time.sleep(1)

        print(f"共获取到 {len(news_list)} 条今日新闻")
        return news_list[:max_news]

    def get_news_detail(self, url):
        """
        获取新闻详细内容

        Args:
            url (str): 新闻URL

        Returns:
            str: 新闻正文内容
        """
        try:
            # 随机更换User-Agent
            headers = {
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': f"{self.config['base_url']}/",
                'Connection': 'keep-alive',
            }

            response = self.session.get(url, headers=headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取文章内容
            content = ""
            content_div = soup.find('div', class_='content')
            if content_div:
                article_tag = content_div.find('article')
                if article_tag:
                    # 提取所有段落，过滤掉广告等元素
                    paragraphs = article_tag.find_all('p')
                    content_parts = []
                    for p in paragraphs:
                        # 跳过包含广告的段落
                        if (p.find('adv-loader') or p.find('i', class_='pic-con') or
                            p.find('video') or p.find('div', class_='video-con')):
                            continue

                        text = p.get_text(strip=True)
                        if text and len(text) > 5:  # 过滤掉太短的文本
                            content_parts.append(text)
                    content = '\n\n'.join(content_parts)

            # 如果上面的方法没找到内容，尝试其他选择器
            if not content:
                selectors = [
                    '.la-content',
                    '.article-content',
                    '.content article',
                    '.main-content',
                    '[data-type="rtext"]'
                ]

                for selector in selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        paragraphs = content_elem.find_all(['p', 'div'])
                        content_parts = []
                        for p in paragraphs:
                            # 跳过广告相关内容
                            if (p.find('adv-loader') or p.find('i', class_='pic-con') or
                                p.find('video') or p.find('div', class_='video-con')):
                                continue

                            text = p.get_text(strip=True)
                            if text and len(text) > 10:
                                content_parts.append(text)
                        if content_parts:
                            content = '\n\n'.join(content_parts)
                            break

            return content

        except Exception as e:
            print(f"获取文章内容失败 {url}: {e}")
            return ""
    
    def format_time(self, timestamp):
        """
        格式化时间为 MM-DD HH:MM 格式

        Args:
            timestamp: 时间戳

        Returns:
            str: 格式化后的时间字符串
        """
        try:
            if len(str(timestamp)) > 10:
                timestamp = int(timestamp) / 1000
            else:
                timestamp = int(timestamp)

            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%m-%d %H:%M')
        except:
            return datetime.now().strftime('%m-%d %H:%M')

    def get_news(self, max_news=10, get_detail=True):
        """
        获取新闻数据 - 主入口函数

        Args:
            max_news (int): 最大新闻数量，默认10。如果设置为较大值(如200)表示获取当天全部新闻
            get_detail (bool): 是否获取详细内容，默认True

        Returns:
            list: 新闻数据列表，符合规范格式
        """
        if max_news >= 100:
            print(f"开始爬取环球网{self.config['name']}板块的当天全部新闻...")
        else:
            print(f"开始爬取环球网{self.config['name']}新闻，目标数量: {max_news}")

        # 获取新闻列表
        news_list = self.get_news_list(max_news)

        if not news_list:
            print("未获取到新闻列表")
            return []

        print(f"获取到 {len(news_list)} 条今日新闻基本信息")

        if get_detail:
            print("开始获取详细内容...")
        else:
            print("跳过详细内容获取")

        # 构建返回数据
        result = []

        for i, news_item in enumerate(news_list, 1):
            print(f"正在处理第 {i}/{len(news_list)} 条新闻: {news_item['title'][:40]}...")

            # 获取详细内容
            content = ""
            if get_detail:
                print(f"  正在获取详细内容...")
                content = self.get_news_detail(news_item['url'])
                if content:
                    print(f"  ✅ 获取成功，内容长度: {len(content)} 字符")
                else:
                    print(f"  ❌ 获取失败或内容为空")

            # 按照规范格式构建数据
            news_data = {
                'title': news_item['title'],
                'url': news_item['url'],
                'content': content,
                'category': self.config['name'],  # 使用板块名称作为分类
                'publish_time': self.format_time(news_item['ctime']),
                'source': f'huanqiu_{self.section}'  # 来源标识
            }

            result.append(news_data)

            # 避免请求过快
            if get_detail:
                print(f"  等待 1.5 秒...")
                time.sleep(1.5)

        print(f"爬取完成！共获取 {len(result)} 条新闻")
        return result


# 外部调用接口 - 符合规范要求
def get_news(max_news=10, get_detail=True, section='world'):
    """
    获取环球网新闻数据 - 外部调用接口

    Args:
        max_news (int): 最大新闻数量，默认10
        get_detail (bool): 是否获取详细内容，默认True
        section (str): 板块名称，默认'world'

    Returns:
        list: 新闻数据列表，符合规范格式
    """
    crawler = HuanqiuUniversalSpider(section)
    return crawler.get_news(max_news, get_detail)


def get_all_sections_news(max_news_per_section=None, get_detail=True):
    """
    获取所有板块的新闻数据

    Args:
        max_news_per_section (int): 每个板块的最大新闻数量，None表示获取当天全部新闻
        get_detail (bool): 是否获取详细内容，默认True

    Returns:
        list: 所有板块的新闻数据列表
    """
    print("=== 开始爬取所有板块的当天全部新闻 ===")
    if max_news_per_section:
        print(f"共 {len(SECTIONS)} 个板块，每个板块最多 {max_news_per_section} 条新闻")
    else:
        print(f"共 {len(SECTIONS)} 个板块，获取每个板块的当天全部新闻")

    all_news = []
    total_sections = len(SECTIONS)

    for i, (section_key, section_config) in enumerate(SECTIONS.items(), 1):
        print(f"\n[{i}/{total_sections}] 正在爬取 {section_key} ({section_config['name']}) 板块...")

        try:
            # 如果没有指定数量限制，则获取当天全部新闻（设置一个较大的数值）
            max_news = max_news_per_section if max_news_per_section else 200

            section_news = get_news(
                max_news=max_news,
                get_detail=get_detail,
                section=section_key
            )

            if section_news:
                all_news.extend(section_news)
                print(f"✅ {section_key} 板块完成，获取到 {len(section_news)} 条新闻")
            else:
                print(f"⚠️  {section_key} 板块无今日新闻")

        except Exception as e:
            print(f"❌ {section_key} 板块爬取失败: {e}")

        # 板块间间隔，避免请求过快
        if i < total_sections:
            print("等待 3 秒...")
            time.sleep(3)

    print(f"\n=== 所有板块爬取完成 ===")
    print(f"总共获取到 {len(all_news)} 条新闻")

    return all_news


def main():
    """
    主函数 - 默认爬取所有板块的当天全部新闻
    """
    print("=== 环球网通用新闻爬虫 ===")
    print("默认模式：爬取所有板块的当天全部新闻")
    print(f"共 {len(SECTIONS)} 个板块:")
    for key, config in SECTIONS.items():
        print(f"  {key}: {config['name']}")

    print(f"\n开始爬取所有板块的当天全部新闻...")

    # 爬取所有板块的当天全部新闻（不限制数量）
    news_data = get_all_sections_news(max_news_per_section=None, get_detail=True)

    print(f"\n=== 爬取结果 ===")
    print(f"爬取到 {len(news_data)} 条新闻")

    if news_data:
        # 保存数据到文件
        import csv

        # 保存JSON格式 - 所有板块
        json_filename = f"huanqiu_all_sections_news_{datetime.now().strftime('%Y%m%d_%H%M')}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(news_data, f, ensure_ascii=False, indent=2)
        print(f"\n数据已保存到: {json_filename}")

        # 保存CSV格式 - 所有板块
        csv_filename = f"huanqiu_all_sections_news_{datetime.now().strftime('%Y%m%d_%H%M')}.csv"
        with open(csv_filename, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['标题', '链接', '分类', '发布时间', '来源', '内容长度', '内容'])
            for news in news_data:
                writer.writerow([
                    news['title'],
                    news['url'],
                    news['category'],
                    news['publish_time'],
                    news['source'],
                    len(news['content']),
                    news['content']
                ])
        print(f"数据已保存到: {csv_filename}")

        # 显示统计信息
        valid_content = [news for news in news_data if news['content']]
        if valid_content:
            avg_content_len = sum(len(news['content']) for news in valid_content) / len(valid_content)
            print(f"\n=== 统计信息 ===")
            print(f"总新闻数量: {len(news_data)}")
            print(f"平均内容长度: {avg_content_len:.0f} 字")
            print(f"有效内容文章数: {len(valid_content)}")

            # 按板块统计
            section_stats = {}
            for news in news_data:
                category = news['category']
                if category not in section_stats:
                    section_stats[category] = 0
                section_stats[category] += 1

            print(f"\n各板块新闻数量:")
            for category, count in sorted(section_stats.items()):
                print(f"  {category}: {count} 条")

        # 显示前5篇文章预览 - 按照规范格式
        print(f"\n=== 前5篇文章预览 ===")
        for i, news in enumerate(news_data[:5], 1):
            print(f"\n第{i}条:")
            print(f"标题: {news['title']}")
            print(f"链接: {news['url']}")
            print(f"分类: {news['category']}")
            print(f"时间: {news['publish_time']}")
            print(f"内容长度: {len(news['content'])}字")
            print(f"来源: {news['source']}")
            if news['content']:
                print(f"内容预览: {news['content'][:100]}...")
            print("-" * 50)
    else:
        print("未获取到任何新闻数据")


if __name__ == "__main__":
    main()
