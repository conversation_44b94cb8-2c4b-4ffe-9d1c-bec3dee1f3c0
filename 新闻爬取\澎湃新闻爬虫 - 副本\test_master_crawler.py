#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
澎湃新闻主控制器测试脚本
"""

from thepaper_master_crawler import get_news, ThepaperMasterCrawler
import json

def test_single_channel():
    """测试单个板块爬取"""
    print("=== 测试单个板块爬取 ===")
    
    crawler = ThepaperMasterCrawler()
    
    # 测试要闻板块
    try:
        news = crawler.get_news(max_news=5, get_detail=False, channels=['yawen'], parallel=False)
        print(f"要闻板块测试: 获取到 {len(news)} 条新闻")
        
        if news:
            print("第一条新闻:")
            print(f"  标题: {news[0]['title']}")
            print(f"  分类: {news[0]['category']}")
            print(f"  时间: {news[0]['publish_time']}")
            print(f"  来源: {news[0]['source']}")
            
    except Exception as e:
        print(f"单板块测试失败: {e}")

def test_multiple_channels():
    """测试多板块爬取"""
    print("\n=== 测试多板块爬取 ===")
    
    try:
        # 测试3个板块，每个板块3条新闻
        news = get_news(max_news=9, get_detail=False)
        print(f"多板块测试: 获取到 {len(news)} 条新闻")
        
        # 统计各板块数量
        category_count = {}
        for item in news:
            category = item['category']
            category_count[category] = category_count.get(category, 0) + 1
        
        print("各板块新闻数量:")
        for category, count in category_count.items():
            print(f"  {category}: {count} 条")
            
    except Exception as e:
        print(f"多板块测试失败: {e}")

def test_data_format():
    """测试数据格式"""
    print("\n=== 测试数据格式 ===")
    
    try:
        news = get_news(max_news=3, get_detail=False)
        
        if news:
            required_fields = ['title', 'url', 'content', 'category', 'publish_time', 'source']
            
            for i, item in enumerate(news):
                print(f"\n第{i+1}条新闻格式检查:")
                
                for field in required_fields:
                    if field in item:
                        value = item[field]
                        print(f"  ✅ {field}: {type(value).__name__} - {str(value)[:50]}...")
                    else:
                        print(f"  ❌ 缺少字段: {field}")
                
                # 检查数据完整性
                if all(field in item and item[field] for field in required_fields):
                    print("  ✅ 数据格式完整")
                else:
                    print("  ❌ 数据格式不完整")
                    
    except Exception as e:
        print(f"格式测试失败: {e}")

def save_sample_data():
    """保存样本数据"""
    print("\n=== 保存样本数据 ===")
    
    try:
        news = get_news(max_news=10, get_detail=False)
        
        if news:
            # 保存为JSON格式
            with open('thepaper_sample_data.json', 'w', encoding='utf-8') as f:
                json.dump(news, f, ensure_ascii=False, indent=2)
            
            print(f"样本数据已保存到 thepaper_sample_data.json")
            print(f"包含 {len(news)} 条新闻")
            
            # 显示前3条
            print("\n前3条新闻预览:")
            for i, item in enumerate(news[:3]):
                print(f"\n{i+1}. {item['title']}")
                print(f"   分类: {item['category']}")
                print(f"   时间: {item['publish_time']}")
                print(f"   链接: {item['url']}")
                print(f"   内容: {item['content'][:100]}...")
                
    except Exception as e:
        print(f"保存样本数据失败: {e}")

def main():
    """主测试函数"""
    print("澎湃新闻主控制器测试")
    print("=" * 50)
    
    # 运行各项测试
    test_single_channel()
    test_multiple_channels()
    test_data_format()
    save_sample_data()
    
    print("\n" + "=" * 50)
    print("测试完成!")

if __name__ == "__main__":
    main()
