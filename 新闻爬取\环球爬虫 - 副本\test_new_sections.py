#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增板块的爬虫功能
"""

from huanqiu_universal_spider import SECTIONS, HuanqiuUniversalSpider

def test_all_sections():
    """显示所有板块配置"""
    print("=== 环球网通用爬虫 - 全部板块配置 ===")
    print(f"共配置了 {len(SECTIONS)} 个板块:\n")
    
    for i, (key, config) in enumerate(SECTIONS.items(), 1):
        special_mark = " (特殊API)" if config.get('special_api') else ""
        print(f"{i:2d}. {key:15s} - {config['name']:6s} - {config['base_url']}{special_mark}")
        print(f"    节点数量: {len(config['nodes'])}")
        print(f"    API地址: {config['api_url']}")
        print()

def test_new_sections():
    """测试新增的板块"""
    new_sections = [
        'hope',      # 公益
        'society',   # 社会  
        'digital',   # 数码 (特殊API)
        'game',      # 互动娱乐
        'safety',    # 消防
        'business',  # 商业
        'oversea',   # 海外
        'culture'    # 文化
    ]
    
    print("=== 测试新增板块 ===")
    
    for section_key in new_sections:
        print(f"\n{'='*60}")
        print(f"测试板块: {section_key} ({SECTIONS[section_key]['name']})")
        
        try:
            spider = HuanqiuUniversalSpider(section_key)
            print(f"✅ 爬虫初始化成功")
            print(f"   基础URL: {spider.config['base_url']}")
            print(f"   API URL: {spider.config['api_url']}")
            print(f"   节点数量: {len(spider.config['nodes'])}")
            
            if spider.config.get('special_api'):
                print(f"   ⚠️  特殊API格式")
            
            # 尝试获取文章列表（不获取详细内容，只测试API）
            print(f"   正在测试API连接...")
            articles = spider.get_article_list(offset=0, limit=5)
            
            if articles:
                print(f"   ✅ API连接成功，获取到 {len(articles)} 条文章")
                if articles:
                    print(f"   示例标题: {articles[0].get('title', 'N/A')[:50]}...")
            else:
                print(f"   ❌ API连接失败或无数据")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
        
        print("="*60)

def test_section_api_formats():
    """测试不同API格式"""
    print("\n=== API格式测试 ===")
    
    # 测试标准API格式
    print("1. 标准API格式测试 (hope - 公益):")
    try:
        spider = HuanqiuUniversalSpider('hope')
        articles = spider.get_article_list(offset=0, limit=3)
        print(f"   ✅ 标准API: 获取到 {len(articles)} 条文章")
    except Exception as e:
        print(f"   ❌ 标准API测试失败: {e}")
    
    # 测试特殊API格式
    print("\n2. 特殊API格式测试 (digital - 数码):")
    try:
        spider = HuanqiuUniversalSpider('digital')
        articles = spider.get_article_list(offset=0, limit=3)
        print(f"   ✅ 特殊API: 获取到 {len(articles)} 条文章")
    except Exception as e:
        print(f"   ❌ 特殊API测试失败: {e}")

if __name__ == "__main__":
    # 显示所有板块
    test_all_sections()
    
    # 测试新增板块
    test_new_sections()
    
    # 测试API格式
    test_section_api_formats()
    
    print(f"\n=== 测试完成 ===")
    print(f"环球网爬虫系统现已支持 {len(SECTIONS)} 个板块！")
