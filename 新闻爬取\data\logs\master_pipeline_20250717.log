2025-07-17 21:15:32 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-17 21:15:32 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-17 21:15:32 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=30, detail=True, all=False
2025-07-17 21:15:32 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-17 21:15:32 - MasterPipeline - INFO -    参数: max_news=30, detail=True, all=False
2025-07-17 21:15:32 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-17 21:15:58 - MasterPipeline - INFO - ⏱️ 总执行时间: 25.66 秒
2025-07-17 21:16:16 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-17 21:16:16 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-17 21:16:16 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=10, detail=True, all=False
2025-07-17 21:16:16 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-17 21:16:16 - MasterPipeline - INFO -    参数: max_news=10, detail=True, all=False
2025-07-17 21:16:16 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-17 21:17:54 - MasterPipeline - INFO - ✅ 新闻爬取成功
2025-07-17 21:17:54 - MasterPipeline - WARNING - ⚠️ 发现 7 个质量问题
2025-07-17 21:17:54 - MasterPipeline - WARNING -    - 新闻16缺少media字段
2025-07-17 21:17:54 - MasterPipeline - WARNING -    - 新闻17缺少media字段
2025-07-17 21:17:54 - MasterPipeline - WARNING -    - 新闻18缺少media字段
2025-07-17 21:17:54 - MasterPipeline - WARNING -    - 新闻19缺少media字段
2025-07-17 21:17:54 - MasterPipeline - WARNING -    - 新闻20缺少media字段
2025-07-17 21:17:54 - MasterPipeline - INFO - ✅ 爬取结果验证通过: 40条新闻, 文件大小84125字节
2025-07-17 21:17:54 - MasterPipeline - INFO - ✅ 阶段1完成: 爬取 40 条新闻，保存到 unified_news_20250717_211754.csv
2025-07-17 21:17:54 - MasterPipeline - INFO - 🔄 阶段2: 开始向量化处理
2025-07-17 21:17:54 - MasterPipeline - INFO -    处理文件: unified_news_20250717_211754.csv
2025-07-17 21:17:54 - MasterPipeline - INFO - 🔄 执行向量化处理 (尝试 1/2)
2025-07-17 21:17:54 - MasterPipeline - INFO - ✅ 向量化处理成功
2025-07-17 21:17:54 - MasterPipeline - INFO - ℹ️ 所有新闻都被跳过: 重复10条, 低质量0条
2025-07-17 21:17:54 - MasterPipeline - INFO - ✅ 处理结果验证通过: 成功0条, 失败0条
2025-07-17 21:17:54 - MasterPipeline - INFO - ✅ 阶段2完成: 处理 0 条新闻向量
2025-07-17 21:17:54 - MasterPipeline - INFO - 💾 阶段3: 开始数据库存储验证
2025-07-17 21:17:54 - MasterPipeline - INFO -    数据库路径: C:\Users\<USER>\Desktop\完整拼接3\新闻爬取\news_vectors
2025-07-17 21:17:54 - MasterPipeline - INFO -    存储文档数: 1087
2025-07-17 21:17:54 - MasterPipeline - INFO - ✅ 存储结果验证通过: 1087条文档
2025-07-17 21:17:54 - MasterPipeline - INFO - 🔧 执行数据库优化...
2025-07-17 21:17:55 - MasterPipeline - INFO - 💾 数据库备份: C:\Users\<USER>\Desktop\完整拼接3\新闻爬取\data\archive\db_backup_20250717_211755
2025-07-17 21:17:55 - MasterPipeline - INFO - ✅ 阶段3完成: 验证 1087 条存储记录
2025-07-17 21:17:55 - MasterPipeline - INFO - 📁 阶段4: 开始文件管理和清理
2025-07-17 21:17:55 - MasterPipeline - INFO - 📦 文件已归档: C:\Users\<USER>\Desktop\完整拼接3\新闻爬取\data\archive\archived_20250717_211755_unified_news_20250717_211754.csv
2025-07-17 21:17:55 - MasterPipeline - INFO - 🧹 清理了 2 个旧文件
2025-07-17 21:17:55 - MasterPipeline - INFO - 🗑️ 删除临时CSV文件: unified_news_20250717_211754.csv
2025-07-17 21:17:55 - MasterPipeline - INFO - ✅ 阶段4完成: 文件管理完成
2025-07-17 21:17:55 - MasterPipeline - INFO - 🎉 完整流水线执行成功!
2025-07-17 21:17:55 - MasterPipeline - INFO - ⏱️ 总执行时间: 98.48 秒
2025-07-17 21:21:07 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-17 21:21:07 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-17 21:21:07 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=10, detail=True, all=False
2025-07-17 21:21:07 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-17 21:21:07 - MasterPipeline - INFO -    参数: max_news=10, detail=True, all=False
2025-07-17 21:21:07 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-17 21:22:50 - MasterPipeline - INFO - ✅ 新闻爬取成功
2025-07-17 21:22:50 - MasterPipeline - WARNING - ⚠️ 发现 7 个质量问题
2025-07-17 21:22:50 - MasterPipeline - WARNING -    - 新闻16缺少media字段
2025-07-17 21:22:50 - MasterPipeline - WARNING -    - 新闻17缺少media字段
2025-07-17 21:22:50 - MasterPipeline - WARNING -    - 新闻18缺少media字段
2025-07-17 21:22:50 - MasterPipeline - WARNING -    - 新闻19缺少media字段
2025-07-17 21:22:50 - MasterPipeline - WARNING -    - 新闻20缺少media字段
2025-07-17 21:22:50 - MasterPipeline - INFO - ✅ 爬取结果验证通过: 40条新闻, 文件大小84146字节
2025-07-17 21:22:50 - MasterPipeline - INFO - ✅ 阶段1完成: 爬取 40 条新闻，保存到 unified_news_20250717_212250.csv
2025-07-17 21:22:50 - MasterPipeline - INFO - 🔄 阶段2: 开始向量化处理
2025-07-17 21:22:50 - MasterPipeline - INFO -    处理文件: unified_news_20250717_212250.csv
2025-07-17 21:22:50 - MasterPipeline - INFO - 🔄 执行向量化处理 (尝试 1/2)
2025-07-17 21:22:50 - MasterPipeline - INFO - ✅ 向量化处理成功
2025-07-17 21:22:50 - MasterPipeline - INFO - ℹ️ 所有新闻都被跳过: 重复10条, 低质量0条
2025-07-17 21:22:50 - MasterPipeline - INFO - ✅ 处理结果验证通过: 成功0条, 失败0条
2025-07-17 21:22:50 - MasterPipeline - INFO - ✅ 阶段2完成: 处理 0 条新闻向量
2025-07-17 21:22:50 - MasterPipeline - INFO - 💾 阶段3: 开始数据库存储验证
2025-07-17 21:22:50 - MasterPipeline - INFO -    数据库路径: C:\Users\<USER>\Desktop\完整拼接3\新闻爬取\news_vectors
2025-07-17 21:22:50 - MasterPipeline - INFO -    存储文档数: 1087
2025-07-17 21:22:50 - MasterPipeline - INFO - ✅ 存储结果验证通过: 1087条文档
2025-07-17 21:22:50 - MasterPipeline - INFO - 🔧 执行数据库优化...
2025-07-17 21:22:50 - MasterPipeline - INFO - 💾 数据库备份: C:\Users\<USER>\Desktop\完整拼接3\新闻爬取\data\archive\db_backup_20250717_212250
2025-07-17 21:22:50 - MasterPipeline - INFO - ✅ 阶段3完成: 验证 1087 条存储记录
2025-07-17 21:22:50 - MasterPipeline - INFO - 📁 阶段4: 开始文件管理和清理
2025-07-17 21:22:50 - MasterPipeline - INFO - 📦 文件已归档: C:\Users\<USER>\Desktop\完整拼接3\新闻爬取\data\archive\archived_20250717_212250_unified_news_20250717_212250.csv
2025-07-17 21:22:50 - MasterPipeline - INFO - 🧹 清理了 0 个旧文件
2025-07-17 21:22:50 - MasterPipeline - INFO - 🗑️ 删除临时CSV文件: unified_news_20250717_212250.csv
2025-07-17 21:22:50 - MasterPipeline - INFO - ✅ 阶段4完成: 文件管理完成
2025-07-17 21:22:50 - MasterPipeline - INFO - 🎉 完整流水线执行成功!
2025-07-17 21:22:50 - MasterPipeline - INFO - ⏱️ 总执行时间: 103.41 秒
2025-07-17 21:31:12 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-17 21:31:12 - MasterPipeline - INFO - 🚀 开始执行完整新闻处理流水线
2025-07-17 21:31:12 - MasterPipeline - INFO - ⚙️ 配置参数: max_news=10, detail=True, all=False
2025-07-17 21:31:12 - MasterPipeline - INFO - 📰 阶段1: 开始新闻爬取
2025-07-17 21:31:12 - MasterPipeline - INFO -    参数: max_news=10, detail=True, all=False
2025-07-17 21:31:12 - MasterPipeline - INFO - 🔄 执行新闻爬取 (尝试 1/3)
2025-07-17 21:32:58 - MasterPipeline - INFO - ✅ 新闻爬取成功
2025-07-17 21:32:58 - MasterPipeline - WARNING - ⚠️ 发现 7 个质量问题
2025-07-17 21:32:58 - MasterPipeline - WARNING -    - 新闻16缺少media字段
2025-07-17 21:32:58 - MasterPipeline - WARNING -    - 新闻17缺少media字段
2025-07-17 21:32:58 - MasterPipeline - WARNING -    - 新闻18缺少media字段
2025-07-17 21:32:58 - MasterPipeline - WARNING -    - 新闻19缺少media字段
2025-07-17 21:32:58 - MasterPipeline - WARNING -    - 新闻20缺少media字段
2025-07-17 21:32:58 - MasterPipeline - INFO - ✅ 爬取结果验证通过: 40条新闻, 文件大小88108字节
2025-07-17 21:32:58 - MasterPipeline - INFO - ✅ 阶段1完成: 爬取 40 条新闻，保存到 unified_news_20250717_213258.csv
2025-07-17 21:32:58 - MasterPipeline - INFO - 🔄 阶段2: 开始向量化处理
2025-07-17 21:32:58 - MasterPipeline - INFO -    处理文件: unified_news_20250717_213258.csv
2025-07-17 21:32:58 - MasterPipeline - INFO - 🔄 执行向量化处理 (尝试 1/2)
2025-07-17 21:33:02 - MasterPipeline - INFO - ✅ 向量化处理成功
2025-07-17 21:33:02 - MasterPipeline - INFO - ✅ 处理结果验证通过: 成功7条, 失败0条
2025-07-17 21:33:02 - MasterPipeline - INFO - ✅ 阶段2完成: 处理 7 条新闻向量
2025-07-17 21:33:02 - MasterPipeline - INFO - 💾 阶段3: 开始数据库存储验证
2025-07-17 21:33:02 - MasterPipeline - INFO -    数据库路径: C:\Users\<USER>\Desktop\完整拼接3\新闻爬取\news_vectors
2025-07-17 21:33:02 - MasterPipeline - INFO -    存储文档数: 1094
2025-07-17 21:33:02 - MasterPipeline - INFO - ✅ 存储结果验证通过: 1094条文档
2025-07-17 21:33:02 - MasterPipeline - INFO - 🔧 执行数据库优化...
2025-07-17 21:33:02 - MasterPipeline - INFO - 💾 数据库备份: C:\Users\<USER>\Desktop\完整拼接3\新闻爬取\data\archive\db_backup_20250717_213302
2025-07-17 21:33:02 - MasterPipeline - INFO - ✅ 阶段3完成: 验证 1094 条存储记录
2025-07-17 21:33:02 - MasterPipeline - INFO - 📁 阶段4: 开始文件管理和清理
2025-07-17 21:33:02 - MasterPipeline - INFO - 📦 文件已归档: C:\Users\<USER>\Desktop\完整拼接3\新闻爬取\data\archive\archived_20250717_213302_unified_news_20250717_213258.csv
2025-07-17 21:33:02 - MasterPipeline - INFO - 🧹 清理了 0 个旧文件
2025-07-17 21:33:02 - MasterPipeline - INFO - 🗑️ 删除临时CSV文件: unified_news_20250717_213258.csv
2025-07-17 21:33:02 - MasterPipeline - INFO - ✅ 阶段4完成: 文件管理完成
2025-07-17 21:33:02 - MasterPipeline - INFO - 🎉 完整流水线执行成功!
2025-07-17 21:33:02 - MasterPipeline - INFO - ⏱️ 总执行时间: 109.77 秒
2025-07-17 21:36:50 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-17 21:36:50 - MasterPipeline - INFO - 🔍 运行搜索演示: 人工智能
2025-07-17 21:37:17 - MasterPipeline - INFO - 🚀 主流水线初始化完成
2025-07-17 21:37:17 - MasterPipeline - INFO - 🔍 运行搜索演示: 人工智能
