#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成新闻系统测试脚本
用少量数据快速测试整个流水线的效果
"""

import os
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_pipeline import IntegratedNewsPipeline
from config_loader import ConfigLoader

def print_banner():
    """打印测试横幅"""
    print("\n" + "="*60)
    print("🧪 集成新闻系统 - 快速测试模式")
    print("   测试整个流水线的完整功能")
    print("="*60)

def print_step(step_num, step_name):
    """打印步骤信息"""
    print(f"\n{'='*50}")
    print(f"🔄 步骤 {step_num}: {step_name}")
    print("="*50)

def test_quick_pipeline():
    """快速测试流水线"""
    print_banner()
    
    # 检查配置
    print_step(0, "检查配置环境")
    try:
        config = ConfigLoader()
        print("✅ 配置加载成功")
        
        # 检查关键配置
        if not config.get_llm_config():
            print("❌ LLM配置缺失")
            return False
        print("✅ LLM配置正常")
        
        if not config.get_embedding_config():
            print("❌ Embedding配置缺失") 
            return False
        print("✅ Embedding配置正常")
        
        if not config.get_database_config():
            print("❌ 数据库配置缺失")
            return False
        print("✅ 数据库配置正常")
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False
    
    # 初始化流水线
    print_step(1, "初始化系统组件")
    try:
        pipeline = IntegratedNewsPipeline()
        print("✅ 流水线初始化成功")
    except Exception as e:
        print(f"❌ 流水线初始化失败: {e}")
        return False
    
    # 开始测试
    start_time = time.time()
    
    try:
        # 步骤1: 爬取少量新闻 (测试模式)
        print_step(2, "爬取测试新闻数据")
        news_result = pipeline._step1_crawl_news(test_mode=True, max_items_per_platform=5)
        
        if not news_result['success']:
            print(f"❌ 新闻爬取失败: {news_result.get('error', '未知错误')}")
            return False
        
        print(f"✅ 爬取成功: {news_result['total_news']} 条新闻")
        
        # 步骤2: 合并话题 (限制数量)
        print_step(3, "合并相似话题")
        merge_result = pipeline._step2_merge_topics(
            news_result['news_data'], 
            max_topics=8  # 限制最多8个话题
        )
        
        if not merge_result['success']:
            print(f"❌ 话题合并失败: {merge_result.get('error', '未知错误')}")
            return False
        
        print(f"✅ 合并成功: {len(news_result['news_data'])} → {merge_result['merged_count']} 个话题")
        
        # 步骤3: 深度分析 (只处理前3个话题)
        print_step(4, "深度分析话题")
        topics_to_analyze = merge_result['merged_topics'][:3]  # 只分析前3个
        print(f"📊 选择前 {len(topics_to_analyze)} 个话题进行深度分析")
        
        analysis_result = pipeline._step3_process_topics(topics_to_analyze)
        
        if not analysis_result['success']:
            print(f"❌ 深度分析失败: {analysis_result.get('error', '未知错误')}")
            return False
        
        print(f"✅ 分析成功: {analysis_result['processed_count']} 个话题")
        
        # 步骤4: 搜索相关新闻
        print_step(5, "搜索相关新闻")
        search_result = pipeline._step4_search_news(analysis_result['processed_topics'])
        
        if not search_result['success']:
            print(f"❌ 新闻搜索失败: {search_result.get('error', '未知错误')}")
            return False
        
        print(f"✅ 搜索成功: {search_result['searched_topics']} 个话题")
        
        # 步骤5: 生成文章 (只生成2篇)
        print_step(6, "生成测试文章")
        topics_for_articles = analysis_result['processed_topics'][:2]  # 只生成2篇
        search_for_articles = search_result['search_results'][:2]
        
        print(f"📝 选择前 {len(topics_for_articles)} 个话题生成文章")
        
        article_result = pipeline._step5_generate_articles(
            topics_for_articles, 
            search_for_articles
        )
        
        if not article_result['success']:
            print(f"❌ 文章生成失败: {article_result.get('error', '未知错误')}")
            return False
        
        print(f"✅ 生成成功: {len(article_result['articles'])} 篇文章")
        
        # 步骤6: 保存到数据库
        print_step(7, "保存到数据库")
        save_result = pipeline._step6_save_to_database(article_result['articles'])
        
        if not save_result['success']:
            print(f"❌ 数据库保存失败: {save_result.get('error', '未知错误')}")
            return False
        
        print(f"✅ 保存成功: {save_result['saved_count']} 篇文章")
        
        # 测试完成
        end_time = time.time()
        total_time = end_time - start_time
        
        print_step(8, "测试完成")
        print(f"🎉 测试流水线执行成功!")
        print(f"⏱️  总耗时: {total_time:.1f} 秒")
        print(f"📊 测试摘要:")
        print(f"   📰 爬取新闻: {news_result['total_news']} 条")
        print(f"   🔄 合并话题: {merge_result['merged_count']} 个")
        print(f"   🧠 深度分析: {analysis_result['processed_count']} 个")
        print(f"   🔍 搜索话题: {search_result['searched_topics']} 个")
        print(f"   📝 生成文章: {len(article_result['articles'])} 篇")
        print(f"   💾 保存文章: {save_result['saved_count']} 篇")
        
        # 显示生成的文章信息
        print(f"\n📝 生成的文章:")
        for i, article in enumerate(article_result['articles'], 1):
            print(f"   {i}. {article.get('title', '无标题')}")
            print(f"      分类: {article.get('category', '未知')}")
            print(f"      字数: {article.get('word_count', 0)}")
            print(f"      重要性: {article.get('importance_score', 0)}/10")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_components():
    """测试单个组件"""
    print_banner()
    print("🔧 单组件测试模式")
    
    try:
        pipeline = IntegratedNewsPipeline()
        
        # 测试新闻爬取
        print("\n🧪 测试新闻爬取...")
        news_result = pipeline._step1_crawl_news(test_mode=True, max_items_per_platform=3)
        if news_result['success']:
            print(f"✅ 爬取测试通过: {news_result['total_news']} 条")
        else:
            print(f"❌ 爬取测试失败: {news_result.get('error')}")
            return False
        
        # 测试话题合并
        print("\n🧪 测试话题合并...")
        if len(news_result['news_data']) >= 5:
            test_topics = news_result['news_data'][:10]  # 取前10个测试
            merge_result = pipeline._step2_merge_topics(test_topics, max_topics=3)
            if merge_result['success']:
                print(f"✅ 合并测试通过: {len(test_topics)} → {merge_result['merged_count']}")
            else:
                print(f"❌ 合并测试失败: {merge_result.get('error')}")
                return False
        
        print("\n🎉 单组件测试全部通过!")
        return True
        
    except Exception as e:
        print(f"❌ 单组件测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 集成新闻系统测试工具")
    print("\n请选择测试模式:")
    print("1. 完整流水线测试 (推荐)")
    print("2. 单组件测试")
    print("0. 退出")
    
    try:
        choice = input("\n请输入选项 (1/2/0): ").strip()
        
        if choice == "1":
            success = test_quick_pipeline()
        elif choice == "2":
            success = test_individual_components()
        elif choice == "0":
            print("👋 退出测试")
            return
        else:
            print("❌ 无效选项")
            return
        
        if success:
            print(f"\n🎉 测试成功完成!")
        else:
            print(f"\n❌ 测试失败，请检查错误信息")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
