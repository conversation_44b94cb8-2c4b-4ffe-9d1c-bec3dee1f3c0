# 澎湃新闻爬虫

这是一个专门用于爬取澎湃新闻（https://m.thepaper.cn/）要闻板块的Python爬虫程序。

## 功能特点

- 🚀 支持爬取澎湃新闻要闻板块的所有新闻
- 📄 同时支持首页HTML解析和API接口调用
- 💾 自动保存为CSV格式，方便数据分析
- 🔄 支持分页爬取，可自定义爬取页数
- 📊 提供详细的爬取统计信息
- ⏱️ 智能延时，避免请求过于频繁

## 爬取的数据字段

### 基本信息
- `news_id`: 新闻ID
- `title`: 新闻标题
- `url`: 新闻链接
- `image_url`: 新闻图片链接
- `image_alt`: 图片描述

### 发布信息
- `source`: 新闻来源（如：中国政库、直击现场等）
- `source_url`: 来源链接
- `pub_time`: 发布时间（如：1小时前）
- `pub_time_new`: 格式化发布时间
- `pub_time_long`: 时间戳

### 互动数据
- `comment_count`: 评论数
- `praise_times`: 点赞数
- `interaction_num`: 互动数

### 内容类型
- `content_type`: 内容类型（article/video）
- `video_duration`: 视频时长（如果是视频）
- `label`: 标签（如：推荐）

### 其他信息
- `tags`: 新闻标签列表
- `crawl_time`: 爬取时间

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 基本使用

```bash
python run_crawler.py
```

### 2. 自定义爬取页数

```bash
python run_crawler.py --pages 5
```

### 3. 指定输出文件名

```bash
python run_crawler.py --pages 3 --output my_news.csv
```

### 4. 在代码中使用

```python
from thepaper_crawler import ThepaperCrawler

# 创建爬虫实例
crawler = ThepaperCrawler()

# 爬取要闻数据
news_data = crawler.crawl_yawen_news(max_pages=3)

# 保存到CSV
crawler.save_to_csv(news_data, "news_output.csv")
```

## 测试

运行测试脚本来验证爬虫是否正常工作：

```bash
python test_crawler.py
```

## 文件说明

- `thepaper_crawler.py`: 主要的爬虫类
- `run_crawler.py`: 命令行运行脚本
- `test_crawler.py`: 测试脚本
- `requirements.txt`: 依赖包列表
- `README.md`: 说明文档

## 技术实现

### 1. 首页数据获取
- 使用BeautifulSoup解析HTML页面
- 提取`<div class="index_wrapper__9rz3z">`中的新闻信息

### 2. API数据获取
- 调用`https://api.thepaper.cn/contentapi/nodeCont/getByChannelId`接口
- 使用POST请求获取分页数据
- 处理`excludeContIds`和`listRecommendIds`参数

### 3. 数据处理
- 统一数据格式
- 处理时间戳转换
- 提取新闻ID和分类信息

## 注意事项

1. **请求频率**: 程序已添加1秒延时，请勿过于频繁请求
2. **数据准确性**: 网站结构可能会变化，如遇问题请及时更新代码
3. **使用规范**: 请遵守网站的robots.txt和使用条款
4. **数据用途**: 爬取的数据仅供学习和研究使用

## 扩展功能

当前版本专注于要闻板块，后续可以扩展：

- 其他板块爬取（深度、国际、财经等）
- 新闻详情页面内容爬取
- 评论数据爬取
- 实时监控新闻更新
- 数据可视化分析

## 问题反馈

如果在使用过程中遇到问题，请检查：

1. 网络连接是否正常
2. 依赖包是否正确安装
3. 网站结构是否发生变化

## 更新日志

- v1.0.0: 初始版本，支持要闻板块爬取
