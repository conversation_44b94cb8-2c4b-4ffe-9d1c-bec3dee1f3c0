#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环球网通用爬虫是否符合规范要求
"""

from huanqiu_universal_spider import get_news, SECTIONS

def test_function_interface():
    """测试函数接口是否符合规范"""
    print("=== 测试函数接口规范 ===")
    
    # 测试默认参数
    try:
        news_data = get_news()  # 使用默认参数
        print(f"✅ 默认参数调用成功，获取到 {len(news_data)} 条新闻")
    except Exception as e:
        print(f"❌ 默认参数调用失败: {e}")
    
    # 测试指定参数
    try:
        news_data = get_news(max_news=3, get_detail=True, section='finance')
        print(f"✅ 指定参数调用成功，获取到 {len(news_data)} 条财经新闻")
    except Exception as e:
        print(f"❌ 指定参数调用失败: {e}")

def test_output_format():
    """测试输出格式是否符合规范"""
    print("\n=== 测试输出格式规范 ===")
    
    # 获取少量新闻进行格式测试
    news_data = get_news(max_news=2, get_detail=True, section='world')
    
    if not news_data:
        print("❌ 未获取到新闻数据")
        return
    
    print(f"获取到 {len(news_data)} 条新闻，检查格式...")
    
    required_fields = ['title', 'url', 'content', 'category', 'publish_time', 'source']
    
    for i, news in enumerate(news_data, 1):
        print(f"\n第{i}条新闻格式检查:")
        
        # 检查必需字段
        missing_fields = []
        for field in required_fields:
            if field not in news:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields}")
        else:
            print("✅ 包含所有必需字段")
        
        # 检查字段类型和格式
        print(f"   title: {type(news.get('title'))} - '{news.get('title', '')[:50]}...'")
        print(f"   url: {type(news.get('url'))} - {news.get('url', '')}")
        print(f"   content: {type(news.get('content'))} - 长度 {len(news.get('content', ''))} 字符")
        print(f"   category: {type(news.get('category'))} - '{news.get('category', '')}'")
        print(f"   publish_time: {type(news.get('publish_time'))} - '{news.get('publish_time', '')}'")
        print(f"   source: {type(news.get('source'))} - '{news.get('source', '')}'")
        
        # 检查时间格式 (MM-DD HH:MM)
        publish_time = news.get('publish_time', '')
        if len(publish_time) == 11 and publish_time[2] == '-' and publish_time[5] == ' ' and publish_time[8] == ':':
            print("✅ 时间格式正确 (MM-DD HH:MM)")
        else:
            print(f"❌ 时间格式错误，应为 MM-DD HH:MM，实际为: '{publish_time}'")

def test_different_sections():
    """测试不同板块"""
    print("\n=== 测试不同板块 ===")
    
    test_sections = ['world', 'china', 'finance', 'tech', 'digital']
    
    for section in test_sections:
        try:
            news_data = get_news(max_news=1, get_detail=False, section=section)
            if news_data:
                print(f"✅ {section} ({SECTIONS[section]['name']}) - 获取到 {len(news_data)} 条新闻")
                print(f"   示例: {news_data[0]['title'][:30]}...")
            else:
                print(f"⚠️  {section} ({SECTIONS[section]['name']}) - 无今日新闻")
        except Exception as e:
            print(f"❌ {section} ({SECTIONS[section]['name']}) - 测试失败: {e}")

def show_example_output():
    """显示示例输出"""
    print("\n=== 示例输出 ===")
    
    news_data = get_news(max_news=1, get_detail=True, section='world')
    
    if news_data:
        import json
        print("符合规范的输出格式:")
        print(json.dumps(news_data[0], ensure_ascii=False, indent=2))
    else:
        print("无法获取示例数据")

if __name__ == "__main__":
    print("环球网通用爬虫规范符合性测试")
    print("="*50)
    
    # 测试函数接口
    test_function_interface()
    
    # 测试输出格式
    test_output_format()
    
    # 测试不同板块
    test_different_sections()
    
    # 显示示例输出
    show_example_output()
    
    print("\n" + "="*50)
    print("测试完成！")
