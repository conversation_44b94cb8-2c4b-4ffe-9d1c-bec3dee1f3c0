#!/bin/bash
# 新闻爬取和向量化定时任务脚本
# 每2小时执行一次

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 设置日志文件
LOG_DIR="logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/news_crawl_$(date +%Y%m%d).log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log "🚀 开始新闻爬取和向量化任务..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    log "❌ Python3 未找到"
    exit 1
fi

# 检查必要文件
if [ ! -f "master_pipeline.py" ]; then
    log "❌ master_pipeline.py 文件不存在"
    exit 1
fi

# 执行新闻爬取 (all参数表示爬取所有新闻)
log "📰 执行新闻爬取..."
if python3 master_pipeline.py all >> "$LOG_FILE" 2>&1; then
    log "✅ 新闻爬取和向量化完成"
    
    # 检查生成的文件
    NEWS_OUTPUT_COUNT=$(find news_output -name "*.json" -mmin -120 2>/dev/null | wc -l)
    log "📊 本次生成文件数: $NEWS_OUTPUT_COUNT"
    
    # 清理旧日志 (保留7天)
    find "$LOG_DIR" -name "news_crawl_*.log" -mtime +7 -delete 2>/dev/null
    
    exit 0
else
    log "❌ 新闻爬取失败"
    exit 1
fi
