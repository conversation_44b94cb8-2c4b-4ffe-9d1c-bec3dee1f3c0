#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫配置文件
可以在这里调整各平台的数据获取数量和其他参数
"""

# 各平台数据获取数量配置
PLATFORM_LIMITS = {
    'zhihu': 50,        # 知乎热榜数量 (API支持最大50)
    'bilibili': 50,     # B站热搜数量 (API支持最大50)  
    'github': 50,       # GitHub趋势数量 (API支持最大100)
    'v2ex': 50,         # V2EX热门数量 (手动限制)
    'ithome': 50,       # IT之家新闻数量 (手动限制)
}

# 网络请求配置
NETWORK_CONFIG = {
    'timeout': 10,          # 请求超时时间（秒）
    'max_retries': 3,       # 最大重试次数
    'retry_delay': 1,       # 重试间隔（秒）
    'request_interval': 2,  # 请求间隔（秒）- 增加到2秒更安全
}

# 各平台特定的速率限制配置
PLATFORM_RATE_LIMITS = {
    'github': {
        'requests_per_hour': 60,        # GitHub未认证限制: 60/小时
        'min_interval': 60,             # 最小间隔60秒 (3600/60)
        'burst_limit': 5,               # 突发请求限制
    },
    'zhihu': {
        'requests_per_hour': 1000,      # 知乎相对宽松
        'min_interval': 4,              # 最小间隔4秒
        'burst_limit': 10,
    },
    'bilibili': {
        'requests_per_hour': 1000,      # B站相对宽松
        'min_interval': 4,              # 最小间隔4秒
        'burst_limit': 10,
    }
}

# 代理配置
PROXY_CONFIG = {
    'enabled': False,                   # 是否启用代理
    'http_proxy': None,                 # HTTP代理地址
    'https_proxy': None,                # HTTPS代理地址
    'rotation_enabled': False,          # 是否启用代理轮换
    'proxy_list': []                    # 代理列表
}

# 输出配置
OUTPUT_CONFIG = {
    'save_to_file': True,               # 是否保存到文件
    'output_format': 'json',            # 输出格式: json, csv, txt
    'show_summary': True,               # 是否显示摘要
    'summary_preview_count': 5,         # 摘要预览条数
    'include_metadata': True,           # 是否包含元数据
}

# 平台启用配置
PLATFORM_ENABLED = {
    'weibo': True,                      # 微博热搜
    'baidu': True,                      # 百度热搜
    'zhihu': True,                      # 知乎热榜
    'toutiao': True,                    # 今日头条
    'bilibili': True,                   # B站热搜
    'github': True,                     # GitHub趋势
    'v2ex': True,                       # V2EX热门
    'ithome': True,                     # IT之家
}

# 数据质量配置
QUALITY_CONFIG = {
    'min_title_length': 5,              # 最小标题长度
    'max_title_length': 200,            # 最大标题长度
    'filter_duplicates': True,          # 是否过滤重复
    'similarity_threshold': 0.8,        # 相似度阈值
    'remove_ads': True,                 # 是否移除广告
}

# 缓存配置
CACHE_CONFIG = {
    'enabled': True,                    # 是否启用缓存
    'cache_duration': 300,              # 缓存时长（秒）
    'cache_dir': 'cache',               # 缓存目录
    'max_cache_size': 100,              # 最大缓存文件数
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',                    # 日志级别
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_enabled': True,               # 是否写入文件
    'file_path': 'logs/crawler.log',    # 日志文件路径
    'max_file_size': 10,                # 最大文件大小（MB）
    'backup_count': 5,                  # 备份文件数量
}

# 用户代理配置
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
]

# 错误处理配置
ERROR_CONFIG = {
    'max_consecutive_failures': 3,     # 最大连续失败次数
    'failure_cooldown': 60,            # 失败冷却时间（秒）
    'auto_retry_enabled': True,        # 是否自动重试
    'fallback_enabled': True,          # 是否启用降级方案
}
