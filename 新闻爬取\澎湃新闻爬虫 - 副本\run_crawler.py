#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
澎湃新闻爬虫运行脚本
"""

from thepaper_crawler import ThepaperCrawler
import argparse
import sys

def main():
    parser = argparse.ArgumentParser(description='澎湃新闻爬虫 - 要闻板块')
    parser.add_argument('--pages', type=int, default=3, help='爬取页数 (默认: 3)')
    parser.add_argument('--output', type=str, help='输出文件名 (可选)')
    parser.add_argument('--content', action='store_true', help='是否获取新闻详细内容 (会显著增加爬取时间)')

    args = parser.parse_args()

    print("=" * 50)
    print("澎湃新闻爬虫 - 要闻板块")
    print("=" * 50)
    print(f"爬取页数: {args.pages}")
    print(f"获取详细内容: {'是' if args.content else '否'}")
    if args.output:
        print(f"输出文件: {args.output}")
    if args.content:
        print("⚠️  获取详细内容会显著增加爬取时间")
    print("=" * 50)

    try:
        crawler = ThepaperCrawler()
        news_data = crawler.crawl_yawen_news(max_pages=args.pages, include_content=args.content)

        if args.output and news_data:
            crawler.save_to_csv(news_data, args.output)

        print("\n爬取任务完成！")

        # 如果获取了详细内容，显示统计信息
        if args.content and news_data:
            content_count = sum(1 for news in news_data if news.get('content'))
            total_chars = sum(news.get('content_length', 0) for news in news_data if news.get('content'))
            print(f"成功获取 {content_count} 篇文章的详细内容")
            print(f"总字符数: {total_chars:,} 字符")

    except KeyboardInterrupt:
        print("\n用户中断爬取任务")
        sys.exit(1)
    except Exception as e:
        print(f"\n爬取过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
