#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分步走话题处理器示例 - 避免JSON解析问题
"""

import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from llm_client import LLMClient


class StepwiseTopicProcessor:
    """分步走话题处理器"""
    
    def __init__(self):
        self.llm_client = LLMClient()
    
    def analyze_topic_stepwise(self, topic: Dict[str, Any]) -> Dict[str, Any]:
        """
        分步骤分析话题，每次只要求AI返回一种类型的数据
        """
        print(f"🔍 开始分步分析话题: {topic['merged_title']}")
        
        result = {
            'original_topic': topic,
            'analysis_time': datetime.now().isoformat()
        }
        
        # 基础信息
        topic_title = topic['merged_title']
        topic_context = f"""
话题：{topic_title}
分类：{topic['category']}
重要性：{topic['importance_score']}/10
相关标题：{', '.join(topic['source_titles'][:3])}
"""
        
        try:
            # 步骤1: 提取关键词
            print("   📝 步骤1: 提取关键词...")
            result['keywords'] = self._extract_keywords(topic_title, topic_context)
            
            # 步骤2: 生成搜索查询
            print("   🔍 步骤2: 生成搜索查询...")
            result['search_queries'] = self._generate_search_queries(topic_title, topic_context)
            
            # 步骤3: 分析背景信息
            print("   📖 步骤3: 分析背景信息...")
            result['background_context'] = self._analyze_background(topic_title, topic_context)
            
            # 步骤4: 提取关键要点
            print("   📋 步骤4: 提取关键要点...")
            result['key_points'] = self._extract_key_points(topic_title, topic_context)
            
            # 步骤5: 识别相关实体
            print("   👥 步骤5: 识别相关实体...")
            result['related_entities'] = self._identify_entities(topic_title, topic_context)
            
            print(f"✅ 分步分析完成")
            return result
            
        except Exception as e:
            print(f"❌ 分步分析失败: {e}")
            return self._fallback_analysis(topic)
    
    def _extract_keywords(self, topic_title: str, context: str) -> List[str]:
        """步骤1: 提取关键词 - 返回纯文本列表"""
        
        prompt = f"""请为以下话题提取3-8个最重要的关键词：

{context}

要求：
- 只返回关键词，用逗号分隔
- 不要编号，不要解释
- 例如：人工智能,ChatGPT,技术发展,AI应用

请提取关键词："""

        try:
            response = self.llm_client.chat(
                prompt=prompt,
                max_tokens=200,
                temperature=0.3
            )
            
            # 解析逗号分隔的关键词
            keywords = [kw.strip() for kw in response.strip().split(',') if kw.strip()]
            return keywords[:8]  # 限制数量
            
        except Exception as e:
            print(f"⚠️ 关键词提取失败: {e}")
            return self._fallback_keywords(topic_title)
    
    def _generate_search_queries(self, topic_title: str, context: str) -> List[str]:
        """步骤2: 生成搜索查询 - 返回纯文本列表"""
        
        prompt = f"""请为以下话题生成5-8个不同角度的搜索查询词：

{context}

要求：
- 每行一个搜索查询词
- 不要编号，不要解释
- 包含不同表述方式
- 例如：
人工智能最新发展
ChatGPT技术突破
AI对社会的影响

请生成搜索查询："""

        try:
            response = self.llm_client.chat(
                prompt=prompt,
                max_tokens=500,
                temperature=0.3
            )
            
            # 按行分割搜索查询
            queries = [q.strip() for q in response.strip().split('\n') if q.strip()]
            # 清理可能的编号
            cleaned_queries = []
            for query in queries:
                # 去除开头的数字编号
                cleaned = re.sub(r'^\d+[\.\)]\s*', '', query)
                if cleaned and len(cleaned) > 5:
                    cleaned_queries.append(cleaned)
            
            return cleaned_queries[:8]  # 限制数量
            
        except Exception as e:
            print(f"⚠️ 搜索查询生成失败: {e}")
            return self._fallback_queries(topic_title)
    
    def _analyze_background(self, topic_title: str, context: str) -> str:
        """步骤3: 分析背景信息 - 返回纯文本"""
        
        prompt = f"""请为以下话题写一段背景信息（100-200字）：

{context}

要求：
- 解释话题的背景和起因
- 说明为什么成为热点
- 只返回背景文字，不要标题或格式
- 客观描述，不要主观评论

请写背景信息："""

        try:
            response = self.llm_client.chat(
                prompt=prompt,
                max_tokens=800,
                temperature=0.3
            )
            
            return response.strip()
            
        except Exception as e:
            print(f"⚠️ 背景分析失败: {e}")
            return f"{topic_title}相关事件成为近期热点话题，引发广泛关注和讨论。"
    
    def _extract_key_points(self, topic_title: str, context: str) -> List[str]:
        """步骤4: 提取关键要点 - 返回纯文本列表"""
        
        prompt = f"""请为以下话题列出3-6个关键要点：

{context}

要求：
- 每行一个要点
- 不要编号，不要解释
- 要点要简洁明确
- 例如：
技术突破带来新机遇
引发行业竞争加剧
对就业市场产生影响

请列出关键要点："""

        try:
            response = self.llm_client.chat(
                prompt=prompt,
                max_tokens=600,
                temperature=0.3
            )
            
            # 按行分割要点
            points = [p.strip() for p in response.strip().split('\n') if p.strip()]
            # 清理可能的编号
            cleaned_points = []
            for point in points:
                cleaned = re.sub(r'^\d+[\.\)]\s*', '', point)
                cleaned = re.sub(r'^[•·-]\s*', '', cleaned)  # 去除项目符号
                if cleaned and len(cleaned) > 5:
                    cleaned_points.append(cleaned)
            
            return cleaned_points[:6]  # 限制数量
            
        except Exception as e:
            print(f"⚠️ 关键要点提取失败: {e}")
            return [f"{topic_title}成为热点话题", "引发社会广泛关注", "相关讨论持续升温"]
    
    def _identify_entities(self, topic_title: str, context: str) -> Dict[str, List[str]]:
        """步骤5: 识别相关实体 - 分别获取不同类型实体"""
        
        result = {
            'persons': [],
            'organizations': [], 
            'locations': [],
            'others': []
        }
        
        # 5.1 识别人物
        try:
            persons_prompt = f"""从以下话题中识别相关人物：

{context}

要求：
- 只返回人物姓名，用逗号分隔
- 不要解释，不要职务
- 例如：张三,李四,王五

相关人物："""
            
            persons_response = self.llm_client.chat(
                prompt=persons_prompt,
                max_tokens=200,
                temperature=0.3
            )
            
            persons = [p.strip() for p in persons_response.strip().split(',') if p.strip()]
            result['persons'] = persons[:5]
            
        except Exception as e:
            print(f"⚠️ 人物识别失败: {e}")
        
        # 5.2 识别机构
        try:
            orgs_prompt = f"""从以下话题中识别相关机构组织：

{context}

要求：
- 只返回机构名称，用逗号分隔
- 包括公司、政府部门、组织等
- 例如：苹果公司,教育部,联合国

相关机构："""
            
            orgs_response = self.llm_client.chat(
                prompt=orgs_prompt,
                max_tokens=200,
                temperature=0.3
            )
            
            orgs = [o.strip() for o in orgs_response.strip().split(',') if o.strip()]
            result['organizations'] = orgs[:5]
            
        except Exception as e:
            print(f"⚠️ 机构识别失败: {e}")
        
        # 5.3 识别地点（简化处理）
        try:
            locations_prompt = f"""从以下话题中识别相关地点：

{context}

要求：
- 只返回地点名称，用逗号分隔
- 包括城市、国家、地区等
- 例如：北京,美国,欧洲

相关地点："""
            
            locations_response = self.llm_client.chat(
                prompt=locations_prompt,
                max_tokens=200,
                temperature=0.3
            )
            
            locations = [l.strip() for l in locations_response.strip().split(',') if l.strip()]
            result['locations'] = locations[:5]
            
        except Exception as e:
            print(f"⚠️ 地点识别失败: {e}")
        
        return result
    
    def _fallback_keywords(self, topic_title: str) -> List[str]:
        """备用关键词提取"""
        # 简单的关键词提取逻辑
        words = topic_title.split()
        return words[:5]
    
    def _fallback_queries(self, topic_title: str) -> List[str]:
        """备用搜索查询生成"""
        return [
            f"{topic_title}最新情况",
            f"{topic_title}详细报道", 
            f"{topic_title}分析评论",
            f"{topic_title}相关新闻"
        ]
    
    def _fallback_analysis(self, topic: Dict[str, Any]) -> Dict[str, Any]:
        """完全失败时的备用分析"""
        topic_title = topic['merged_title']
        
        return {
            'original_topic': topic,
            'analysis_time': datetime.now().isoformat(),
            'keywords': self._fallback_keywords(topic_title),
            'search_queries': self._fallback_queries(topic_title),
            'background_context': f"{topic_title}成为近期热点话题，引发广泛关注。",
            'key_points': [f"{topic_title}引发关注", "相关讨论持续", "影响逐步显现"],
            'related_entities': {
                'persons': [],
                'organizations': [],
                'locations': [],
                'others': []
            }
        }


# 示例使用
if __name__ == "__main__":
    processor = StepwiseTopicProcessor()
    
    test_topic = {
        'merged_title': '人工智能技术发展',
        'category': '科技',
        'importance_score': 8,
        'source_platforms': ['微博', '知乎'],
        'source_titles': ['ChatGPT技术突破', 'AI发展新阶段', '人工智能应用']
    }
    
    result = processor.analyze_topic_stepwise(test_topic)
    print(json.dumps(result, ensure_ascii=False, indent=2))