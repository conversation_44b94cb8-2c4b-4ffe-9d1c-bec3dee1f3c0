#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试澎湃新闻和环球新闻爬虫的集成
"""

import sys
import os

# 添加路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '澎湃新闻爬虫 - 副本'))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '环球爬虫 - 副本'))

def test_thepaper_import():
    """测试澎湃新闻爬虫导入"""
    try:
        from thepaper_master_crawler import get_news as get_thepaper_news
        print("[SUCCESS] 澎湃新闻爬虫导入成功")
        return True
    except Exception as e:
        print(f"[ERROR] 澎湃新闻爬虫导入失败: {e}")
        return False

def test_huanqiu_import():
    """测试环球新闻爬虫导入"""
    try:
        from huanqiu_universal_spider import get_all_sections_news as get_huanqiu_news
        print("[SUCCESS] 环球新闻爬虫导入成功")
        return True
    except Exception as e:
        print(f"[ERROR] 环球新闻爬虫导入失败: {e}")
        return False

def test_unified_crawler_import():
    """测试统一爬虫导入"""
    try:
        from unified_news_crawler import UnifiedNewsCrawler
        print("[SUCCESS] 统一新闻爬虫导入成功")
        return True
    except Exception as e:
        print(f"[ERROR] 统一新闻爬虫导入失败: {e}")
        return False

def test_integration():
    """测试集成功能"""
    try:
        from unified_news_crawler import UnifiedNewsCrawler, THEPAPER_AVAILABLE, HUANQIU_AVAILABLE
        
        print(f"[INFO] 澎湃新闻爬虫可用: {THEPAPER_AVAILABLE}")
        print(f"[INFO] 环球新闻爬虫可用: {HUANQIU_AVAILABLE}")
        
        # 创建爬虫实例
        crawler = UnifiedNewsCrawler(max_news_per_source=1, get_detail=False, get_all=False)
        print("[SUCCESS] 统一爬虫实例创建成功")
        
        # 检查结果字典
        expected_sources = ['netease', 'sina', 'ifeng', 'jiemian', 'thepaper', 'huanqiu']
        for source in expected_sources:
            if source in crawler.results:
                print(f"[SUCCESS] 爬虫结果字典包含 {source}")
            else:
                print(f"[ERROR] 爬虫结果字典缺少 {source}")
                
        return True
    except Exception as e:
        print(f"[ERROR] 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("澎湃新闻和环球新闻爬虫集成测试")
    print("=" * 60)
    
    tests = [
        ("澎湃新闻爬虫导入测试", test_thepaper_import),
        ("环球新闻爬虫导入测试", test_huanqiu_import),
        ("统一爬虫导入测试", test_unified_crawler_import),
        ("集成功能测试", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[TEST] {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"[FAILED] {test_name}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("[SUCCESS] 所有测试通过！澎湃新闻和环球新闻爬虫已成功集成。")
        print("\n[NEXT STEPS]")
        print("1. 运行完整爬虫: python unified_news_crawler.py 10 true")
        print("2. 处理向量数据: python master_pipeline.py")
        print("3. 搜索新闻: python news_search.py")
    else:
        print(f"[WARNING] {total-passed} 个测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()