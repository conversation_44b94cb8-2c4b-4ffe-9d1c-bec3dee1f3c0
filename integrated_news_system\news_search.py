#!/usr/bin/env python3
"""
新闻向量数据库查询模块
支持基于话题关键词的新闻检索
"""

import sys
import os

import json
import numpy as np
from typing import List, Dict, Optional, Any
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入现有的新闻搜索实现
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '新闻爬取'))

try:
    from csv_to_vector_processor import CSVNewsVectorProcessor
    from config import get_embedding_config
    from vector_database import SimpleVectorDB
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("💡 请确保新闻爬取项目的相关文件存在")


class NewsSearchEngine:
    """新闻搜索引擎 - 基于现有实现"""

    def __init__(self, db_path: str = None):
        """初始化搜索引擎"""
        # 确保 os 模块可用
        import os

        # 从环境变量获取向量数据库路径
        if db_path is None:
            db_path = os.environ.get('VECTOR_DB_PATH', '../新闻爬取/news_vectors')

        self.db_path = db_path
        self.config = get_embedding_config()

        # 检查数据库是否存在
        if not os.path.exists(db_path):
            print(f"⚠️ 向量数据库不存在: {db_path}")
            print("💡 将创建新的向量数据库")
            os.makedirs(db_path, exist_ok=True)

        # 初始化处理器和数据库（使用现有实现）
        try:
            # 确保所有必要的模块都正确导入
            import sys
            import os

            # 添加详细的调试信息
            print(f"🔍 尝试初始化向量数据库: {db_path}")
            print(f"🔍 配置信息: {self.config}")

            self.processor = CSVNewsVectorProcessor(self.config, db_path)
            self.db = self.processor.vector_db

            print(f"✅ 向量数据库加载成功")
            stats = self.db.get_stats()
            print(f"   📊 总文档数: {stats['total_documents']}")
            print(f"   🔢 向量维度: {stats['vector_dimension']}")
            print(f"   📁 存储路径: {stats['storage_path']}")
        except Exception as e:
            print(f"⚠️ 使用现有向量数据库失败: {e}")
            print(f"⚠️ 错误类型: {type(e)}")
            import traceback
            print("⚠️ 详细错误信息:")
            traceback.print_exc()
            print("💡 将使用简化模式")
            self.processor = None
            self.db = None
    
    def search(self, query: str, top_k: int = 10, threshold: float = 0.3,
               category: str = None, media: str = None, date_range: tuple = None) -> List[Dict]:
        """
        搜索新闻 - 使用现有实现

        Args:
            query: 搜索查询
            top_k: 返回结果数量
            threshold: 相似度阈值
            category: 新闻分类筛选
            media: 媒体来源筛选
            date_range: 日期范围筛选

        Returns:
            搜索结果列表
        """
        if not self.processor or not self.db:
            print("⚠️ 向量数据库未初始化，返回空结果")
            return []

        print(f"\n🔍 搜索查询: {query}")
        print(f"   参数: top_k={top_k}, threshold={threshold}")

        try:
            # 获取查询向量
            query_embedding = self.processor.get_embeddings([query])
            if not query_embedding:
                print("❌ 获取查询向量失败")
                return []

            # 执行向量搜索
            results = self.db.search(query_embedding[0], top_k=top_k*2, threshold=threshold)

            # 应用筛选条件
            filtered_results = self._apply_filters(results, category, media, date_range)

            # 限制结果数量
            final_results = filtered_results[:top_k]

            return final_results

        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            return []

    def _apply_filters(self, results: List[Dict], category: str = None,
                      media: str = None, date_range: tuple = None) -> List[Dict]:
        """应用筛选条件"""
        filtered = []

        for result in results:
            metadata = result.get('metadata', {})

            # 分类筛选
            if category and metadata.get('category', '').lower() != category.lower():
                continue

            # 媒体筛选
            if media and media.lower() not in metadata.get('media', '').lower():
                continue

            # 日期筛选
            if date_range:
                # 这里可以添加日期筛选逻辑
                pass

            filtered.append(result)

        return filtered
    
    def search_by_keywords(self, keywords: List[str], top_k: int = 10,
                          threshold: float = 0.4) -> List[Dict]:
        """
        基于关键词搜索相关新闻（优化版）

        Args:
            keywords: 关键词列表
            top_k: 返回结果数量
            threshold: 相似度阈值

        Returns:
            搜索结果列表
        """
        if not keywords:
            return []

        print(f"🔍 搜索关键词: {', '.join(keywords)}")

        # 智能组合关键词，创建更有意义的搜索短语
        search_phrases = self._create_meaningful_phrases(keywords)

        all_results = []
        seen_doc_ids = set()

        for phrase in search_phrases:
            print(f"   🔍 搜索短语: {phrase}")
            results = self.search(phrase, top_k=top_k//len(search_phrases)+2, threshold=threshold)

            # 去重合并
            for result in results:
                doc_id = result.get('doc_id', result.get('id', ''))
                if doc_id not in seen_doc_ids:
                    seen_doc_ids.add(doc_id)
                    result['search_phrase'] = phrase
                    all_results.append(result)

        # 按相似度排序并限制数量
        all_results.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        final_results = all_results[:top_k]

        print(f"✅ 找到 {len(final_results)} 条相关新闻")
        return final_results

    def _create_meaningful_phrases(self, keywords: List[str]) -> List[str]:
        """将关键词组合成更有意义的搜索短语"""
        if not keywords:
            return []

        phrases = []

        # 1. 完整关键词组合
        if len(keywords) <= 4:
            phrases.append(' '.join(keywords))
        else:
            # 如果关键词太多，分组组合
            phrases.append(' '.join(keywords[:3]))
            phrases.append(' '.join(keywords[2:5]))
            if len(keywords) > 5:
                phrases.append(' '.join(keywords[-3:]))

        # 2. 重要关键词对组合（长度>2的词优先）
        important_keywords = [kw for kw in keywords if len(kw) > 2]
        if len(important_keywords) >= 2:
            phrases.append(' '.join(important_keywords[:2]))

        # 3. 如果有人名+事件的组合
        for i, kw1 in enumerate(keywords):
            for kw2 in keywords[i+1:]:
                if len(kw1) > 1 and len(kw2) > 1:
                    combined = f"{kw1} {kw2}"
                    if len(combined) > 4 and combined not in phrases:
                        phrases.append(combined)
                        if len(phrases) >= 4:  # 限制短语数量
                            break
            if len(phrases) >= 4:
                break

        # 去重并返回
        return list(dict.fromkeys(phrases))  # 保持顺序的去重
    
    def search_by_queries(self, search_queries: List[str], top_k: int = 5,
                         threshold: float = 0.3) -> List[Dict]:
        """
        基于搜索查询词搜索相关新闻

        Args:
            search_queries: 搜索查询词列表
            top_k: 每个查询返回的结果数量
            threshold: 相似度阈值

        Returns:
            合并后的搜索结果列表
        """
        if not search_queries:
            return []

        print(f"🔍 执行多查询搜索: {len(search_queries)} 个查询")

        all_results = []
        seen_doc_ids = set()

        for i, query in enumerate(search_queries, 1):
            print(f"   查询 {i}: {query}")

            # 使用现有的搜索方法
            results = self.search(query, top_k=top_k, threshold=threshold)

            # 去重并添加到结果中
            for result in results:
                doc_id = result.get('doc_id', result.get('id', ''))
                if doc_id not in seen_doc_ids:
                    seen_doc_ids.add(doc_id)
                    result['query_source'] = query  # 记录来源查询
                    all_results.append(result)

        # 按相似度排序
        all_results.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        print(f"✅ 多查询搜索完成，共找到 {len(all_results)} 条去重后的新闻")
        return all_results
    
    def search_for_topic(self, topic_analysis: Dict[str, Any], 
                        max_results: int = 20) -> Dict[str, Any]:
        """
        为话题搜索相关新闻
        
        Args:
            topic_analysis: 话题分析结果
            max_results: 最大结果数量
            
        Returns:
            搜索结果汇总
        """
        print(f"🔍 为话题搜索相关新闻: {topic_analysis.get('original_topic', {}).get('merged_title', '未知话题')}")
        
        search_results = {
            'topic_title': topic_analysis.get('original_topic', {}).get('merged_title', ''),
            'search_time': datetime.now().isoformat(),
            'total_found': 0,
            'news_items': [],
            'search_summary': {
                'keywords_search': 0,
                'queries_search': 0,
                'unique_news': 0
            }
        }
        
        try:
            # 准备并发搜索任务
            search_tasks = []

            # 优先使用search_queries（语义更完整，效果更好）
            search_queries = topic_analysis.get('search_queries', [])
            if search_queries:
                search_tasks.append({
                    'type': 'queries',
                    'data': search_queries,
                    'params': {'top_k': 5, 'threshold': 0.5}  # 提高阈值到0.5，增加数量
                })

            # 关键词搜索作为补充（降级搜索）
            keywords = topic_analysis.get('keywords', [])
            if keywords:
                search_tasks.append({
                    'type': 'keywords',
                    'data': keywords,
                    'params': {'top_k': max_results // 3, 'threshold': 0.4}  # 稍低阈值作为补充
                })

            if search_tasks:
                # 并发执行搜索任务
                all_results = self._execute_search_tasks_concurrent(search_tasks)

                # 统计结果
                for task_type, results in all_results.items():
                    if task_type == 'keywords':
                        search_results['search_summary']['keywords_search'] = len(results)
                        search_results['news_items'].extend(results)
                    elif task_type == 'queries':
                        search_results['search_summary']['queries_search'] = len(results)
                        search_results['news_items'].extend(results)
            
            # 3. 去重和排序
            unique_results = self._deduplicate_results(search_results['news_items'])
            unique_results = unique_results[:max_results]  # 限制最大数量

            search_results['news_items'] = unique_results
            search_results['total_found'] = len(unique_results)
            search_results['search_summary']['unique_news'] = len(unique_results)

            # 4. 如果搜索结果太少，启用降级搜索策略
            if len(unique_results) < 3:
                print(f"⚠️ 搜索结果较少({len(unique_results)}条)，启用降级搜索...")
                fallback_results = self._fallback_search(topic_analysis, max_results)

                # 合并降级搜索结果
                for result in fallback_results:
                    if result.get('doc_id') not in [r.get('doc_id') for r in unique_results]:
                        unique_results.append(result)

                search_results['news_items'] = unique_results[:max_results]
                search_results['total_found'] = len(search_results['news_items'])
                search_results['search_summary']['fallback_search'] = len(fallback_results)
                print(f"   降级搜索: {len(fallback_results)} 条")

            print(f"✅ 话题新闻搜索完成:")
            print(f"   查询词搜索: {search_results['search_summary']['queries_search']} 条")
            print(f"   关键词搜索: {search_results['search_summary']['keywords_search']} 条")
            if 'fallback_search' in search_results['search_summary']:
                print(f"   降级搜索: {search_results['search_summary']['fallback_search']} 条")
            print(f"   去重后总计: {search_results['total_found']} 条")

            return search_results
            
        except Exception as e:
            print(f"❌ 话题新闻搜索失败: {e}")
            search_results['error'] = str(e)
            return search_results

    def _execute_search_tasks_concurrent(self, search_tasks: List[Dict]) -> Dict[str, List]:
        """
        并发执行搜索任务

        Args:
            search_tasks: 搜索任务列表

        Returns:
            按任务类型分组的搜索结果
        """
        results = {}

        def execute_single_task(task):
            """执行单个搜索任务"""
            task_type = task['type']
            data = task['data']
            params = task['params']

            try:
                if task_type == 'keywords':
                    print(f"   🔑 基于关键词搜索...")
                    return task_type, self.search_by_keywords(
                        keywords=data,
                        top_k=params['top_k'],
                        threshold=params['threshold']
                    )
                elif task_type == 'queries':
                    print(f"   🔍 基于搜索查询词搜索...")
                    return task_type, self.search_by_queries(
                        search_queries=data,
                        top_k=params['top_k'],
                        threshold=params['threshold']
                    )
                else:
                    return task_type, []

            except Exception as e:
                print(f"❌ {task_type} 搜索失败: {e}")
                return task_type, []

        # 使用线程池并发执行（针对4C16G服务器优化为4个线程）
        max_workers = min(4, len(search_tasks))
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_task = {executor.submit(execute_single_task, task): task for task in search_tasks}

            # 收集结果
            for future in as_completed(future_to_task):
                try:
                    task_type, task_results = future.result()
                    results[task_type] = task_results
                except Exception as e:
                    print(f"❌ 获取搜索任务结果失败: {e}")

        return results

    def _fallback_search(self, topic_analysis: Dict[str, Any], max_results: int = 10) -> List[Dict]:
        """降级搜索策略：当主要搜索结果不足时使用"""
        print("🔄 执行降级搜索策略...")

        fallback_results = []

        # 策略1：使用话题标题直接搜索（降低阈值）
        topic_title = topic_analysis.get('original_topic', {}).get('merged_title', '')
        if topic_title:
            print(f"   📝 使用话题标题搜索: {topic_title}")
            results = self.search(topic_title, top_k=5, threshold=0.3)  # 降低阈值
            fallback_results.extend(results)

        # 策略2：使用分类进行宽泛搜索
        category = topic_analysis.get('original_topic', {}).get('category', '')
        if category and len(fallback_results) < 3:
            print(f"   🏷️ 使用分类搜索: {category}")
            # 根据分类构建宽泛搜索词
            category_queries = {
                '社会': ['社会新闻', '社会事件', '民生新闻'],
                '国际': ['国际新闻', '国际事件', '国外新闻'],
                '科技': ['科技新闻', '技术发展', '科技动态'],
                '财经': ['财经新闻', '经济动态', '金融新闻'],
                '娱乐': ['娱乐新闻', '明星动态', '娱乐圈'],
                '体育': ['体育新闻', '体育赛事', '运动'],
                '时政': ['时政新闻', '政治新闻', '政策']
            }

            if category in category_queries:
                for query in category_queries[category][:2]:  # 只用前2个
                    results = self.search(query, top_k=2, threshold=0.25)  # 更低阈值
                    fallback_results.extend(results)

        # 策略3：使用关键实体搜索
        entities = topic_analysis.get('related_entities', {})
        if len(fallback_results) < 5:
            for entity_type, entity_list in entities.items():
                if entity_list and len(fallback_results) < 8:
                    for entity in entity_list[:2]:  # 每类实体最多2个
                        if len(entity) > 2:  # 过滤太短的实体
                            print(f"   👤 使用实体搜索: {entity}")
                            results = self.search(entity, top_k=2, threshold=0.3)
                            fallback_results.extend(results)

        # 去重
        seen_ids = set()
        unique_fallback = []
        for result in fallback_results:
            doc_id = result.get('doc_id', result.get('id', ''))
            if doc_id not in seen_ids:
                seen_ids.add(doc_id)
                result['search_type'] = 'fallback'
                unique_fallback.append(result)

        return unique_fallback[:max_results]

    def _deduplicate_results(self, results: List[Dict]) -> List[Dict]:
        """去重搜索结果"""
        seen_doc_ids = set()
        unique_results = []
        
        for result in results:
            doc_id = result['doc_id']
            if doc_id not in seen_doc_ids:
                seen_doc_ids.add(doc_id)
                unique_results.append(result)
        
        # 按相似度排序
        unique_results.sort(key=lambda x: x['similarity'], reverse=True)
        return unique_results
    
    def display_search_results(self, results: List[Dict], max_display: int = 10):
        """显示搜索结果"""
        if not results:
            print("❌ 未找到相关新闻")
            return
        
        print(f"\n📰 搜索结果 (显示前 {min(len(results), max_display)} 条):")
        print("-" * 80)
        
        for i, result in enumerate(results[:max_display], 1):
            metadata = result['metadata']
            similarity = result['similarity']
            
            print(f"\n{i}. 📰 {metadata.get('title', '无标题')}")
            print(f"   🎯 相似度: {similarity:.3f}")
            print(f"   🏷️  分类: {metadata.get('category', '未知')} | 📰 媒体: {metadata.get('source', '未知')}")
            print(f"   📅 时间: {metadata.get('publish_time', '未知')}")
            
            # 显示内容摘要
            content = metadata.get('content', '')
            if content:
                summary = content[:100] + "..." if len(content) > 100 else content
                print(f"   📝 内容: {summary}")
            
            if 'query_source' in result:
                print(f"   🔍 匹配查询: {result['query_source']}")
            
            print("-" * 80)
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        if not self.db:
            print("⚠️ 向量数据库未初始化")
            return

        try:
            stats = self.db.get_stats()

            print("\n📊 新闻数据库统计信息")
            print("=" * 50)
            print(f"总文档数: {stats.get('total_documents', 0)}")
            print(f"向量维度: {stats.get('vector_dimension', 0)}")
            print(f"存储路径: {stats.get('storage_path', 'N/A')}")
            print(f"总搜索次数: {stats.get('total_searches', 0)}")

            if stats.get('last_updated'):
                print(f"最后更新: {stats['last_updated']}")

            # 分类分布
            categories = stats.get('categories', {})
            if categories:
                print(f"\n📈 分类分布:")
                for category, count in categories.items():
                    print(f"   {category}: {count} 条")

            # 来源分布
            sources = stats.get('sources', {})
            if sources:
                print(f"\n📰 来源分布:")
                for source, count in sources.items():
                    print(f"   {source}: {count} 条")

        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")


if __name__ == "__main__":
    # 测试新闻搜索引擎
    search_engine = NewsSearchEngine()
    
    # 显示统计信息
    search_engine.get_database_stats()
    
    # 测试搜索
    test_keywords = ["人工智能", "技术发展"]
    results = search_engine.search_by_keywords(test_keywords, top_k=5)
    search_engine.display_search_results(results)
