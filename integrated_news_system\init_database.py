#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
创建数据库和必要的数据表结构
"""

import sys
import os
from database_manager import DatabaseManager, init_database
from config import get_database_config, print_config_status


def main():
    """主函数"""
    print("🚀 集成新闻系统 - 数据库初始化")
    print("=" * 60)
    
    # 检查配置状态
    print("📋 检查配置状态...")
    if not print_config_status():
        print("\n❌ 配置检查失败，请先配置.env文件")
        return False
    
    # 显示数据库配置
    db_config = get_database_config()
    print(f"\n🗄️ 数据库配置信息:")
    print(f"   主机: {db_config['host']}:{db_config['port']}")
    print(f"   用户: {db_config['user']}")
    print(f"   数据库: {db_config['database']}")
    print(f"   字符集: {db_config['charset']}")
    
    # 确认初始化
    confirm = input(f"\n❓ 确认要初始化数据库 '{db_config['database']}' 吗？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 用户取消初始化")
        return False
    
    # 执行初始化
    print(f"\n🚀 开始初始化数据库...")
    try:
        if init_database():
            print(f"\n✅ 数据库初始化成功！")
            
            # 测试连接和显示统计
            test_connection()
            return True
        else:
            print(f"\n❌ 数据库初始化失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 初始化过程中出现错误: {e}")
        return False


def test_connection():
    """测试数据库连接并显示统计信息"""
    print(f"\n🔍 测试数据库连接...")
    
    try:
        db_manager = DatabaseManager()
        
        if db_manager.connect():
            print(f"✅ 数据库连接测试成功")
            
            # 获取并显示统计信息
            stats = db_manager.get_database_stats()
            
            print(f"\n📊 数据库统计信息:")
            print(f"   文章总数: {stats.get('total_articles', 0)}")
            print(f"   话题总数: {stats.get('total_topics', 0)}")
            
            if stats.get('articles'):
                print(f"   文章状态分布:")
                for status, count in stats['articles'].items():
                    print(f"     {status}: {count}")
            
            if stats.get('topics'):
                print(f"   话题状态分布:")
                for status, count in stats['topics'].items():
                    print(f"     {status}: {count}")
            
            # 显示表结构信息
            print(f"\n📋 数据表信息:")
            tables = ['articles', 'topic_processing_records', 'news_retrieval_records', 'system_logs']
            
            for table in tables:
                try:
                    db_manager.cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    result = db_manager.cursor.fetchone()
                    count = result['count'] if result else 0
                    print(f"   {table}: {count} 条记录")
                except Exception as e:
                    print(f"   {table}: 查询失败 - {e}")
            
            db_manager.disconnect()
            
        else:
            print(f"❌ 数据库连接测试失败")
            
    except Exception as e:
        print(f"❌ 连接测试出现错误: {e}")


def reset_database():
    """重置数据库（危险操作）"""
    print("⚠️ 危险操作：重置数据库")
    print("这将删除所有现有数据！")
    
    confirm1 = input("确认要重置数据库吗？输入 'RESET' 确认: ").strip()
    if confirm1 != 'RESET':
        print("❌ 操作已取消")
        return False
    
    confirm2 = input("再次确认，这将不可恢复！输入 'YES' 确认: ").strip()
    if confirm2 != 'YES':
        print("❌ 操作已取消")
        return False
    
    try:
        db_config = get_database_config()
        
        # 连接到MySQL服务器（不指定数据库）
        import mysql.connector
        connection = mysql.connector.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            charset=db_config['charset']
        )
        
        cursor = connection.cursor()
        
        # 删除数据库
        cursor.execute(f"DROP DATABASE IF EXISTS {db_config['database']}")
        print(f"✅ 数据库 {db_config['database']} 已删除")
        
        cursor.close()
        connection.close()
        
        # 重新初始化
        print(f"🚀 重新初始化数据库...")
        return init_database()
        
    except Exception as e:
        print(f"❌ 重置数据库失败: {e}")
        return False


def show_help():
    """显示帮助信息"""
    help_text = """
🔧 集成新闻系统 - 数据库初始化工具

使用方法:
  python init_database.py [选项]

选项:
  无参数        - 正常初始化数据库
  --test       - 仅测试数据库连接
  --reset      - 重置数据库（危险操作）
  --help       - 显示此帮助信息

示例:
  python init_database.py           # 初始化数据库
  python init_database.py --test    # 测试连接
  python init_database.py --reset   # 重置数据库

注意事项:
  1. 首次运行前请确保已配置 .env 文件
  2. 确保MySQL服务已启动
  3. 确保用户有创建数据库的权限
  4. --reset 操作将删除所有数据，请谨慎使用
"""
    print(help_text)


if __name__ == "__main__":
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        
        if arg in ['--help', '-h']:
            show_help()
        elif arg == '--test':
            print("🔍 数据库连接测试")
            print("=" * 30)
            test_connection()
        elif arg == '--reset':
            if reset_database():
                print("✅ 数据库重置完成")
            else:
                print("❌ 数据库重置失败")
        else:
            print(f"❌ 未知参数: {arg}")
            print("使用 --help 查看帮助信息")
    else:
        # 正常初始化
        if main():
            print(f"\n🎉 数据库初始化完成！")
            print(f"💡 现在可以运行主流水线脚本了")
        else:
            print(f"\n❌ 数据库初始化失败")
            sys.exit(1)
