# 新闻爬虫使用说明

## 📊 数据量设定规则

### **各平台数据量来源**

| 平台 | 数据量控制方式 | 默认数量 | 可调整范围 |
|------|---------------|----------|------------|
| **微博热搜** | 平台返回全部 | ~50条 | 无法调整 |
| **百度热搜** | 平台返回全部 | ~50条 | 无法调整 |
| **知乎热榜** | API参数控制 | 20条 | 1-50条 |
| **今日头条** | 平台返回全部 | ~50条 | 无法调整 |
| **B站热搜** | API参数控制 | 30条 | 1-50条 |
| **GitHub趋势** | API参数控制 | 20条 | 1-100条 |
| **V2EX热门** | 代码手动限制 | 20条 | 任意数量 |
| **IT之家** | 代码手动限制 | 20条 | 任意数量 |

### **数量调整方法**

#### 方法1: 修改配置文件 (推荐)

编辑 `crawler_config.py` 文件：

```python
# 各平台数据获取数量配置
PLATFORM_LIMITS = {
    'zhihu': 50,        # 知乎热榜数量 (最大50)
    'bilibili': 50,     # B站热搜数量 (最大50)  
    'github': 100,      # GitHub趋势数量 (最大100)
    'v2ex': 100,        # V2EX热门数量 (任意)
    'ithome': 100,      # IT之家新闻数量 (任意)
}
```

#### 方法2: 关闭某些平台

```python
# 平台开关配置
PLATFORM_ENABLED = {
    'weibo': True,      # 微博热搜
    'baidu': False,     # 关闭百度热搜
    'zhihu': True,      # 知乎热榜
    'toutiao': False,   # 关闭今日头条
    'bilibili': True,   # B站热搜
    'github': True,     # GitHub趋势
    'v2ex': True,       # V2EX热门
    'ithome': False,    # 关闭IT之家
}
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 基本运行
```bash
python news_crawler.py
```

### 3. 使用代理
编辑 `crawler_config.py`：
```python
PROXY_CONFIG = {
    'enabled': True,
    'http_proxy': 'http://127.0.0.1:7890',
    'https_proxy': 'http://127.0.0.1:7890',
}
```

### 4. 调整网络参数
```python
NETWORK_CONFIG = {
    'timeout': 15,          # 请求超时时间（秒）
    'max_retries': 5,       # 最大重试次数
    'retry_delay': 2,       # 重试间隔（秒）
    'request_interval': 2,  # 请求间隔（秒）
}
```

## 📈 输出示例

```
🚀 开始抓取新闻数据...
==================================================
✅ 微博热搜: 获取到 50 条数据
✅ 百度热搜: 获取到 50 条数据
✅ 知乎热榜: 获取到 50 条数据    # 调整后
✅ 今日头条: 获取到 50 条数据
✅ B站热搜: 获取到 50 条数据    # 调整后
✅ GitHub趋势: 获取到 100 条数据 # 调整后
✅ V2EX热门: 获取到 100 条数据  # 调整后
✅ IT之家: 获取到 100 条数据    # 调整后
==================================================
📊 抓取完成!
总计获取 550 条新闻数据
💾 数据已保存到: news_data_20250706_214530.json
```

## 🔧 高级配置

### 自定义输出格式
```python
OUTPUT_CONFIG = {
    'save_to_file': True,           # 是否保存到文件
    'show_summary': True,           # 是否显示摘要
    'summary_preview_count': 10,    # 摘要中每个平台显示的条目数
    'filename_format': 'news_{timestamp}.json',  # 自定义文件名
}
```

### 单独获取某个平台
```python
from news_crawler import NewsCrawler

crawler = NewsCrawler()

# 只获取知乎热榜
zhihu_data = crawler.zhihu_hot_list()
print(f"获取到 {len(zhihu_data)} 条知乎数据")

# 只获取微博热搜
weibo_data = crawler.weibo_hot_search()
print(f"获取到 {len(weibo_data)} 条微博数据")
```

## ❓ 常见问题

**Q: 为什么某些平台获取到0条数据？**
A: 可能的原因：
- 网络连接问题
- 平台API发生变化
- 需要使用代理访问
- 平台临时限制访问

**Q: 如何提高获取成功率？**
A: 建议：
- 使用稳定的网络环境
- 配置合适的代理
- 增加重试次数和超时时间
- 适当增加请求间隔

**Q: 数据格式是什么？**
A: 统一的JSON格式，每条新闻包含：
- `id`: 唯一标识
- `title`: 标题
- `url`: 链接地址
- `extra`: 额外信息（图标、热度等）
