#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证脚本
验证.env配置是否正确，并测试各项服务连接
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config import print_config_status, get_llm_config, get_embedding_config, get_database_config
from llm_client import LLMClient
from database_manager import DatabaseManager


def test_llm_connection():
    """测试LLM连接"""
    print("\n🤖 测试LLM连接...")
    try:
        client = LLMClient()
        
        # 获取状态
        status = client.get_status()
        print(f"   总API Key数: {status['total_keys']}")
        print(f"   可用API Key数: {status['available_keys']}")
        print(f"   模型: {status['model']}")
        
        # 简单测试
        response = client.chat("请回答：1+1等于几？", max_tokens=50)
        if response:
            print(f"   ✅ LLM响应测试成功: {response[:30]}...")
            return True
        else:
            print("   ❌ LLM无响应")
            return False
            
    except Exception as e:
        print(f"   ❌ LLM连接失败: {e}")
        return False


def test_database_connection():
    """测试数据库连接"""
    print("\n🗄️ 测试数据库连接...")
    try:
        db_manager = DatabaseManager()
        
        # 尝试连接
        if db_manager.connect():
            print("   ✅ 数据库连接成功")
            
            # 获取统计信息
            stats = db_manager.get_database_stats()
            print(f"   📊 数据库统计:")
            print(f"      文章总数: {stats.get('total_articles', 0)}")
            print(f"      话题总数: {stats.get('total_topics', 0)}")
            
            db_manager.disconnect()
            return True
        else:
            print("   ❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库连接异常: {e}")
        return False


def test_vector_database():
    """测试向量数据库"""
    print("\n🔤 测试向量数据库...")
    try:
        from vector_database import VectorDatabase
        
        # 检查向量数据库路径
        config = get_embedding_config()
        vector_path = os.environ.get('VECTOR_DB_PATH', '../新闻爬取/news_vectors')
        
        if os.path.exists(vector_path):
            print(f"   ✅ 向量数据库路径存在: {vector_path}")
            
            # 初始化向量数据库
            vector_db = VectorDatabase(vector_path)
            stats = vector_db.get_stats()
            
            print(f"   📊 向量数据库统计:")
            print(f"      总文档数: {stats['total_documents']}")
            print(f"      向量维度: {stats['vector_dimension']}")
            print(f"      总搜索次数: {stats['total_searches']}")
            
            return True
        else:
            print(f"   ⚠️ 向量数据库路径不存在: {vector_path}")
            print("   💡 这是正常的，系统会自动创建")
            return True
            
    except Exception as e:
        print(f"   ❌ 向量数据库测试失败: {e}")
        return False


def test_news_crawler():
    """测试新闻爬虫"""
    print("\n📰 测试新闻爬虫...")
    try:
        from news_crawler import NewsCrawler
        
        crawler = NewsCrawler()
        
        # 测试单个平台
        print("   🔍 测试微博热搜...")
        weibo_news = crawler.weibo_hot_search()
        
        if weibo_news and len(weibo_news) > 0:
            print(f"   ✅ 成功获取 {len(weibo_news)} 条微博热搜")
            print(f"   📋 示例: {weibo_news[0]['title']}")
            return True
        else:
            print("   ⚠️ 未获取到微博热搜数据")
            return False
            
    except Exception as e:
        print(f"   ❌ 新闻爬虫测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 集成新闻系统配置验证")
    print("=" * 50)
    
    # 检查.env文件
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ 未找到.env文件")
        print("💡 请确保.env文件存在于当前目录")
        return False
    
    print("✅ 找到.env配置文件")
    
    # 基础配置检查
    print("\n📋 基础配置检查:")
    config_ok = print_config_status()
    
    if not config_ok:
        print("\n❌ 基础配置检查失败")
        return False
    
    # 详细测试
    tests = [
        ("LLM连接", test_llm_connection),
        ("数据库连接", test_database_connection),
        ("向量数据库", test_vector_database),
        ("新闻爬虫", test_news_crawler)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"   ❌ {test_name}测试异常: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: ✅ 通过: {passed} ❌ 失败: {failed}")
    
    if failed == 0:
        print("\n🎉 所有测试通过！系统配置正确，可以开始使用")
        print("\n🚀 下一步:")
        print("   1. 初始化数据库: python init_database.py")
        print("   2. 运行系统: python quick_start.py")
        return True
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，请检查配置")
        print("\n💡 常见问题:")
        print("   - 确保MySQL服务已启动")
        print("   - 检查API密钥是否有效")
        print("   - 确认网络连接正常")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
