# 澎湃新闻板块爬虫标准化开发流程

## 概述
本文档提供了一套标准化的流程，用于快速开发澎湃新闻任意板块的爬虫。通过遵循这个流程，可以在30分钟内完成一个新板块爬虫的开发和测试。

## 开发流程总览

### 第1步：信息收集 (5分钟)
1. **获取板块基本信息**
2. **抓包分析API接口**
3. **验证HTML结构**

### 第2步：代码开发 (15分钟)
1. **复制模板代码**
2. **修改配置参数**
3. **调整板块特定设置**

### 第3步：测试验证 (10分钟)
1. **运行测试**
2. **验证数据质量**
3. **确认功能完整性**

---

## 详细开发步骤

### 第1步：信息收集

#### 1.1 获取板块基本信息
访问目标板块页面，记录以下信息：

```
板块名称: [如：科技、体育、文化等]
板块URL: https://m.thepaper.cn/channel_[板块ID]
板块ID: [从URL中提取的数字ID]
板块描述: [用于代码注释]
```

**示例：**
```
板块名称: 财经
板块URL: https://m.thepaper.cn/channel_25951
板块ID: 25951
板块描述: 财经新闻和商业资讯
```

#### 1.2 抓包分析API接口
使用浏览器开发者工具抓包：

1. **打开开发者工具** (F12)
2. **切换到Network标签**
3. **刷新页面或滚动加载更多**
4. **找到API请求**：`https://api.thepaper.cn/contentapi/nodeCont/getByChannelId`
5. **记录请求参数**：

```json
{
    "channelId": "[板块ID]",
    "excludeContIds": [],
    "listRecommendIds": [],
    "pageSize": 10,
    "startTime": [时间戳],
    "pageNum": 2
}
```

6. **验证响应格式**：确认返回数据结构与标准格式一致

#### 1.3 验证HTML结构
检查首页HTML结构是否使用标准容器：
- 新闻容器：`div.index_wrapper__9rz3z`
- 标题选择器：`h3.index_title__aGAqD`
- 来源选择器：`div.index_extra__M2kfN`

### 第2步：代码开发

#### 2.1 复制模板代码
使用财经板块作为标准模板：

```bash
cp thepaper_finance_crawler.py thepaper_[新板块名]_crawler.py
```

#### 2.2 修改配置参数
在新文件中进行以下替换：

**2.2.1 基本配置替换**
```python
# 原代码
class ThepaperFinanceCrawler:
    def __init__(self):
        self.base_url = "https://m.thepaper.cn/channel_25951"
        # ...

# 修改为
class Thepaper[板块名]Crawler:
    def __init__(self):
        self.base_url = "https://m.thepaper.cn/channel_[新板块ID]"
        # ...
```

**2.2.2 API参数替换**
```python
# 原代码
payload = {
    "channelId": "25951",  # 财经板块的channelId
    # ...
}

# 修改为
payload = {
    "channelId": "[新板块ID]",  # [新板块名]板块的channelId
    # ...
}
```

**2.2.3 标识符替换**
```python
# 原代码
'channel': 'finance'

# 修改为
'channel': '[新板块英文名]'
```

**2.2.4 日志信息替换**
将所有包含"财经"的日志信息替换为新板块名称：
```python
# 原代码
print("正在获取财经板块首页HTML数据...")

# 修改为
print("正在获取[新板块名]板块首页HTML数据...")
```

#### 2.3 文件名和函数名替换
使用IDE的全局替换功能：
- `finance` → `[新板块英文名]`
- `财经` → `[新板块中文名]`
- `Finance` → `[新板块英文名首字母大写]`

### 第3步：测试验证

#### 3.1 运行测试
```bash
python thepaper_[新板块名]_crawler.py
```

选择"2"（不获取详细内容）进行快速测试。

#### 3.2 验证数据质量
检查输出结果：
- ✅ 成功获取首页HTML数据
- ✅ API请求正常响应
- ✅ 数据去重功能正常
- ✅ 时间过滤功能正常
- ✅ CSV文件保存成功

#### 3.3 确认功能完整性
检查生成的CSV文件：
- 数据字段完整
- 新闻标题和链接正确
- 时间信息准确
- 来源信息正确
- 无重复数据

---

## 标准化模板

### 快速替换清单
创建新板块爬虫时，需要替换以下内容：

| 项目 | 原值 | 新值 | 示例 |
|------|------|------|------|
| 类名 | `ThepaperFinanceCrawler` | `Thepaper[板块名]Crawler` | `ThepaperTechCrawler` |
| 板块URL | `channel_25951` | `channel_[新ID]` | `channel_25952` |
| channelId | `"25951"` | `"[新ID]"` | `"25952"` |
| channel标识 | `'finance'` | `'[英文名]'` | `'tech'` |
| 中文名称 | `财经` | `[新板块名]` | `科技` |
| 英文名称 | `finance` | `[英文名]` | `tech` |
| Referer | `channel_25951` | `channel_[新ID]` | `channel_25952` |

### 标准文件命名规范
```
thepaper_[英文名]_crawler.py
```

**示例：**
- 科技板块：`thepaper_tech_crawler.py`
- 体育板块：`thepaper_sports_crawler.py`
- 文化板块：`thepaper_culture_crawler.py`

### 标准输出文件命名
```
thepaper_[英文名]_final_YYYYMMDD_HHMMSS.csv
```

---

## 常见板块信息参考

### 已实现板块
| 板块名称 | 板块ID | 英文标识 | 文件名 |
|----------|--------|----------|---------|
| 时事 | 25950 | news | thepaper_news_crawler.py |
| 国际 | 122908 | international | thepaper_international_crawler.py |
| 财经 | 25951 | finance | thepaper_finance_crawler.py |

### 待实现板块（示例）
| 板块名称 | 预估板块ID | 建议英文标识 | 建议文件名 |
|----------|------------|--------------|------------|
| 科技 | 25XXX | tech | thepaper_tech_crawler.py |
| 体育 | 25XXX | sports | thepaper_sports_crawler.py |
| 文化 | 25XXX | culture | thepaper_culture_crawler.py |
| 教育 | 25XXX | education | thepaper_education_crawler.py |

---

## 质量检查清单

### 开发完成后检查
- [ ] 类名正确修改
- [ ] 所有URL和ID正确替换
- [ ] 日志信息使用正确的板块名称
- [ ] channel标识符正确设置
- [ ] 文件名符合命名规范

### 测试完成后检查
- [ ] 程序能正常启动
- [ ] 能获取到首页HTML数据
- [ ] API请求返回正确数据
- [ ] 数据去重功能正常
- [ ] 时间过滤功能正常
- [ ] CSV文件格式正确
- [ ] 数据内容准确完整

### 数据质量检查
- [ ] 新闻标题完整
- [ ] 链接地址正确
- [ ] 发布时间准确
- [ ] 来源信息正确
- [ ] 无重复记录
- [ ] 字段映射正确

---

## 故障排除

### 常见问题及解决方案

#### 1. API返回空数据
**原因：** channelId错误
**解决：** 重新抓包确认正确的板块ID

#### 2. HTML解析失败
**原因：** 页面结构不同
**解决：** 检查该板块是否使用标准HTML结构

#### 3. 时间过滤异常
**原因：** 时间戳格式不同
**解决：** 检查API返回的时间字段格式

#### 4. 重复数据过多
**原因：** excludeContIds参数未正确使用
**解决：** 确认API参数设置正确

---

## 总结

通过遵循这个标准化流程，开发新板块爬虫的时间可以控制在30分钟以内：

1. **信息收集 (5分钟)**：快速获取必要的配置信息
2. **代码开发 (15分钟)**：基于模板进行标准化替换
3. **测试验证 (10分钟)**：确保功能正常和数据质量

这个流程确保了：
- **一致性**：所有板块爬虫使用相同的架构和功能
- **可靠性**：基于已验证的模板，减少错误
- **效率**：标准化流程大幅提升开发速度
- **可维护性**：统一的代码结构便于后续维护

**下次开发新板块时，只需要按照这个文档的步骤操作即可！**
