#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环球网台海新闻爬虫
支持爬取台海板块文章列表和详细内容
"""

import requests
import json
import time
import csv
from datetime import datetime
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from tqdm import tqdm


class HuanqiuTaiwanSpider:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.base_url = "https://taiwan.huanqiu.com"
        self.api_url = "https://taiwan.huanqiu.com/api/list"
        
        # 台海新闻的节点ID
        self.nodes = [
            "/e3pmh1fmg/e3pmh1g6o",
            "/e3pmh1fmg/e3pmt8gfv",
            "/e3pmh1fmg/e3pmt8i3n",
            "/e3pmh1fmg/e3pmt8lic",
            "/e3pmh1fmg/e3pmunk13",
            "/e3pmh1fmg/e3pn6elbj"
        ]
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://taiwan.huanqiu.com/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        
        self.session.headers.update(self.headers)
        
        # 存储数据
        self.articles = []
        
    def get_article_list(self, offset=0, limit=24):
        """
        获取文章列表
        """
        params = {
            'node': ','.join([f'"{node}"' for node in self.nodes]),
            'offset': offset,
            'limit': limit
        }
        
        try:
            response = self.session.get(self.api_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return data.get('list', [])
            
        except requests.RequestException as e:
            print(f"获取文章列表失败: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return []
    
    def get_article_content(self, article_id):
        """
        获取文章详细内容
        """
        article_url = f"https://taiwan.huanqiu.com/article/{article_id}"
        
        try:
            # 随机更换User-Agent
            headers = {
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://taiwan.huanqiu.com/',
                'Connection': 'keep-alive',
            }
            
            response = self.session.get(article_url, headers=headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取文章内容 - 根据HTML结构
            content = ""
            content_div = soup.find('div', class_='content')
            if content_div:
                article_tag = content_div.find('article')
                if article_tag:
                    # 提取所有段落，过滤掉广告等元素
                    paragraphs = article_tag.find_all('p')
                    content_parts = []
                    for p in paragraphs:
                        # 跳过包含广告的段落
                        if p.find('adv-loader'):
                            continue
                        # 跳过图片说明文字
                        if p.find('i', class_='pic-con'):
                            continue
                        
                        text = p.get_text(strip=True)
                        if text and len(text) > 5:  # 过滤掉太短的文本
                            content_parts.append(text)
                    content = '\n\n'.join(content_parts)
            
            # 如果上面的方法没找到内容，尝试其他选择器
            if not content:
                # 尝试其他可能的内容选择器
                selectors = [
                    '.la-content',
                    '.article-content', 
                    '.content article',
                    '.main-content',
                    '[data-type="rtext"]'
                ]
                
                for selector in selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        paragraphs = content_elem.find_all(['p', 'div'])
                        content_parts = []
                        for p in paragraphs:
                            # 跳过广告相关内容
                            if p.find('adv-loader') or p.find('i', class_='pic-con'):
                                continue
                            
                            text = p.get_text(strip=True)
                            if text and len(text) > 10:
                                content_parts.append(text)
                        if content_parts:
                            content = '\n\n'.join(content_parts)
                            break
            
            # 提取发布时间
            publish_time = ""
            time_selectors = [
                '.time',
                'time',
                '.publish-time',
                '.article-time'
            ]
            
            for selector in time_selectors:
                time_elem = soup.select_one(selector)
                if time_elem:
                    publish_time = time_elem.get_text(strip=True)
                    break
            
            # 提取作者/来源
            source = ""
            source_selectors = [
                '.source',
                '.article-source',
                '.author'
            ]
            
            for selector in source_selectors:
                source_elem = soup.select_one(selector)
                if source_elem:
                    source = source_elem.get_text(strip=True)
                    break
            
            return {
                'content': content,
                'publish_time': publish_time,
                'source': source,
                'url': article_url
            }
            
        except requests.RequestException as e:
            print(f"获取文章内容失败 {article_id}: {e}")
            return None
        except Exception as e:
            print(f"解析文章内容失败 {article_id}: {e}")
            return None
    
    def is_today_article(self, ctime):
        """
        判断文章是否为今天发布
        """
        if not ctime or not str(ctime).isdigit():
            return False
        
        try:
            # 时间戳可能是毫秒或秒，先尝试毫秒
            if len(str(ctime)) > 10:
                timestamp = int(ctime) / 1000
            else:
                timestamp = int(ctime)
            
            article_date = datetime.fromtimestamp(timestamp).date()
            today = datetime.now().date()
            
            return article_date == today
        except:
            return False
    
    def crawl_today_pages(self, max_pages=3):
        """
        爬取当天的文章 - 测试版本
        """
        print(f"开始爬取环球网台海新闻（当天文章 - 最多{max_pages}页）...")
        
        today = datetime.now().date()
        offset = 0
        limit = 24
        total_today_articles = 0
        
        for page in range(1, max_pages + 1):
            print(f"\n正在爬取第 {page} 页 (offset: {offset})...")
            
            # 获取文章列表
            article_list = self.get_article_list(offset, limit)
            
            if not article_list:
                print("没有更多文章了")
                break
            
            # 筛选今天的文章
            today_articles = []
            non_today_count = 0
            
            for article in article_list:
                ctime = article.get('ctime', 0)
                if self.is_today_article(ctime):
                    today_articles.append(article)
                else:
                    non_today_count += 1
            
            print(f"本页共 {len(article_list)} 篇文章，其中今日文章 {len(today_articles)} 篇")
            
            if not today_articles:
                print("本页没有今日文章，停止爬取")
                break
            
            # 如果非今日文章过多，可能已经爬取完今日文章
            if non_today_count > len(today_articles) * 2:
                print("非今日文章较多，可能已接近今日文章末尾")
            
            # 爬取今日文章详细内容
            for i, article in enumerate(tqdm(today_articles, desc=f"第{page}页今日文章"), 1):
                article_id = article.get('aid')
                title = article.get('title', '')
                summary = article.get('summary', '')
                
                # 处理时间戳
                ctime = article.get('ctime', 0)
                article_date = "未知日期"
                if ctime and str(ctime).isdigit():
                    try:
                        # 时间戳可能是毫秒或秒，先尝试毫秒
                        if len(str(ctime)) > 10:
                            timestamp = int(ctime) / 1000
                        else:
                            timestamp = int(ctime)
                        
                        article_date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        article_date = "时间解析失败"
                
                print(f"\n正在爬取第 {i} 篇: {title[:50]}...")
                print(f"文章ID: {article_id}")
                print(f"发布时间: {article_date}")
                
                if not article_id or article_id == "无ID":
                    print("跳过无效文章ID")
                    continue
                
                # 获取详细内容
                detail = self.get_article_content(article_id)
                
                if detail:
                    article_data = {
                        'id': article_id,
                        'title': title,
                        'summary': summary,
                        'content': detail['content'],
                        'source': detail['source'] or article.get('source', {}).get('name', ''),
                        'publish_time': detail['publish_time'] or article_date,
                        'url': detail['url'],
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    # 显示内容预览
                    content_preview = detail['content'][:200] + "..." if len(detail['content']) > 200 else detail['content']
                    print(f"内容预览: {content_preview}")
                    print(f"内容长度: {len(detail['content'])} 字符")
                    
                    self.articles.append(article_data)
                    total_today_articles += 1
                else:
                    print("获取文章内容失败")
                
                # 避免请求过快
                time.sleep(1.5)
            
            # 如果这页的今日文章数量很少，可能已经接近末尾
            if len(today_articles) < limit // 3:
                print("今日文章数量较少，可能已接近末尾")
                # 继续爬取几页确认
                if page > 2:
                    break
            
            offset += limit
            
            # 页面间隔
            time.sleep(2)
        
        print(f"\n爬取完成！共获取 {total_today_articles} 篇今日文章")
        return self.articles
    
    def save_to_csv(self, filename=None):
        """
        保存到CSV文件
        """
        if not filename:
            filename = f"huanqiu_taiwan_news_test_{datetime.now().strftime('%Y%m%d_%H%M')}.csv"
        
        if not self.articles:
            print("没有数据可保存")
            return
        
        # 手动写CSV文件
        with open(filename, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            
            # 写入表头
            if self.articles:
                headers = list(self.articles[0].keys())
                writer.writerow(headers)
                
                # 写入数据
                for article in self.articles:
                    row = [article.get(header, '') for header in headers]
                    writer.writerow(row)
        
        print(f"数据已保存到: {filename}")
    
    def save_to_json(self, filename=None):
        """
        保存到JSON文件
        """
        if not filename:
            filename = f"huanqiu_taiwan_news_test_{datetime.now().strftime('%Y%m%d_%H%M')}.json"
        
        if not self.articles:
            print("没有数据可保存")
            return
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.articles, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {filename}")


def main():
    """
    主函数 - 测试版本
    """
    spider = HuanqiuTaiwanSpider()
    
    # 爬取当天的文章进行测试
    articles = spider.crawl_today_pages(max_pages=3)
    
    if articles:
        # 保存数据
        spider.save_to_csv()
        spider.save_to_json()
        
        # 显示统计信息
        print(f"\n=== 爬取统计 ===")
        print(f"总文章数: {len(articles)}")
        if articles:
            avg_title_len = sum(len(a['title']) for a in articles) / len(articles)
            valid_content = [a for a in articles if a['content']]
            if valid_content:
                avg_content_len = sum(len(a['content']) for a in valid_content) / len(valid_content)
                print(f"平均标题长度: {avg_title_len:.1f}")
                print(f"平均内容长度: {avg_content_len:.1f}")
                print(f"有效内容文章数: {len(valid_content)}")
        
        # 显示前3篇文章的详细信息
        print(f"\n=== 前3篇文章详情 ===")
        for i, article in enumerate(articles[:3], 1):
            print(f"\n{i}. {article['title']}")
            print(f"   来源: {article['source']}")
            print(f"   时间: {article['publish_time']}")
            print(f"   链接: {article['url']}")
            print(f"   摘要: {article['summary'][:100]}...")
            print(f"   内容: {article['content'][:200]}...")


if __name__ == "__main__":
    main()
