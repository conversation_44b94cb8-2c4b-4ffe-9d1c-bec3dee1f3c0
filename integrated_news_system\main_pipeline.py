#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成新闻系统主流水线
整合热点爬取、话题合并、新闻检索、文章生成的完整流程
"""

import sys
import os
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入各个模块
from config import print_config_status, get_system_config
from database_manager import DatabaseManager
from news_crawler import NewsCrawler
from topic_merger import TopicMerger
# 使用当前目录的分步走版本topic_processor
from topic_processor import TopicProcessor
# 导入报告生成功能
import sys
sys.path.append('../热点新闻标题爬取整合')
from news_intelligence import generate_report
from news_search import NewsSearchEngine
from article_generator import ArticleGenerator


class IntegratedNewsPipeline:
    """集成新闻系统主流水线"""
    
    def __init__(self):
        """初始化流水线"""
        print("🚀 集成新闻系统启动")
        print("=" * 60)
        
        # 检查配置
        if not print_config_status():
            raise Exception("❌ 配置检查失败，请先配置.env文件")
        
        # 初始化各个组件
        self.config = get_system_config()
        self.db_manager = DatabaseManager()
        self.news_crawler = NewsCrawler()
        self.topic_merger = TopicMerger()
        self.topic_processor = TopicProcessor()

        # 初始化向量数据库搜索引擎
        try:
            import os
            vector_db_path = os.environ.get('VECTOR_DB_PATH', '../新闻爬取/news_vectors')
            self.news_search = NewsSearchEngine(vector_db_path)
        except Exception as e:
            print(f"⚠️ 向量数据库初始化失败: {e}")
            print("💡 将使用简化搜索模式")
            self.news_search = None

        self.article_generator = ArticleGenerator()
        
        # 确保目录存在
        self.config.ensure_directories()
        
        print("✅ 所有组件初始化完成")
        print("=" * 60)
    
    def run_full_pipeline(self, max_topics: int = None, max_articles: int = None) -> Dict[str, Any]:
        """
        运行完整流水线

        Args:
            max_topics: 最大处理话题数（None表示处理所有合并后的话题）
            max_articles: 最大生成文章数（None表示为所有话题生成文章）

        Returns:
            流水线执行结果
        """
        pipeline_start_time = time.time()
        
        print(f"🚀 开始执行完整流水线")
        print(f"   话题处理策略: {'处理所有合并话题' if max_topics is None else f'最多处理{max_topics}个话题'}")
        print(f"   文章生成策略: {'为所有话题生成文章' if max_articles is None else f'最多生成{max_articles}篇文章'}")
        print("=" * 60)
        
        pipeline_result = {
            'start_time': datetime.now().isoformat(),
            'steps': {},
            'final_stats': {},
            'errors': []
        }
        
        try:
            # 步骤1: 爬取热点新闻
            print("\n📰 步骤1: 爬取热点新闻")
            print("-" * 40)
            step1_result = self._step1_crawl_news()
            pipeline_result['steps']['step1_crawl'] = step1_result
            
            if not step1_result.get('success', False):
                raise Exception("步骤1失败: 新闻爬取失败")
            
            # 步骤2: 合并相似话题
            print("\n🔄 步骤2: 合并相似话题")
            print("-" * 40)
            step2_result = self._step2_merge_topics(step1_result['news_data'], max_topics)
            pipeline_result['steps']['step2_merge'] = step2_result
            
            if not step2_result.get('success', False):
                raise Exception("步骤2失败: 话题合并失败")
            
            # 步骤3: 处理话题分析
            print("\n🔍 步骤3: 处理话题分析")
            print("-" * 40)
            step3_result = self._step3_process_topics(step2_result['merged_topics'])
            pipeline_result['steps']['step3_process'] = step3_result
            
            if not step3_result.get('success', False):
                raise Exception("步骤3失败: 话题处理失败")
            
            # 步骤4: 搜索相关新闻
            print("\n🔍 步骤4: 搜索相关新闻")
            print("-" * 40)
            step4_result = self._step4_search_news(step3_result['processed_topics'])
            pipeline_result['steps']['step4_search'] = step4_result
            
            if not step4_result.get('success', False):
                raise Exception("步骤4失败: 新闻搜索失败")
            
            # 步骤5: 生成文章
            print("\n📝 步骤5: 生成文章")
            print("-" * 40)
            step5_result = self._step5_generate_articles(
                step3_result['processed_topics'],
                step4_result['search_results'],
                max_articles
            )
            pipeline_result['steps']['step5_generate'] = step5_result
            
            if not step5_result.get('success', False):
                raise Exception("步骤5失败: 文章生成失败")
            
            # 步骤6: 保存到数据库
            print("\n💾 步骤6: 保存到数据库")
            print("-" * 40)
            step6_result = self._step6_save_to_database(step5_result['articles'])
            pipeline_result['steps']['step6_save'] = step6_result
            
            # 计算最终统计
            pipeline_end_time = time.time()
            execution_time = pipeline_end_time - pipeline_start_time
            
            pipeline_result.update({
                'success': True,
                'end_time': datetime.now().isoformat(),
                'execution_time': execution_time,
                'final_stats': {
                    'total_news_crawled': step1_result.get('total_news', 0),
                    'topics_merged': len(step2_result.get('merged_topics', [])),
                    'topics_processed': len(step3_result.get('processed_topics', [])),
                    'articles_generated': len(step5_result.get('articles', [])),
                    'articles_saved': step6_result.get('saved_count', 0)
                }
            })
            
            print("\n" + "=" * 60)
            print("🎉 流水线执行完成!")
            self._print_final_summary(pipeline_result)
            
            return pipeline_result
            
        except Exception as e:
            pipeline_result.update({
                'success': False,
                'error': str(e),
                'end_time': datetime.now().isoformat()
            })
            
            print(f"\n❌ 流水线执行失败: {e}")
            return pipeline_result
    
    def _step1_crawl_news(self, test_mode: bool = False, max_items_per_platform: int = 50) -> Dict[str, Any]:
        """步骤1: 爬取热点新闻"""
        try:
            if test_mode:
                print(f"📰 测试模式: 每个平台最多爬取 {max_items_per_platform} 条")
                # 测试模式：只爬取部分数据
                news_data = {}
                platforms = ['weibo', 'baidu', 'zhihu']  # 只测试3个平台
                for platform in platforms:
                    try:
                        if hasattr(self.news_crawler, f'{platform}_hot_list'):
                            method = getattr(self.news_crawler, f'{platform}_hot_list')
                            data = method()[:max_items_per_platform]  # 限制数量
                            news_data[platform] = data
                            print(f"✅ {platform}: {len(data)} 条")
                    except Exception as e:
                        print(f"⚠️ {platform}: 获取失败 - {e}")
                        news_data[platform] = []
            else:
                news_data = self.news_crawler.get_all_news()

            # 统计总数
            total_news = sum(len(items) for items in news_data.values())

            # 保存原始数据
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"crawled_news_{timestamp}_test.json" if test_mode else f"crawled_news_{timestamp}.json"
            output_file = self.news_crawler.save_to_json(news_data, filename)

            return {
                'success': True,
                'news_data': news_data,
                'total_news': total_news,
                'output_file': output_file,
                'platforms': list(news_data.keys())
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _step2_merge_topics(self, news_data: Dict[str, List], max_topics: int = None) -> Dict[str, Any]:
        """步骤2: 合并相似话题"""
        try:
            # 提取标题
            topics_data = {}
            for platform, news_items in news_data.items():
                titles = [item.get('title', '') for item in news_items if item.get('title')]
                topics_data[platform] = titles
            
            # 合并话题
            merged_topics = self.topic_merger.merge_topics(topics_data)

            # 二次合并（如果话题数量过多）
            if len(merged_topics) > 15:  # 超过15个话题时进行二次合并
                merged_topics = self.topic_merger.second_merge(merged_topics)

            # 根据参数限制数量
            if max_topics is not None:
                merged_topics = merged_topics[:max_topics]
                print(f"📊 根据设置限制为 {max_topics} 个话题")
            
            return {
                'success': True,
                'merged_topics': merged_topics,
                'original_count': sum(len(titles) for titles in topics_data.values()),
                'merged_count': len(merged_topics)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _step3_process_topics(self, merged_topics: List[Dict]) -> Dict[str, Any]:
        """步骤3: 使用您现有的深度分析功能"""
        try:
            print(f"🧠 开始深度分析 {len(merged_topics)} 个话题...")

            # 使用您现有的深度分析功能
            processed_topics = self.topic_processor.process_all_topics(merged_topics, use_concurrent=True)

            # 生成深度分析报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存处理结果
            processed_file = f"news_output/processed_topics_{timestamp}.json"
            os.makedirs("news_output", exist_ok=True)
            with open(processed_file, 'w', encoding='utf-8') as f:
                json.dump(processed_topics, f, ensure_ascii=False, indent=2)

            # 生成智能报告
            report_file = f"news_output/intelligence_report_{timestamp}.md"
            generate_report(processed_topics, report_file)

            print(f"✅ 深度分析完成，处理 {len(processed_topics)} 个话题")
            print(f"📁 输出文件:")
            print(f"   分析结果: {processed_file}")
            print(f"   智能报告: {report_file}")

            return {
                'success': True,
                'processed_topics': processed_topics,
                'processed_count': len(processed_topics),
                'processed_file': processed_file,
                'report_file': report_file
            }

        except Exception as e:
            print(f"❌ 深度分析失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _step4_search_news(self, processed_topics: List[Dict]) -> Dict[str, Any]:
        """步骤4: 并发搜索相关新闻"""
        try:
            # 过滤有效话题
            valid_topics = [topic for topic in processed_topics if 'error' not in topic]

            if not valid_topics:
                return {
                    'success': True,
                    'search_results': [],
                    'searched_topics': 0
                }

            print(f"🔍 开始并发搜索 {len(valid_topics)} 个话题的相关新闻...")

            search_results = []

            def search_single_topic(topic_with_index):
                """单个话题搜索任务"""
                index, topic = topic_with_index
                try:
                    print(f"🔍 线程 {index+1}: 开始搜索话题...")
                    if self.news_search:
                        result = self.news_search.search_for_topic(topic)
                    else:
                        # 简化搜索模式
                        result = {
                            'topic_title': topic.get('original_topic', {}).get('merged_title', ''),
                            'search_time': datetime.now().isoformat(),
                            'total_found': 0,
                            'news_items': [],
                            'note': '使用简化搜索模式'
                        }
                    print(f"✅ 线程 {index+1}: 搜索完成")
                    return result
                except Exception as e:
                    print(f"❌ 线程 {index+1}: 搜索失败 - {e}")
                    return {
                        'topic_title': topic.get('original_topic', {}).get('merged_title', ''),
                        'search_time': datetime.now().isoformat(),
                        'total_found': 0,
                        'news_items': [],
                        'error': str(e)
                    }

            # 使用线程池并发搜索（针对4C16G服务器优化为8个线程）
            max_workers = min(8, len(valid_topics))
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有搜索任务
                future_to_index = {
                    executor.submit(search_single_topic, (i, topic)): i
                    for i, topic in enumerate(valid_topics)
                }

                # 收集结果
                search_results = [None] * len(valid_topics)
                completed_count = 0

                for future in as_completed(future_to_index):
                    completed_count += 1
                    index = future_to_index[future]
                    print(f"📊 搜索进度: {completed_count}/{len(valid_topics)} 个话题完成")

                    try:
                        result = future.result()
                        search_results[index] = result
                    except Exception as e:
                        print(f"❌ 获取搜索结果失败: {e}")
                        search_results[index] = {
                            'topic_title': valid_topics[index].get('original_topic', {}).get('merged_title', ''),
                            'search_time': datetime.now().isoformat(),
                            'total_found': 0,
                            'news_items': [],
                            'error': str(e)
                        }

            # 过滤None结果
            search_results = [r for r in search_results if r is not None]

            print(f"🎉 并发搜索完成: 成功搜索 {len(search_results)} 个话题")

            return {
                'success': True,
                'search_results': search_results,
                'searched_topics': len(search_results)
            }

        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _step5_generate_articles(self, processed_topics: List[Dict],
                                search_results: List[Dict], max_articles: int = None) -> Dict[str, Any]:
        """步骤5: 生成文章"""
        try:
            articles = []

            # 确定要处理的话题数量
            topics_to_process = processed_topics
            if max_articles is not None:
                topics_to_process = processed_topics[:max_articles]
                print(f"📊 根据设置限制生成 {max_articles} 篇文章")
            else:
                print(f"📊 为所有 {len(processed_topics)} 个话题生成文章")

            # 准备并发任务
            tasks = []
            for i, topic in enumerate(topics_to_process):
                if 'error' in topic:
                    continue

                if i < len(search_results):
                    search_result = search_results[i]
                    tasks.append({
                        'topic': topic,
                        'search_result': search_result,
                        'index': i
                    })

            if tasks:
                print(f"🚀 开始并发生成 {len(tasks)} 篇文章，使用5线程...")
                articles = self.article_generator.generate_articles_concurrent(tasks, max_workers=5)
            else:
                articles = []
            
            return {
                'success': True,
                'articles': articles,
                'generated_count': len(articles)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _step6_save_to_database(self, articles: List[Dict]) -> Dict[str, Any]:
        """步骤6: 保存到数据库"""
        try:
            # 连接数据库
            if not self.db_manager.connect():
                raise Exception("数据库连接失败")
            
            saved_count = 0
            saved_ids = []
            
            for article in articles:
                article_id = self.db_manager.insert_article(article)
                if article_id:
                    saved_count += 1
                    saved_ids.append(article_id)
            
            self.db_manager.disconnect()
            
            return {
                'success': True,
                'saved_count': saved_count,
                'saved_ids': saved_ids,
                'total_articles': len(articles)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _print_final_summary(self, pipeline_result: Dict[str, Any]):
        """打印最终摘要"""
        stats = pipeline_result['final_stats']
        execution_time = pipeline_result.get('execution_time', 0)
        
        print(f"📊 执行摘要:")
        print(f"   ⏱️  总耗时: {execution_time:.1f} 秒")
        print(f"   📰 爬取新闻: {stats['total_news_crawled']} 条")
        print(f"   🔄 合并话题: {stats['topics_merged']} 个")
        print(f"   🔍 处理话题: {stats['topics_processed']} 个")
        print(f"   📝 生成文章: {stats['articles_generated']} 篇")
        print(f"   💾 保存文章: {stats['articles_saved']} 篇")
        
        if stats['articles_saved'] > 0:
            print(f"\n✅ 成功生成并保存了 {stats['articles_saved']} 篇文章到数据库")
        else:
            print(f"\n⚠️ 没有文章被保存到数据库")


def main():
    """主函数"""
    try:
        # 创建流水线实例
        pipeline = IntegratedNewsPipeline()
        
        # 运行完整流水线（处理所有合并后的话题，为所有话题生成文章）
        result = pipeline.run_full_pipeline(
            max_topics=None,    # 处理所有合并后的话题
            max_articles=None   # 为所有话题生成文章
        )
        
        if result['success']:
            print(f"\n🎉 流水线执行成功!")
            return True
        else:
            print(f"\n❌ 流水线执行失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
