# 🎯 热点新闻智能处理系统

一个基于 Python 的多平台热点新闻爬虫和智能分析工具，支持自动抓取、智能合并、深度分析各大平台的热门内容。

<div align="center">

![演示动图](https://via.placeholder.com/800x400/4CAF50/FFFFFF?text=AI+News+Intelligence+System)

**🚀 从300个原始话题智能合并至70+个精品话题 | 🧠 AI驱动的深度分析 | ⚡ 5倍并发处理速度**

[![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Stars](https://img.shields.io/github/stars/yourusername/yourrepo.svg)](https://github.com/yourusername/yourrepo/stargazers)

</div>

## 📋 快速导航

- [✨ 功能特色](#-功能特色)
- [🚀 快速开始](#-快速开始)
- [📁 输出文件说明](#-输出文件说明)
- [📊 支持的平台](#-支持的平台)
- [⚙️ 配置选项](#️-配置选项)
- [📈 更新日志](#-更新日志)

## ✨ 功能特色

### 🚀 核心功能
- **多平台支持**: 支持微博、百度、知乎、今日头条、B站、GitHub、V2EX、IT之家等8个主流平台
- **智能合并**: 使用AI技术自动合并相似话题，去重整合
- **深度分析**: 智能提取关键词、分析话题重要性和趋势
- **并发处理**: 支持多API Key并发处理，大幅提升处理效率
- **数据完整性**: 严格的重试机制确保数据不丢失

### 🧠 AI智能功能
- **话题合并**: 300个原始话题智能合并至70+个精品话题
- **分类处理**: 按时政、科技、娱乐等分类进行深度合并
- **关键词提取**: 自动提取每个话题的核心关键词
- **重要性评分**: AI评估话题影响力和关注度
- **智能报告**: 生成结构化的分析报告

### 🔧 技术特色
- **并发架构**: 支持5个API Key同时工作，处理速度提升5倍
- **重试机制**: 智能重试失败的批次，确保100%数据处理
- **JSON优化**: 简化格式要求，大幅降低解析错误率
- **文件管理**: 所有输出文件自动保存到 `news_output` 文件夹
- **美观界面**: 带有 emoji 和颜色的友好命令行界面

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

### 依赖包说明
- `requests`: HTTP 请求库
- `beautifulsoup4`: HTML 解析库
- `feedparser`: RSS/Atom 解析库
- `python-dotenv`: 环境变量管理

### 🔑 配置API Key

创建 `.env` 文件并配置你的LLM API Key：

```bash
# 支持多个API Key，用逗号分隔
LLM_API_KEYS=sk-xxx1,sk-xxx2,sk-xxx3,sk-xxx4,sk-xxx5
LLM_BASE_URL=https://api.openai.com/v1
LLM_MODEL=gpt-3.5-turbo
```

## 🚀 快速开始

### 1. 智能处理（推荐）⭐

运行完整的智能处理流程，包含爬取、合并、分析：

```bash
python news_intelligence.py
```

程序会自动完成：
1. 🕷️ **爬取热门新闻** - 从8个平台获取300+条热点
2. 📝 **格式化标题** - 统一格式，便于后续处理
3. 🧠 **智能合并** - AI合并相似话题，去重整合
4. 🔍 **深度分析** - 提取关键词，评估重要性
5. 📊 **生成报告** - 输出结构化分析报告

### 2. 仅爬取数据

只运行爬虫，获取原始数据：

```bash
python news_crawler.py
```

### 3. 格式化已有数据

如果你已有数据文件，只想格式化标题：

```bash
python news_crawler.py --format-only <数据文件> [输出前缀]
```

示例：
```bash
python news_crawler.py --format-only news_data_20250707_115444.json my_titles
```

## 📊 支持的平台

| 平台 | 状态 | 数据类型 | 默认数量 |
|------|------|----------|----------|
| 🔸 微博热搜 | ✅ | 热搜话题 | 50 |
| 🔸 百度热搜 | ✅ | 热搜关键词 | 50 |
| 🔸 知乎热榜 | ✅ | 热门问题 | 50 |
| 🔸 今日头条 | ✅ | 热门新闻 | 50 |
| 🔸 B站热搜 | ✅ | 热搜关键词 | 50 |
| 🔸 GitHub趋势 | ⚠️ | 趋势项目 | 20 |
| 🔸 V2EX热门 | ✅ | 热门话题 | 50 |
| 🔸 IT之家 | ⚠️ | 科技新闻 | 20 |

> ⚠️ 注：GitHub 和 IT之家 可能因网络或API限制偶尔获取失败

## 📁 输出文件说明

所有文件都保存在 `news_output/` 文件夹中：

### 🗂️ 完整处理流程输出

运行 `news_intelligence.py` 会生成以下文件：

1. **原始数据**: `news_data_YYYYMMDD_HHMMSS.json`
   - 包含完整的原始数据，包括标题、链接、额外信息等

2. **格式化标题**: `formatted_titles_YYYYMMDD_HHMMSS.json`
   - 纯标题数据，按平台分类，便于程序处理

3. **合并话题**: `merged_topics_YYYYMMDD_HHMMSS.json`
   - AI智能合并后的话题，包含分类、重要性评分
   - 从300+个原始话题合并至70+个精品话题

4. **分析结果**: `processed_topics_YYYYMMDD_HHMMSS.json`
   - 深度分析结果，包含关键词、趋势分析等

5. **智能报告**: `intelligence_report_YYYYMMDD_HHMMSS.md`
   - 结构化的Markdown报告，包含统计和分析

### 📊 性能对比

| 指标 | 传统方式 | 智能处理系统 | 提升效果 |
|------|----------|-------------|----------|
| 🕷️ 数据获取 | 300条原始话题 | 300条原始话题 | ✅ 相同 |
| 🔄 话题合并 | 手动去重 | AI智能合并 | 🚀 **自动化** |
| 📊 最终话题数 | ~300条 | ~74条 | 🎯 **75%减少** |
| ⚡ 处理速度 | 单线程 | 5并发 | 🚀 **5倍提升** |
| 🧠 分析深度 | 基础统计 | AI深度分析 | 🎯 **质的飞跃** |
| 📈 数据完整性 | 可能丢失 | 100%保障 | ✅ **零丢失** |
| 🎨 输出格式 | 简单列表 | 智能报告 | 📊 **结构化** |

### 🎯 处理效果展示

```
📊 数据流转过程：
原始数据 (300条)
    ↓ 🧠 AI智能合并
一次合并 (85条)
    ↓ 🔄 分类深度合并
二次合并 (74条)
    ↓ 🔍 深度分析
最终报告 (74条精品话题 + 关键词 + 分析)
```

## ⚙️ 配置选项

### 创建配置文件 `crawler_config.py`

```python
# 平台数量限制
PLATFORM_LIMITS = {
    'zhihu': 50,        # 知乎热榜数量
    'bilibili': 50,     # B站热搜数量
    'github': 25,       # GitHub趋势数量
    'v2ex': 50,         # V2EX热门数量
    'ithome': 30        # IT之家新闻数量
}

# 网络配置
NETWORK_CONFIG = {
    'timeout': 15,          # 请求超时时间（秒）
    'max_retries': 3,       # 最大重试次数
    'retry_delay': 2,       # 重试延迟（秒）
    'request_interval': 1   # 请求间隔（秒）
}

# 代理配置
PROXY_CONFIG = {
    'enabled': False,                    # 是否启用代理
    'http_proxy': 'http://127.0.0.1:7890',   # HTTP代理
    'https_proxy': 'http://127.0.0.1:7890'   # HTTPS代理
}

# 输出配置
OUTPUT_CONFIG = {
    'save_to_file': True,           # 是否保存到文件
    'show_summary': True,           # 是否显示摘要
    'summary_preview_count': 5      # 摘要预览条数
}

# 平台开关
PLATFORM_ENABLED = {
    'weibo': True,      # 微博热搜
    'baidu': True,      # 百度热搜
    'zhihu': True,      # 知乎热榜
    'toutiao': True,    # 今日头条
    'bilibili': True,   # B站热搜
    'github': True,     # GitHub趋势
    'v2ex': True,       # V2EX热门
    'ithome': True      # IT之家
}
```

## 🔧 高级用法

### 使用代理

```python
from news_crawler import NewsCrawler

# 创建带代理的爬虫实例
crawler = NewsCrawler(proxy="http://127.0.0.1:7890")
news_data = crawler.get_all_news()
```

### 单独获取某个平台

```python
from news_crawler import NewsCrawler

crawler = NewsCrawler()

# 只获取知乎热榜
zhihu_data = crawler.zhihu_hot_list()
print(f"获取到 {len(zhihu_data)} 条知乎数据")

# 只获取微博热搜
weibo_data = crawler.weibo_hot_search()
print(f"获取到 {len(weibo_data)} 条微博数据")
```

### 自定义格式化

```python
from news_crawler import NewsFormatter
import json

# 加载数据
with open('news_output/news_data_20250707_115444.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 格式化
formatter = NewsFormatter()
json_file, txt_file = formatter.format_news_data(data, "custom_output")
```

## 📋 输出示例

### 智能处理控制台输出
```
🚀 启动新闻智能处理一条龙服务
============================================================

📰 第一步：爬取热门新闻...
🚀 开始抓取新闻数据...
==================================================
✅ 微博热搜: 获取到 50 条数据
✅ 百度热搜: 获取到 50 条数据
✅ 知乎热榜: 获取到 50 条数据
✅ 今日头条: 获取到 50 条数据
✅ B站热搜: 获取到 50 条数据
✅ V2EX热门: 获取到 50 条数据
==================================================
📊 抓取完成! 总计获取 300 条新闻数据

📝 第二步：格式化标题...
✅ 格式化完成，处理 300 条标题

🔄 第三步：智能合并相似话题...
📊 总共 300 个话题，将分 3 批处理（每批最多 100 个）
🚀 第 1 轮：并发处理 3 批话题...
📊 并发处理完成: ✅ 成功: 3 ❌ 失败: 0 📈 成功率: 100.0%
✅ 一次合并完成，生成 85 个合并话题

🔄 第三步B：二次智能合并...
📊 分类统计: 科技: 9个 时政: 3个 社会: 25个 娱乐: 17个...
🚀 开始并发处理 8 个分类批次...
✅ 二次合并完成：85 → 74 个话题

🧠 第四步：深度分析话题...
🚀 开始处理 74 个话题...
⚡ 使用并发模式处理话题...
📊 并发处理完成: ✅ 成功: 74 ❌ 失败: 0 📈 成功率: 100.0%
✅ 深度分析完成，处理 74 个话题

📊 第五步：生成智能报告...
✅ 报告生成完成

🎉 新闻智能处理完成！
```

### 格式化文本输出示例
```
📋 热点新闻标题汇总
==================================================
⏰ 生成时间: 2025-07-07 12:03:14

🔸 微博热搜 (50条):
----------------------------------------
   1. 果然高铁F座最受欢迎
   2. 实付2元点外卖被偷气得直接报警
   3. 七七事变背后的历史真相
   ...

🔸 百度热搜 (50条):
----------------------------------------
   1. 央视曝光零差评背后猫腻
   2. 王毅这句话的含金量还在飙升
   ...
```

## ❓ 常见问题

### Q: 为什么某些平台获取到0条数据？
A: 可能的原因：
- 网络连接问题
- 平台API发生变化
- 需要使用代理访问
- 平台临时限制访问

### Q: 如何提高获取成功率？
A: 建议：
- 使用稳定的网络环境
- 配置合适的代理
- 增加重试次数和超时时间
- 适当增加请求间隔

### Q: 数据格式是什么？
A: 统一的JSON格式，每条新闻包含：
- `id`: 唯一标识
- `title`: 标题
- `url`: 链接地址
- `extra`: 额外信息（图标、热度等）

### Q: 如何定制输出格式？
A: 可以修改 `NewsFormatter` 类中的 `format_output_text` 方法来自定义输出格式。

## 🛠️ 开发说明

### 项目结构
```
热点新闻标题爬取整合/
├── news_intelligence.py    # 🧠 智能处理主程序（推荐）
├── news_crawler.py         # 🕷️ 新闻爬虫
├── topic_merger.py         # 🔄 话题合并器
├── topic_processor.py      # 🔍 话题分析器
├── llm_client.py           # 🤖 LLM客户端
├── config.py               # ⚙️ 配置管理
├── crawler_config.py       # 🕷️ 爬虫配置
├── .env                    # 🔑 API密钥配置
├── requirements.txt        # 📦 依赖包列表
├── README.md              # 📖 说明文档
├── news_output/           # 📁 输出文件夹
│   ├── news_data_*.json           # 原始数据
│   ├── formatted_titles_*.json   # 格式化标题
│   ├── merged_topics_*.json      # 合并话题
│   ├── processed_topics_*.json   # 分析结果
│   └── intelligence_report_*.md  # 智能报告
└── 使用说明*.md           # 📋 详细使用说明
```

### 添加新平台
1. 在 `NewsCrawler` 类中添加新的方法
2. 在 `get_all_news` 方法中注册新平台
3. 在配置文件中添加相应配置

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🎯 使用场景

### 1. 媒体监控
- 实时监控各平台热点话题
- 分析舆情趋势和热点变化
- 为新闻编辑提供素材参考

### 2. 数据分析
- 收集大量标题数据用于NLP分析
- 研究不同平台的内容特点
- 进行热点话题的统计分析

### 3. 内容创作
- 获取当前热门话题灵感
- 了解各平台用户关注点
- 为内容创作提供方向参考

### 4. 竞品分析
- 监控竞争对手在各平台的表现
- 分析行业热点和趋势
- 制定内容策略

## 🔍 技术实现

### 数据获取方式
- **微博**: 通过官方API获取热搜数据
- **百度**: 解析热搜页面HTML内容
- **知乎**: 使用知乎热榜API
- **今日头条**: 调用热榜接口
- **B站**: 使用搜索热词API
- **GitHub**: 通过GitHub API获取趋势项目
- **V2EX**: 解析RSS订阅源
- **IT之家**: 爬取首页新闻列表

### 错误处理机制
- 自动重试失败的请求
- 跳过无法访问的平台
- 详细的错误日志记录
- 优雅的异常处理

### 性能优化
- 使用Session复用连接
- 合理的请求间隔避免被限制
- 异步处理多个平台数据
- 内存友好的数据处理

## 📈 更新日志

### v3.0.0 (2025-07-08) 🎉 重大更新
- 🧠 **新增AI智能处理**: 完整的智能分析流程
- 🔄 **智能话题合并**: 300个话题智能合并至70+个精品话题
- 🚀 **并发处理架构**: 支持5个API Key并发，速度提升5倍
- 🎯 **二次深度合并**: 按分类进行智能合并，确保数据完整性
- 🔍 **深度话题分析**: 自动提取关键词、评估重要性
- 📊 **智能报告生成**: 输出结构化Markdown分析报告
- 🛡️ **数据完整性保障**: 严格重试机制，确保零数据丢失
- 🎨 **优化JSON解析**: 简化格式要求，大幅降低错误率

### v2.0.0 (2025-07-07)
- ✨ 新增自动格式化功能
- 📁 所有输出文件自动保存到专用文件夹
- 🎨 优化用户界面和输出格式
- 🔧 支持仅格式化模式
- 📊 增强统计信息显示

### v1.0.0
- 🚀 基础爬虫功能
- 📊 支持8个主流平台
- ⚙️ 灵活的配置系统
- 🔄 自动重试机制

## 🚨 注意事项

1. **合规使用**: 请遵守各平台的robots.txt和使用条款
2. **频率控制**: 避免过于频繁的请求，建议间隔1秒以上
3. **网络环境**: 某些平台可能需要特定的网络环境或代理
4. **数据时效**: 热点数据具有时效性，建议定期更新
5. **存储空间**: 长期运行请注意清理旧的数据文件

## 🔮 未来计划

### 🎯 短期计划
- [ ] 支持更多LLM模型（Claude、Gemini等）
- [ ] 添加话题趋势分析和预测
- [ ] 实现话题相关性网络图谱
- [ ] 支持自定义合并规则和分类

### 🚀 中期计划
- [ ] 支持更多平台（抖音、小红书、Twitter等）
- [ ] 添加数据可视化Dashboard
- [ ] 实现定时任务和自动化部署
- [ ] 集成向量数据库进行语义搜索

### 🌟 长期计划
- [ ] 添加Web界面管理系统
- [ ] 支持多语言国际化
- [ ] 实现实时流式处理
- [ ] 构建新闻推荐系统

## 🤝 贡献指南

### 如何贡献
1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范
- 使用 Python 3.7+ 语法
- 遵循 PEP 8 代码风格
- 添加适当的注释和文档字符串
- 确保代码通过基本测试

### 报告问题
- 使用 GitHub Issues 报告 Bug
- 提供详细的错误信息和复现步骤
- 包含系统环境和Python版本信息

## 📞 联系方式

- 📧 Email: 通过 GitHub Issues 联系
- 💬 讨论: GitHub Discussions
- 🐛 Bug报告: GitHub Issues

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目的支持：
- [requests](https://github.com/psf/requests) - HTTP库
- [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) - HTML解析
- [feedparser](https://github.com/kurtmckee/feedparser) - RSS解析

---

<div align="center">

## 🌟 为什么选择我们？

| 🎯 **智能化** | 🚀 **高效率** | 🛡️ **可靠性** | 🔧 **易用性** |
|:---:|:---:|:---:|:---:|
| AI驱动的智能合并 | 5倍并发处理速度 | 100%数据完整性 | 一键启动处理 |
| 深度话题分析 | 批量重试机制 | 严格错误处理 | 友好界面设计 |
| 自动分类整理 | 多API Key负载均衡 | 零数据丢失 | 详细文档说明 |

### 🎉 立即体验

```bash
# 1. 克隆项目
git clone https://github.com/yourusername/yourrepo.git

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置API Key
echo "LLM_API_KEYS=your-api-keys" > .env

# 4. 一键启动
python news_intelligence.py
```

⭐ **如果这个项目对你有帮助，请给个 Star 支持一下！**

🔔 **Watch 本项目以获取最新更新通知**

📢 **欢迎提交 Issue 和 PR，一起完善这个项目！**

</div>
#   - s q l s 
 
 