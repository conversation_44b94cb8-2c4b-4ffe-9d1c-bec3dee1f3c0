#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通用爬虫的配置
"""

from huanqiu_universal_spider import SECTIONS, HuanqiuUniversalSpider

def test_sections():
    """测试所有板块配置"""
    print("=== 环球网通用爬虫板块配置测试 ===")
    print(f"共配置了 {len(SECTIONS)} 个板块:\n")
    
    for i, (key, config) in enumerate(SECTIONS.items(), 1):
        print(f"{i:2d}. {key:12s} - {config['name']:4s} - {config['base_url']}")
        print(f"    节点数量: {len(config['nodes'])}")
        print(f"    API地址: {config['api_url']}")
        print()

def test_single_section(section_key, max_news=3):
    """测试单个板块"""
    if section_key not in SECTIONS:
        print(f"错误：板块 '{section_key}' 不存在")
        return
    
    print(f"=== 测试 {section_key} ({SECTIONS[section_key]['name']}) 板块 ===")
    
    try:
        spider = HuanqiuUniversalSpider(section_key)
        print(f"爬虫初始化成功")
        print(f"基础URL: {spider.config['base_url']}")
        print(f"API URL: {spider.config['api_url']}")
        print(f"节点数量: {len(spider.config['nodes'])}")
        
        # 尝试获取少量新闻进行测试
        print(f"\n正在获取 {max_news} 条新闻进行测试...")
        articles = spider.crawl_today_pages(max_pages=1)
        
        if articles:
            print(f"✅ 成功获取 {len(articles)} 条新闻")
            if articles:
                print(f"第一条新闻标题: {articles[0]['title']}")
        else:
            print("❌ 未获取到新闻")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    # 显示所有板块配置
    test_sections()
    
    # 测试几个主要板块
    test_sections_list = ['world', 'china', 'finance', 'tech', 'agriculture']
    
    for section in test_sections_list:
        print("\n" + "="*60)
        test_single_section(section, max_news=2)
        print("="*60)
