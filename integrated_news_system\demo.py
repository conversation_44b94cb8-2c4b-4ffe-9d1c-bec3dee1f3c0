#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成新闻系统演示脚本
展示系统的核心功能和使用方法
"""

import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config import print_config_status
from news_crawler import NewsCrawler
from topic_merger import TopicMerger
from topic_processor import TopicProcessor
from article_generator import ArticleGenerator


def show_demo_banner():
    """显示演示横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    集成新闻系统演示                            ║
║                  System Demonstration                       ║
║                                                              ║
║  本演示将展示系统的核心功能和完整流程                          ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)


def demo_news_crawling():
    """演示新闻爬取功能"""
    print("\n📰 演示1: 新闻爬取功能")
    print("=" * 50)
    
    try:
        crawler = NewsCrawler()
        
        # 演示单个平台爬取
        print("🔍 正在爬取微博热搜...")
        weibo_news = crawler.weibo_hot_search()
        
        if weibo_news:
            print(f"✅ 成功爬取 {len(weibo_news)} 条微博热搜")
            print("\n📋 前5条热搜:")
            for i, news in enumerate(weibo_news[:5], 1):
                print(f"   {i}. {news['title']}")
        else:
            print("⚠️ 未能获取微博热搜数据")
        
        return weibo_news
        
    except Exception as e:
        print(f"❌ 新闻爬取演示失败: {e}")
        return []


def demo_topic_merging(news_data):
    """演示话题合并功能"""
    print("\n🔄 演示2: 话题合并功能")
    print("=" * 50)
    
    try:
        merger = TopicMerger()
        
        # 准备测试数据
        if news_data:
            topics_data = {'weibo': [item['title'] for item in news_data[:10]]}
        else:
            # 使用示例数据
            topics_data = {
                'weibo': ['人工智能技术突破', 'AI芯片发展', '机器学习应用'],
                'zhihu': ['人工智能前景分析', '深度学习算法', '智能机器人发展'],
                'toutiao': ['AI技术创新', '人工智能产业', '机器学习趋势']
            }
        
        print(f"🔍 正在合并 {sum(len(titles) for titles in topics_data.values())} 个话题...")
        
        merged_topics = merger.merge_topics(topics_data, batch_size=20)
        
        if merged_topics:
            print(f"✅ 成功合并为 {len(merged_topics)} 个话题")
            print("\n📋 合并后的话题:")
            for i, topic in enumerate(merged_topics[:5], 1):
                print(f"   {i}. {topic['merged_title']} ({topic['category']})")
                print(f"      来源: {', '.join(topic['source_titles'][:2])}...")
                print(f"      评分: {topic['importance_score']}/10")
        else:
            print("⚠️ 话题合并失败")
        
        return merged_topics
        
    except Exception as e:
        print(f"❌ 话题合并演示失败: {e}")
        return []


def demo_topic_processing(merged_topics):
    """演示话题处理功能"""
    print("\n🔍 演示3: 话题处理功能")
    print("=" * 50)
    
    try:
        processor = TopicProcessor()
        
        if not merged_topics:
            # 使用示例数据
            merged_topics = [{
                'merged_title': '人工智能技术发展趋势',
                'category': '科技',
                'source_platforms': ['weibo', 'zhihu'],
                'source_titles': ['AI技术突破', '人工智能前景'],
                'importance_score': 8
            }]
        
        # 处理第一个话题
        topic = merged_topics[0]
        print(f"🔍 正在分析话题: {topic['merged_title']}")
        
        analysis_result = processor.analyze_topic(topic)
        
        if analysis_result and 'keywords' in analysis_result:
            print("✅ 话题分析完成")
            print(f"\n📋 分析结果:")
            print(f"   🔑 关键词: {', '.join(analysis_result['keywords'][:5])}")
            print(f"   🔍 搜索词: {', '.join(analysis_result['search_queries'][:3])}")
            print(f"   📝 背景: {analysis_result['background_context'][:100]}...")
            print(f"   📌 要点数: {len(analysis_result['key_points'])}")
        else:
            print("⚠️ 话题分析失败")
        
        return analysis_result
        
    except Exception as e:
        print(f"❌ 话题处理演示失败: {e}")
        return None


def demo_article_generation(topic_analysis):
    """演示文章生成功能"""
    print("\n📝 演示4: 文章生成功能")
    print("=" * 50)
    
    try:
        generator = ArticleGenerator()
        
        if not topic_analysis:
            # 使用示例数据
            topic_analysis = {
                'original_topic': {
                    'merged_title': '人工智能技术发展趋势',
                    'category': '科技',
                    'importance_score': 8
                },
                'keywords': ['人工智能', 'AI', '技术发展', '创新'],
                'background_context': '人工智能技术在近年来取得了重大突破，各行各业都在积极探索AI的应用可能性。',
                'key_points': [
                    '技术突破带来新机遇',
                    '应用场景不断扩展',
                    '产业变革加速推进',
                    '人才需求持续增长'
                ],
                'related_entities': {
                    'people': ['研究人员', '工程师'],
                    'organizations': ['科技公司', '研究机构'],
                    'locations': ['中国', '美国'],
                    'others': ['算法', '数据']
                }
            }
        
        # 模拟新闻搜索结果
        news_search_results = {
            'news_items': [
                {
                    'metadata': {
                        'title': '人工智能技术取得新突破',
                        'content': '最新研究显示，人工智能在多个领域都取得了显著进展...',
                        'source': '科技日报',
                        'publish_time': datetime.now().isoformat()
                    },
                    'similarity': 0.85
                }
            ]
        }
        
        print(f"🔍 正在生成文章: {topic_analysis['original_topic']['merged_title']}")
        
        article = generator.generate_article(topic_analysis, news_search_results)
        
        if article and 'title' in article and 'content' in article:
            print("✅ 文章生成完成")
            print(f"\n📋 文章信息:")
            print(f"   📰 标题: {article['title']}")
            print(f"   📊 字数: {article['word_count']}")
            print(f"   🏷️  分类: {article['category']}")
            print(f"   ⭐ 评分: {article['importance_score']}/10")
            print(f"\n📝 文章摘要:")
            print(f"   {article['summary']}")
            print(f"\n📄 文章开头:")
            content_preview = article['content'][:200] + "..." if len(article['content']) > 200 else article['content']
            print(f"   {content_preview}")
        else:
            print("⚠️ 文章生成失败")
        
        return article
        
    except Exception as e:
        print(f"❌ 文章生成演示失败: {e}")
        return None


def demo_full_workflow():
    """演示完整工作流程"""
    print("\n🚀 演示5: 完整工作流程")
    print("=" * 50)
    
    workflow_start = time.time()
    
    # 步骤1: 爬取新闻
    news_data = demo_news_crawling()
    
    # 步骤2: 合并话题
    merged_topics = demo_topic_merging(news_data)
    
    # 步骤3: 处理话题
    topic_analysis = demo_topic_processing(merged_topics)
    
    # 步骤4: 生成文章
    article = demo_article_generation(topic_analysis)
    
    workflow_end = time.time()
    execution_time = workflow_end - workflow_start
    
    print(f"\n🎉 完整工作流程演示完成!")
    print(f"⏱️  总耗时: {execution_time:.1f} 秒")
    
    if article:
        print(f"✅ 成功生成文章: {article['title']}")
        return True
    else:
        print("⚠️ 工作流程部分失败")
        return False


def interactive_demo():
    """交互式演示"""
    show_demo_banner()
    
    print("🎯 欢迎体验集成新闻系统!")
    print("请选择要演示的功能:\n")
    
    while True:
        print("📋 可用演示:")
        print("  1. 新闻爬取功能")
        print("  2. 话题合并功能")
        print("  3. 话题处理功能")
        print("  4. 文章生成功能")
        print("  5. 完整工作流程")
        print("  0. 退出演示")
        
        choice = input("\n请输入选项编号: ").strip()
        
        if choice == "0":
            print("👋 演示结束，感谢体验!")
            break
        elif choice == "1":
            demo_news_crawling()
        elif choice == "2":
            demo_topic_merging([])
        elif choice == "3":
            demo_topic_processing([])
        elif choice == "4":
            demo_article_generation(None)
        elif choice == "5":
            demo_full_workflow()
        else:
            print("❌ 无效选项，请重新选择")
        
        print("\n" + "-" * 60 + "\n")


def main():
    """主函数"""
    # 检查配置
    print("🔍 检查系统配置...")
    if not print_config_status():
        print("❌ 配置检查失败，某些功能可能无法正常工作")
        print("💡 请配置.env文件中的API密钥")
    
    # 运行演示
    if len(sys.argv) > 1:
        if sys.argv[1] == "--full":
            return demo_full_workflow()
        elif sys.argv[1] == "--crawl":
            demo_news_crawling()
            return True
        elif sys.argv[1] == "--merge":
            demo_topic_merging([])
            return True
        elif sys.argv[1] == "--process":
            demo_topic_processing([])
            return True
        elif sys.argv[1] == "--generate":
            demo_article_generation(None)
            return True
        else:
            print("❌ 未知参数，使用 --help 查看帮助")
            return False
    else:
        interactive_demo()
        return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
