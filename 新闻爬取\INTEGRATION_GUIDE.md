# 澎湃新闻和环球新闻爬虫集成指南

## 🎉 集成完成

已成功将澎湃新闻爬虫和环球新闻爬虫集成到现有的统一新闻处理系统中。

## ✨ 新功能特性

### 6线程并发爬取
- **网易新闻** (线程1)
- **新浪新闻** (线程2) 
- **凤凰网** (线程3)
- **界面新闻** (线程4)
- **澎湃新闻** (线程5) - 新增
- **环球网** (线程6) - 新增

### 智能兼容性
- 自动检测澎湃和环球爬虫是否可用
- 如果某个爬虫不可用，系统会自动跳过并继续其他爬虫
- 保持向后兼容，原有4线程功能不受影响

### 统一数据格式
- 所有6个新闻源输出标准CSV格式
- 兼容现有的向量化处理管道
- 支持完整的爬取→向量化→存储流程

## 📁 文件结构

```
新闻爬取/
├── unified_news_crawler.py      # 主爬虫文件（已更新支持6线程）
├── test_integration.py          # 集成测试脚本（新增）
├── master_pipeline.py           # 主处理管道
├── csv_to_vector_processor.py   # 向量化处理器
├── 澎湃新闻爬虫 - 副本/         # 澎湃新闻爬虫目录
│   └── thepaper_master_crawler.py
└── 环球爬虫 - 副本/             # 环球新闻爬虫目录
    └── huanqiu_universal_spider.py
```

## 🚀 使用方法

### 1. 测试集成状态
```bash
python test_integration.py
```

### 2. 运行6线程并发爬虫
```bash
# 基本使用 - 每个来源30条新闻
python unified_news_crawler.py

# 指定数量 - 每个来源10条新闻，获取详细内容
python unified_news_crawler.py 10 true

# 获取全部新闻 - 不限制数量
python unified_news_crawler.py all true

# 不获取详细内容 - 更快速度
python unified_news_crawler.py 20 false
```

### 3. 运行完整处理管道
```bash
# 爬取→向量化→存储的完整流程
python master_pipeline.py

# 指定参数的完整流程
python master_pipeline.py 15 true 10 3
```

## 📊 性能优势

### 并发效率提升
- **原系统**: 4线程并发
- **新系统**: 6线程并发
- **性能提升**: 约50%的数据获取能力

### 数据源扩展
- **原数据源**: 网易、新浪、凤凰、界面
- **新数据源**: + 澎湃新闻、环球网
- **覆盖范围**: 更全面的新闻内容覆盖

## 🔧 技术实现

### 集成方式
1. **模块化导入**: 动态导入澎湃和环球爬虫模块
2. **格式转换**: 实现数据格式统一转换函数
3. **线程管理**: 扩展线程池到6个并发线程
4. **错误处理**: 优雅处理爬虫不可用的情况

### 数据流程
```
澎湃新闻爬虫 → 格式转换 → 统一CSV格式
    ↓
环球新闻爬虫 → 格式转换 → 统一CSV格式
    ↓
6线程并发处理 → 数据合并 → 向量化处理 → 数据库存储
```

## 📈 输出格式

### CSV文件字段
- `id`: 新闻ID
- `title`: 新闻标题  
- `url`: 新闻链接
- `media`: 媒体来源（澎湃新闻、环球网等）
- `create_date`: 创建日期
- `create_time`: 创建时间
- `formatted_time`: 格式化时间
- `category`: 新闻分类
- `ranking_type`: 排行类型
- `rank`: 排名
- `content`: 新闻内容
- `crawl_time`: 爬取时间

## 🎯 使用建议

### 日常使用
```bash
# 推荐配置 - 平衡速度和内容质量
python unified_news_crawler.py 20 true
```

### 快速测试
```bash
# 少量数据快速测试
python unified_news_crawler.py 5 false
```

### 全量采集
```bash
# 获取当天所有新闻
python unified_news_crawler.py all true
```

## ⚠️ 注意事项

1. **网络依赖**: 需要稳定的网络连接
2. **时间消耗**: 6线程并发会增加网络请求频率
3. **存储空间**: 全量采集可能产生大量数据
4. **速率限制**: 遵守各网站的访问频率限制

## 🔍 故障排除

### 导入错误
如果某个爬虫导入失败，系统会自动跳过并继续其他爬虫的工作。

### 编码问题
Windows系统下如遇到编码问题，确保控制台支持UTF-8编码。

### 权限问题
确保对新闻爬取目录有读写权限。

## 📞 技术支持

如遇到问题，请检查：
1. 运行 `python test_integration.py` 确认集成状态
2. 检查网络连接
3. 确认所有依赖文件完整

---

*集成完成时间: 2025-07-18*  
*支持的新闻源: 网易、新浪、凤凰、界面、澎湃、环球 (6个)*  
*并发线程数: 6*