#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试澎湃新闻爬虫
"""

from thepaper_crawler import ThepaperCrawler
import json

def test_initial_crawl():
    """测试首页爬取"""
    print("测试首页新闻爬取...")
    crawler = ThepaperCrawler()
    
    try:
        news_list = crawler.get_initial_news()
        print(f"成功获取 {len(news_list)} 条初始新闻")
        
        if news_list:
            print("\n=== 第一条新闻示例 ===")
            first_news = news_list[0]
            for key, value in first_news.items():
                print(f"{key}: {value}")
        
        return news_list
        
    except Exception as e:
        print(f"首页爬取测试失败: {e}")
        return []

def test_api_crawl():
    """测试API爬取"""
    print("\n测试API新闻爬取...")
    crawler = ThepaperCrawler()
    
    try:
        api_data = crawler.get_more_news(page_num=2)
        
        if api_data:
            news_list = api_data.get('list', [])
            print(f"成功获取 {len(news_list)} 条API新闻")
            
            if news_list:
                print("\n=== API新闻示例 ===")
                first_api_news = crawler.parse_api_news(news_list[0])
                if first_api_news:
                    for key, value in first_api_news.items():
                        print(f"{key}: {value}")
        else:
            print("API数据获取失败")
            
    except Exception as e:
        print(f"API爬取测试失败: {e}")

def test_full_crawl():
    """测试完整爬取流程"""
    print("\n测试完整爬取流程...")
    crawler = ThepaperCrawler()
    
    try:
        news_data = crawler.crawl_yawen_news(max_pages=2)  # 只测试2页
        print(f"完整爬取测试完成，共获取 {len(news_data)} 条新闻")
        
        # 统计信息
        if news_data:
            sources = {}
            content_types = {}
            
            for news in news_data:
                source = news.get('source', '未知')
                sources[source] = sources.get(source, 0) + 1
                
                content_type = news.get('content_type', '未知')
                content_types[content_type] = content_types.get(content_type, 0) + 1
            
            print("\n=== 来源统计 ===")
            for source, count in sorted(sources.items(), key=lambda x: x[1], reverse=True):
                print(f"{source}: {count}")
            
            print("\n=== 内容类型统计 ===")
            for content_type, count in content_types.items():
                print(f"{content_type}: {count}")
        
    except Exception as e:
        print(f"完整爬取测试失败: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("澎湃新闻爬虫测试")
    print("=" * 60)
    
    # 运行测试
    test_initial_crawl()
    test_api_crawl()
    test_full_crawl()
    
    print("\n测试完成！")
