#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环球网国际新闻爬虫
支持爬取文章列表和详细内容
"""

import requests
import json
import time
import csv
import os
from datetime import datetime
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from tqdm import tqdm
# import pandas as pd  # 暂时注释掉pandas


class HuanqiuSpider:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.base_url = "https://world.huanqiu.com"
        self.api_url = "https://world.huanqiu.com/api/list"
        
        # 国际新闻的节点ID
        self.nodes = [
            "/e3pmh22ph/e3pmh2398",
            "/e3pmh22ph/e3pmh26vv", 
            "/e3pmh22ph/e3pn6efsl",
            "/e3pmh22ph/efp8fqe21"
        ]
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://world.huanqiu.com/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        
        self.session.headers.update(self.headers)
        
        # 存储数据
        self.articles = []
        
    def get_article_list(self, offset=0, limit=24):
        """
        获取文章列表
        """
        params = {
            'node': ','.join([f'"{node}"' for node in self.nodes]),
            'offset': offset,
            'limit': limit
        }
        
        try:
            response = self.session.get(self.api_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return data.get('list', [])
            
        except requests.RequestException as e:
            print(f"获取文章列表失败: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return []
    
    def get_article_content(self, article_id):
        """
        获取文章详细内容
        """
        article_url = f"https://world.huanqiu.com/article/{article_id}"
        
        try:
            # 随机更换User-Agent
            self.session.headers.update({'User-Agent': self.ua.random})
            
            response = self.session.get(article_url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'lxml')
            
            # 提取文章内容
            content_div = soup.find('div', class_='la-content')
            if not content_div:
                content_div = soup.find('div', class_='article-content')
            
            content = ""
            if content_div:
                # 提取所有段落文本
                paragraphs = content_div.find_all(['p', 'div'])
                content_parts = []
                for p in paragraphs:
                    text = p.get_text(strip=True)
                    if text and len(text) > 10:  # 过滤掉太短的文本
                        content_parts.append(text)
                content = '\n'.join(content_parts)
            
            # 提取发布时间
            time_elem = soup.find('span', class_='time') or soup.find('time')
            publish_time = ""
            if time_elem:
                publish_time = time_elem.get_text(strip=True)
            
            # 提取作者/来源
            source_elem = soup.find('span', class_='source')
            source = ""
            if source_elem:
                source = source_elem.get_text(strip=True)
            
            return {
                'content': content,
                'publish_time': publish_time,
                'source': source,
                'url': article_url
            }
            
        except requests.RequestException as e:
            print(f"获取文章内容失败 {article_id}: {e}")
            return None
        except Exception as e:
            print(f"解析文章内容失败 {article_id}: {e}")
            return None
    
    def crawl_today_articles(self, max_pages=50):
        """
        爬取当天的所有文章
        """
        print("开始爬取环球网国际新闻...")
        
        today = datetime.now().strftime('%Y-%m-%d')
        offset = 0
        limit = 24
        page = 1
        
        while page <= max_pages:
            print(f"\n正在爬取第 {page} 页 (offset: {offset})...")
            
            # 获取文章列表
            article_list = self.get_article_list(offset, limit)
            
            if not article_list:
                print("没有更多文章了")
                break
            
            # 检查是否还是今天的文章
            today_articles = []
            for article in article_list:
                # 转换时间戳
                ctime = int(article.get('ctime', 0)) / 1000
                article_date = datetime.fromtimestamp(ctime).strftime('%Y-%m-%d')
                
                if article_date == today:
                    today_articles.append(article)
                else:
                    print(f"发现非今日文章: {article_date}, 停止爬取")
                    break
            
            if not today_articles:
                print("没有找到今日文章，停止爬取")
                break
            
            # 爬取文章详细内容
            for article in tqdm(today_articles, desc=f"第{page}页文章"):
                article_id = article.get('aid')
                title = article.get('title', '')
                summary = article.get('summary', '')
                
                print(f"正在爬取: {title[:50]}...")
                
                # 获取详细内容
                detail = self.get_article_content(article_id)
                
                if detail:
                    article_data = {
                        'id': article_id,
                        'title': title,
                        'summary': summary,
                        'content': detail['content'],
                        'source': detail['source'] or article.get('source', {}).get('name', ''),
                        'publish_time': detail['publish_time'],
                        'url': detail['url'],
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    self.articles.append(article_data)
                
                # 避免请求过快
                time.sleep(1)
            
            # 如果这页的文章数量少于limit，说明没有更多了
            if len(today_articles) < limit:
                print("已爬取完所有今日文章")
                break
            
            offset += limit
            page += 1
            
            # 页面间隔
            time.sleep(2)
        
        print(f"\n爬取完成！共获取 {len(self.articles)} 篇文章")
        return self.articles
    
    def save_to_csv(self, filename=None):
        """
        保存到CSV文件
        """
        if not filename:
            filename = f"huanqiu_world_news_{datetime.now().strftime('%Y%m%d')}.csv"

        if not self.articles:
            print("没有数据可保存")
            return

        # 手动写CSV文件
        with open(filename, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)

            # 写入表头
            if self.articles:
                headers = list(self.articles[0].keys())
                writer.writerow(headers)

                # 写入数据
                for article in self.articles:
                    row = [article.get(header, '') for header in headers]
                    writer.writerow(row)

        print(f"数据已保存到: {filename}")
    
    def save_to_json(self, filename=None):
        """
        保存到JSON文件
        """
        if not filename:
            filename = f"huanqiu_world_news_{datetime.now().strftime('%Y%m%d')}.json"
        
        if not self.articles:
            print("没有数据可保存")
            return
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.articles, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {filename}")


def main():
    """
    主函数
    """
    spider = HuanqiuSpider()
    
    # 爬取今日文章
    articles = spider.crawl_today_articles(max_pages=20)
    
    if articles:
        # 保存数据
        spider.save_to_csv()
        spider.save_to_json()
        
        # 显示统计信息
        print(f"\n=== 爬取统计 ===")
        print(f"总文章数: {len(articles)}")
        print(f"平均标题长度: {sum(len(a['title']) for a in articles) / len(articles):.1f}")
        print(f"平均内容长度: {sum(len(a['content']) for a in articles) / len(articles):.1f}")
        
        # 显示前3篇文章标题
        print(f"\n=== 前3篇文章 ===")
        for i, article in enumerate(articles[:3], 1):
            print(f"{i}. {article['title']}")
            print(f"   来源: {article['source']}")
            print(f"   时间: {article['publish_time']}")
            print(f"   链接: {article['url']}")
            print()


if __name__ == "__main__":
    main()
