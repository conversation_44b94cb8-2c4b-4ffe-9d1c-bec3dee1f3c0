#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环球网国际新闻爬虫 - 修复版本
支持爬取文章列表和详细内容
"""

import requests
import json
import time
import csv
from datetime import datetime
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from tqdm import tqdm


class HuanqiuSpider:
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.base_url = "https://world.huanqiu.com"
        self.api_url = "https://world.huanqiu.com/api/list"
        
        # 国际新闻的节点ID
        self.nodes = [
            "/e3pmh22ph/e3pmh2398",
            "/e3pmh22ph/e3pmh26vv", 
            "/e3pmh22ph/e3pn6efsl",
            "/e3pmh22ph/efp8fqe21"
        ]
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://world.huanqiu.com/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        
        self.session.headers.update(self.headers)
        
        # 存储数据
        self.articles = []
        
    def get_article_list(self, offset=0, limit=24):
        """
        获取文章列表
        """
        params = {
            'node': ','.join([f'"{node}"' for node in self.nodes]),
            'offset': offset,
            'limit': limit
        }
        
        try:
            response = self.session.get(self.api_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return data.get('list', [])
            
        except requests.RequestException as e:
            print(f"获取文章列表失败: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return []
    
    def get_article_content(self, article_id):
        """
        获取文章详细内容
        """
        article_url = f"https://world.huanqiu.com/article/{article_id}"
        
        try:
            # 随机更换User-Agent
            headers = {
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://world.huanqiu.com/',
                'Connection': 'keep-alive',
            }
            
            response = self.session.get(article_url, headers=headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取文章内容 - 根据你提供的HTML结构
            content = ""
            content_div = soup.find('div', class_='content')
            if content_div:
                article_tag = content_div.find('article')
                if article_tag:
                    # 提取所有段落
                    paragraphs = article_tag.find_all('p')
                    content_parts = []
                    for p in paragraphs:
                        text = p.get_text(strip=True)
                        if text and len(text) > 5:  # 过滤掉太短的文本
                            content_parts.append(text)
                    content = '\n\n'.join(content_parts)
            
            # 如果上面的方法没找到内容，尝试其他选择器
            if not content:
                # 尝试其他可能的内容选择器
                selectors = [
                    '.la-content',
                    '.article-content', 
                    '.content article',
                    '.main-content',
                    '[data-type="rtext"]'
                ]
                
                for selector in selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        paragraphs = content_elem.find_all(['p', 'div'])
                        content_parts = []
                        for p in paragraphs:
                            text = p.get_text(strip=True)
                            if text and len(text) > 10:
                                content_parts.append(text)
                        if content_parts:
                            content = '\n\n'.join(content_parts)
                            break
            
            # 提取发布时间
            publish_time = ""
            time_selectors = [
                '.time',
                'time',
                '.publish-time',
                '.article-time'
            ]
            
            for selector in time_selectors:
                time_elem = soup.select_one(selector)
                if time_elem:
                    publish_time = time_elem.get_text(strip=True)
                    break
            
            # 提取作者/来源
            source = ""
            source_selectors = [
                '.source',
                '.article-source',
                '.author'
            ]
            
            for selector in source_selectors:
                source_elem = soup.select_one(selector)
                if source_elem:
                    source = source_elem.get_text(strip=True)
                    break
            
            return {
                'content': content,
                'publish_time': publish_time,
                'source': source,
                'url': article_url
            }
            
        except requests.RequestException as e:
            print(f"获取文章内容失败 {article_id}: {e}")
            return None
        except Exception as e:
            print(f"解析文章内容失败 {article_id}: {e}")
            return None
    
    def crawl_one_page(self, offset=0):
        """
        爬取一页文章 - 用于测试
        """
        print("开始爬取环球网国际新闻（测试版 - 仅一页）...")
        
        # 获取文章列表
        article_list = self.get_article_list(offset, 24)
        
        if not article_list:
            print("没有获取到文章列表")
            return []
        
        print(f"获取到 {len(article_list)} 篇文章")
        
        # 爬取文章详细内容
        for i, article in enumerate(tqdm(article_list, desc="爬取文章"), 1):
            article_id = article.get('aid')
            title = article.get('title', '')
            summary = article.get('summary', '')
            
            # 处理时间戳 - 修复时间解析问题
            ctime = article.get('ctime', 0)
            article_date = "未知日期"
            if ctime and str(ctime).isdigit():
                try:
                    # 时间戳可能是毫秒或秒，先尝试毫秒
                    if len(str(ctime)) > 10:
                        timestamp = int(ctime) / 1000
                    else:
                        timestamp = int(ctime)
                    
                    article_date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                except:
                    article_date = "时间解析失败"
            
            print(f"\n正在爬取第 {i} 篇: {title[:50]}...")
            print(f"文章ID: {article_id}")
            print(f"发布时间: {article_date}")
            
            if not article_id or article_id == "无ID":
                print("跳过无效文章ID")
                continue
            
            # 获取详细内容
            detail = self.get_article_content(article_id)
            
            if detail:
                article_data = {
                    'id': article_id,
                    'title': title,
                    'summary': summary,
                    'content': detail['content'],
                    'source': detail['source'] or article.get('source', {}).get('name', ''),
                    'publish_time': detail['publish_time'] or article_date,
                    'url': detail['url'],
                    'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # 显示内容预览
                content_preview = detail['content'][:200] + "..." if len(detail['content']) > 200 else detail['content']
                print(f"内容预览: {content_preview}")
                print(f"内容长度: {len(detail['content'])} 字符")
                
                self.articles.append(article_data)
            else:
                print("获取文章内容失败")
            
            # 避免请求过快
            time.sleep(1.5)
        
        print(f"\n爬取完成！共获取 {len(self.articles)} 篇文章")
        return self.articles
    
    def save_to_csv(self, filename=None):
        """
        保存到CSV文件
        """
        if not filename:
            filename = f"huanqiu_world_news_test_{datetime.now().strftime('%Y%m%d_%H%M')}.csv"
        
        if not self.articles:
            print("没有数据可保存")
            return
        
        # 手动写CSV文件
        with open(filename, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            
            # 写入表头
            if self.articles:
                headers = list(self.articles[0].keys())
                writer.writerow(headers)
                
                # 写入数据
                for article in self.articles:
                    row = [article.get(header, '') for header in headers]
                    writer.writerow(row)
        
        print(f"数据已保存到: {filename}")
    
    def save_to_json(self, filename=None):
        """
        保存到JSON文件
        """
        if not filename:
            filename = f"huanqiu_world_news_test_{datetime.now().strftime('%Y%m%d_%H%M')}.json"
        
        if not self.articles:
            print("没有数据可保存")
            return
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.articles, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {filename}")


def main():
    """
    主函数 - 测试版本
    """
    spider = HuanqiuSpider()
    
    # 爬取一页文章进行测试
    articles = spider.crawl_one_page(offset=0)
    
    if articles:
        # 保存数据
        spider.save_to_csv()
        spider.save_to_json()
        
        # 显示统计信息
        print(f"\n=== 爬取统计 ===")
        print(f"总文章数: {len(articles)}")
        if articles:
            avg_title_len = sum(len(a['title']) for a in articles) / len(articles)
            avg_content_len = sum(len(a['content']) for a in articles if a['content']) / len(articles)
            print(f"平均标题长度: {avg_title_len:.1f}")
            print(f"平均内容长度: {avg_content_len:.1f}")
        
        # 显示前3篇文章的详细信息
        print(f"\n=== 前3篇文章详情 ===")
        for i, article in enumerate(articles[:3], 1):
            print(f"\n{i}. {article['title']}")
            print(f"   来源: {article['source']}")
            print(f"   时间: {article['publish_time']}")
            print(f"   链接: {article['url']}")
            print(f"   摘要: {article['summary'][:100]}...")
            print(f"   内容: {article['content'][:200]}...")


if __name__ == "__main__":
    main()
