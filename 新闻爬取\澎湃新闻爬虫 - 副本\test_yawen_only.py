#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独测试要闻板块爬虫
"""

from thepaper_crawler import ThepaperNewsCrawler

def main():
    print("单独测试要闻板块爬虫")
    print("=" * 40)
    
    try:
        crawler = ThepaperNewsCrawler()
        
        # 直接调用要闻板块的爬取方法
        print("开始爬取要闻板块...")
        news_data = crawler.crawl_news_continuously(get_detail=False)
        
        if news_data:
            print(f"\n要闻板块获取到 {len(news_data)} 条新闻")
            
            # 显示前5条
            for i, news in enumerate(news_data[:5]):
                print(f"\n第{i+1}条:")
                print(f"标题: {news.get('title', 'N/A')}")
                print(f"时间: {news.get('pub_time', 'N/A')}")
                print(f"ID: {news.get('news_id', 'N/A')}")
                print(f"链接: {news.get('url', 'N/A')}")
                
            if len(news_data) > 5:
                print(f"\n... 还有 {len(news_data) - 5} 条新闻")
                
            # 统计信息
            print(f"\n=== 统计信息 ===")
            print(f"总数量: {len(news_data)}")
            
            # 按时间统计
            time_stats = {}
            for news in news_data:
                pub_time = news.get('pub_time', '')
                if pub_time:
                    time_stats[pub_time] = time_stats.get(pub_time, 0) + 1
            
            print(f"时间分布: {len(time_stats)} 个不同时间点")
            
        else:
            print("要闻板块未获取到数据")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
