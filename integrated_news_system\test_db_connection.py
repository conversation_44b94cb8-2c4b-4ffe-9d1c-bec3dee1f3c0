#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器数据库连接测试脚本
用于测试MySQL数据库连接和基本操作
"""

import os
import sys
import mysql.connector
from datetime import datetime

def load_env_config():
    """从.env文件加载数据库配置"""
    config = {}
    
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
    except FileNotFoundError:
        print("❌ .env 文件不存在")
        return None
    
    # 提取数据库配置
    db_config = {
        'host': config.get('DB_HOST', 'localhost'),
        'port': int(config.get('DB_PORT', 3306)),
        'user': config.get('DB_USER', 'root'),
        'password': config.get('DB_PASSWORD', ''),
        'database': config.get('DB_NAME', 'news_system'),
    }
    
    # 处理SSL配置
    ssl_mode = config.get('DB_SSL_MODE', 'DISABLED')
    if ssl_mode.upper() == 'DISABLED':
        db_config['ssl_disabled'] = True
    
    return db_config

def test_mysql_service():
    """测试MySQL服务是否运行"""
    print("🔍 检查MySQL服务状态...")
    
    # 尝试连接MySQL服务器（不指定数据库）
    try:
        config = load_env_config()
        if not config:
            return False
            
        # 临时配置，不指定数据库
        temp_config = config.copy()
        if 'database' in temp_config:
            del temp_config['database']
            
        conn = mysql.connector.connect(**temp_config)
        conn.close()
        print("✅ MySQL服务运行正常")
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ MySQL服务连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    config = load_env_config()
    if not config:
        return False
    
    try:
        print(f"📡 连接信息: {config['user']}@{config['host']}:{config['port']}/{config['database']}")
        
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        # 测试基本查询
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"✅ 数据库连接成功")
        print(f"📊 MySQL版本: {version}")
        
        cursor.close()
        conn.close()
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        
        # 提供具体的错误建议
        if "Access denied" in str(e):
            print("💡 建议检查用户名和密码是否正确")
        elif "Unknown database" in str(e):
            print("💡 建议检查数据库名称是否正确，或创建数据库")
        elif "Can't connect to MySQL server" in str(e):
            print("💡 建议检查MySQL服务是否启动，或检查主机地址和端口")
            
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_database_operations():
    """测试数据库基本操作"""
    print("🔍 测试数据库操作...")
    
    config = load_env_config()
    if not config:
        return False
    
    try:
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        # 测试创建测试表
        print("📝 测试创建表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_connection (
                id INT AUTO_INCREMENT PRIMARY KEY,
                test_time DATETIME,
                message VARCHAR(255)
            )
        """)
        
        # 测试插入数据
        print("📝 测试插入数据...")
        test_time = datetime.now()
        cursor.execute(
            "INSERT INTO test_connection (test_time, message) VALUES (%s, %s)",
            (test_time, "数据库连接测试成功")
        )
        
        # 测试查询数据
        print("📝 测试查询数据...")
        cursor.execute("SELECT COUNT(*) FROM test_connection")
        count = cursor.fetchone()[0]
        print(f"📊 测试表记录数: {count}")
        
        # 测试删除测试表
        print("📝 清理测试数据...")
        cursor.execute("DROP TABLE test_connection")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("✅ 数据库操作测试成功")
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 操作测试失败: {e}")
        return False

def check_required_tables():
    """检查必需的表是否存在"""
    print("🔍 检查系统表结构...")
    
    config = load_env_config()
    if not config:
        return False
    
    required_tables = ['articles', 'topics', 'news_items']
    
    try:
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        cursor.execute("SHOW TABLES")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📊 现有表: {existing_tables}")
        
        missing_tables = [table for table in required_tables if table not in existing_tables]
        
        if missing_tables:
            print(f"⚠️ 缺少表: {missing_tables}")
            print("💡 建议运行: python3 init_database.py 初始化数据库")
        else:
            print("✅ 所有必需表都存在")
        
        cursor.close()
        conn.close()
        return len(missing_tables) == 0
        
    except mysql.connector.Error as e:
        print(f"❌ 检查表结构失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 表检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 服务器数据库连接测试")
    print("=" * 50)
    
    # 检查依赖
    try:
        import mysql.connector
        print("✅ mysql-connector-python 已安装")
    except ImportError:
        print("❌ mysql-connector-python 未安装")
        print("💡 安装命令: pip3 install mysql-connector-python")
        return
    
    # 检查配置文件
    if not os.path.exists('.env'):
        print("❌ .env 配置文件不存在")
        return
    
    print("✅ .env 配置文件存在")
    
    # 显示配置信息
    config = load_env_config()
    if config:
        print(f"📡 数据库配置:")
        print(f"   主机: {config['host']}")
        print(f"   端口: {config['port']}")
        print(f"   用户: {config['user']}")
        print(f"   数据库: {config['database']}")
        print(f"   密码: {'*' * len(config['password'])}")
    
    print("\n" + "=" * 50)
    
    # 执行测试
    tests = [
        ("MySQL服务", test_mysql_service),
        ("数据库连接", test_database_connection),
        ("数据库操作", test_database_operations),
        ("表结构检查", check_required_tables),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}测试:")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
        print()
    
    # 总结
    print("=" * 50)
    print("📊 测试结果总结:")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有测试通过！数据库配置正常")
    else:
        print("⚠️ 部分测试失败，请检查配置")
        print("💡 常见解决方案:")
        print("   1. 检查MySQL服务是否启动: sudo systemctl start mysql")
        print("   2. 检查用户名密码是否正确")
        print("   3. 检查数据库是否存在: CREATE DATABASE news_system;")
        print("   4. 运行数据库初始化: python3 init_database.py")

if __name__ == "__main__":
    main()
