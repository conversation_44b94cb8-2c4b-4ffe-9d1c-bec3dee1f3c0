#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
澎湃新闻主控制器 - 整合所有板块爬虫
按照新闻爬虫开发规范输出标准格式数据
"""

import sys
import os
import time
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# 导入各板块爬虫
try:
    from thepaper_news_crawler import ThepaperNewsCrawler
    from thepaper_international_crawler import ThepaperInternationalCrawler
    from thepaper_finance_crawler import ThepaperFinanceCrawler
    from thepaper_depth_crawler import ThepaperDepthCrawler
    from thepaper_crawler import ThepaperYawenCrawler
except ImportError as e:
    print(f"导入爬虫模块失败: {e}")
    print("请确保所有板块爬虫文件都在当前目录下")
    sys.exit(1)

class ThepaperMasterCrawler:
    """澎湃新闻主控制器"""
    
    def __init__(self):
        self.crawlers = {
            'yawen': ThepaperYawenCrawler(),      # 要闻板块
            'news': ThepaperNewsCrawler(),        # 时事板块
            'international': ThepaperInternationalCrawler(),  # 国际板块
            'finance': ThepaperFinanceCrawler(),  # 财经板块
            'depth': ThepaperDepthCrawler()       # 深度板块
        }
        
        self.channel_names = {
            'yawen': '要闻',
            'news': '时事',
            'international': '国际',
            'finance': '财经',
            'depth': '深度'
        }
    
    def format_publish_time(self, pub_time, pub_time_long=None):
        """格式化发布时间为标准格式 MM-DD HH:MM"""
        try:
            if pub_time_long:
                # 使用精确时间戳
                dt = datetime.fromtimestamp(pub_time_long / 1000)
                return dt.strftime('%m-%d %H:%M')
            elif pub_time:
                # 处理相对时间
                if '小时前' in pub_time:
                    hours = int(pub_time.replace('小时前', '').strip())
                    dt = datetime.now() - timedelta(hours=hours)
                    return dt.strftime('%m-%d %H:%M')
                elif '分钟前' in pub_time:
                    minutes = int(pub_time.replace('分钟前', '').strip())
                    dt = datetime.now() - timedelta(minutes=minutes)
                    return dt.strftime('%m-%d %H:%M')
                elif '天前' in pub_time:
                    days = int(pub_time.replace('天前', '').strip())
                    dt = datetime.now() - timedelta(days=days)
                    return dt.strftime('%m-%d %H:%M')
                elif '刚刚' in pub_time:
                    return datetime.now().strftime('%m-%d %H:%M')
                else:
                    # 如果是其他格式，尝试直接返回
                    return pub_time
            else:
                # 没有时间信息，使用当前时间
                return datetime.now().strftime('%m-%d %H:%M')
        except:
            # 解析失败，使用当前时间
            return datetime.now().strftime('%m-%d %H:%M')
    
    def convert_to_standard_format(self, news_item, channel):
        """将澎湃新闻数据转换为标准格式"""
        try:
            # 获取内容，优先使用完整内容
            content = news_item.get('content', '')
            if not content:
                content = news_item.get('summary', '')
            if not content:
                content = news_item.get('title', '')
            
            # 格式化时间
            publish_time = self.format_publish_time(
                news_item.get('pub_time', ''),
                news_item.get('pub_time_long')
            )
            
            # 返回标准格式
            return {
                'title': news_item.get('title', ''),
                'url': news_item.get('url', ''),
                'content': content,
                'category': self.channel_names.get(channel, channel),
                'publish_time': publish_time,
                'source': 'thepaper'
            }
        except Exception as e:
            print(f"转换数据格式失败: {e}")
            return None
    
    def crawl_single_channel(self, channel, max_news_per_channel=None, get_detail=True):
        """爬取单个板块的新闻"""
        try:
            print(f"开始爬取{self.channel_names.get(channel, channel)}板块...")

            crawler = self.crawlers[channel]

            # 调用爬虫的持续爬取方法
            if hasattr(crawler, 'crawl_news_continuously'):
                raw_news = crawler.crawl_news_continuously(get_detail=get_detail)
            elif hasattr(crawler, 'crawl_depth_news_continuously'):  # 深度板块特殊方法
                raw_news = crawler.crawl_depth_news_continuously(get_detail=get_detail)
            elif hasattr(crawler, 'crawl_depth_news'):  # 深度板块备用方法
                raw_news = crawler.crawl_depth_news(max_pages=10, get_detail=get_detail)
            else:
                print(f"警告: {channel}板块爬虫没有标准的爬取方法")
                return []

            if not raw_news:
                print(f"{self.channel_names.get(channel, channel)}板块未获取到数据")
                return []

            # 转换格式（不限制数量或按指定数量限制）
            if max_news_per_channel:
                limited_news = raw_news[:max_news_per_channel]
            else:
                limited_news = raw_news  # 获取全部

            standard_news = []

            for news_item in limited_news:
                converted = self.convert_to_standard_format(news_item, channel)
                if converted and converted['title'] and converted['url']:
                    standard_news.append(converted)

            print(f"{self.channel_names.get(channel, channel)}板块获取到 {len(standard_news)} 条新闻")
            return standard_news

        except Exception as e:
            print(f"爬取{self.channel_names.get(channel, channel)}板块失败: {e}")
            return []
    
    def get_news(self, max_news=None, get_detail=True, channels=None, parallel=False):
        """
        获取澎湃新闻数据 - 主入口函数

        Args:
            max_news (int): 最大新闻数量，None表示不限制
            get_detail (bool): 是否获取详细内容，默认True
            channels (list): 指定爬取的板块，默认全部
            parallel (bool): 是否并行爬取，默认True

        Returns:
            list: 标准格式的新闻数据列表
        """
        if channels is None:
            channels = list(self.crawlers.keys())

        print(f"开始爬取澎湃新闻 - 目标板块: {[self.channel_names.get(ch, ch) for ch in channels]}")
        if max_news:
            max_news_per_channel = max(1, max_news // len(channels))
            print(f"每个板块最多获取: {max_news_per_channel} 条新闻")
        else:
            max_news_per_channel = None
            print("获取所有今日新闻（不限制数量）")
        print(f"是否获取详细内容: {get_detail}")
        print(f"是否并行爬取: {parallel}")
        print("=" * 60)
        
        all_news = []
        
        if parallel and len(channels) > 1:
            # 并行爬取
            with ThreadPoolExecutor(max_workers=min(len(channels), 3)) as executor:
                future_to_channel = {
                    executor.submit(self.crawl_single_channel, channel, max_news_per_channel, get_detail): channel
                    for channel in channels
                }
                
                for future in as_completed(future_to_channel):
                    channel = future_to_channel[future]
                    try:
                        channel_news = future.result()
                        all_news.extend(channel_news)
                    except Exception as e:
                        print(f"并行爬取{channel}板块异常: {e}")
        else:
            # 串行爬取
            for channel in channels:
                channel_news = self.crawl_single_channel(channel, max_news_per_channel, get_detail)
                all_news.extend(channel_news)
                
                # 添加延时避免请求过快
                if len(channels) > 1:
                    time.sleep(2)
        
        # 按时间排序（最新的在前面）
        try:
            all_news.sort(key=lambda x: x['publish_time'], reverse=True)
        except:
            pass  # 排序失败不影响结果
        
        # 限制总数量（如果指定了max_news）
        if max_news:
            final_news = all_news[:max_news]
        else:
            final_news = all_news

        print("=" * 60)
        print(f"澎湃新闻爬取完成!")
        print(f"总计获取: {len(final_news)} 条新闻")
        
        # 按板块统计
        channel_stats = {}
        for news in final_news:
            category = news['category']
            channel_stats[category] = channel_stats.get(category, 0) + 1
        
        for category, count in channel_stats.items():
            print(f"  {category}: {count} 条")
        
        return final_news

def get_news(max_news=None, get_detail=True):
    """
    澎湃新闻爬虫主入口函数 - 符合标准接口规范

    Args:
        max_news (int): 最大新闻数量，None表示获取全部
        get_detail (bool): 是否获取详细内容，默认True

    Returns:
        list: 标准格式的新闻数据列表
    """
    crawler = ThepaperMasterCrawler()
    return crawler.get_news(max_news=max_news, get_detail=get_detail)

def main():
    """主函数 - 用于测试"""
    print("澎湃新闻主控制器 - 全量爬取模式")
    print("=" * 50)

    try:
        # 全量爬取所有板块的今日新闻，包含详细内容
        print("开始全量爬取澎湃新闻（获取详细内容）...")
        news_data = get_news(max_news=None, get_detail=True)

        if news_data:
            print(f"\n=== 爬取结果预览 ===")
            for i, news in enumerate(news_data[:5]):
                print(f"\n第{i+1}条:")
                print(f"标题: {news['title']}")
                print(f"链接: {news['url']}")
                print(f"分类: {news['category']}")
                print(f"时间: {news['publish_time']}")
                print(f"内容长度: {len(news['content'])}字")
                print(f"来源: {news['source']}")

            if len(news_data) > 5:
                print(f"\n... 还有 {len(news_data) - 5} 条新闻")

            # 保存完整数据
            import json
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"thepaper_all_news_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(news_data, f, ensure_ascii=False, indent=2)

            print(f"\n完整数据已保存到: {filename}")
        else:
            print("未获取到任何新闻数据")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
