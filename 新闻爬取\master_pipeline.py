#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻处理主流水线脚本 - 一键式自动化处理
从新闻爬取 → 向量化处理 → 向量数据库存储的完整流程

功能特性:
- 🚀 一键式全流程自动化
- 🔄 完整的错误处理和重试机制
- 📊 详细的进度监控和统计信息
- 🛡️ 数据完整性检查和验证
- 📁 自动文件管理和清理
- 🔍 实时状态监控和日志记录
- ⚙️ 灵活的参数配置和自定义选项

作者: AI Assistant
创建时间: 2025-07-07
版本: 1.0.0
"""

import os
import sys
import time
import json
import shutil
import logging
import logging.handlers
import argparse
import traceback
import signal
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入项目模块
try:
    from unified_news_crawler import UnifiedNewsCrawler
    from csv_to_vector_processor import CSVNewsVectorProcessor
    from news_search import NewsSearchEngine
    from vector_database import VectorDatabase
    from config import SystemConfig
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有依赖模块都在当前目录下")
    sys.exit(1)


class MasterPipeline:
    """新闻处理主流水线管理器"""
    
    def __init__(self, custom_config: Optional[Dict] = None):
        """
        初始化主流水线

        Args:
            custom_config: 自定义配置字典，覆盖默认配置
        """
        # 使用现有的SystemConfig作为基础配置
        SystemConfig.ensure_directories()

        self.config = self._merge_config(custom_config or {})
        self.stats = self._init_stats()
        self.logger = self._setup_logging()
        self.start_time = datetime.now()

        # 初始化组件
        self.crawler = None
        self.processor = None
        self.search_engine = None
        self.vector_db = None

        self.logger.info("🚀 主流水线初始化完成")
    
    def _merge_config(self, custom_config: Dict) -> Dict:
        """基于SystemConfig合并自定义配置"""
        # 基于SystemConfig的默认配置
        default_config = {
            # 爬虫配置
            'crawler': {
                'max_news_per_source': 30,
                'get_detail': True,
                'get_all': False,
                'timeout': 300,  # 5分钟超时
                'retry_attempts': 3,
                'retry_delay': 5
            },

            # 向量处理配置
            'processor': {
                'batch_size': SystemConfig.BATCH_SIZE,
                'max_workers': SystemConfig.MAX_WORKERS,
                'max_news': None,
                'timeout': 600,  # 10分钟超时
                'retry_attempts': 2,
                'retry_delay': 10
            },

            # 数据库配置
            'database': {
                'vector_db_path': SystemConfig.VECTOR_DB_PATH,
                'vector_dimension': SystemConfig.VECTOR_DIMENSION,
                'backup_enabled': True,
                'auto_optimize': True
            },

            # 文件管理配置
            'file_management': {
                'auto_cleanup': SystemConfig.AUTO_CLEANUP_CSV,
                'archive_processed': SystemConfig.ARCHIVE_PROCESSED_FILES,
                'keep_latest_csv': not SystemConfig.AUTO_CLEANUP_CSV,
                'max_archive_days': SystemConfig.MAX_CSV_AGE_DAYS
            },

            # 日志配置
            'logging': {
                'level': SystemConfig.LOG_LEVEL,
                'file_enabled': True,
                'console_enabled': True,
                'max_log_size': 10 * 1024 * 1024,  # 10MB
                'backup_count': 5
            },

            # 验证配置
            'validation': {
                'min_news_count': 1,
                'max_duplicate_ratio': 0.1,
                'min_content_length': SystemConfig.MIN_CONTENT_LENGTH,
                'quality_check_enabled': True
            }
        }

        # 深度合并自定义配置
        for key, value in custom_config.items():
            if key in default_config and isinstance(value, dict):
                default_config[key].update(value)
            else:
                default_config[key] = value

        return default_config
    
    def _init_stats(self) -> Dict:
        """初始化统计信息"""
        return {
            'pipeline_start_time': datetime.now().isoformat(),
            'total_execution_time': 0,
            'stages': {
                'crawling': {'status': 'pending', 'duration': 0, 'news_count': 0},
                'processing': {'status': 'pending', 'duration': 0, 'processed_count': 0},
                'storage': {'status': 'pending', 'duration': 0, 'stored_count': 0}
            },
            'files': {
                'csv_file': None,
                'archive_file': None,
                'log_file': None
            },
            'errors': [],
            'warnings': []
        }
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('MasterPipeline')
        logger.setLevel(getattr(logging, self.config['logging']['level']))

        # 清除现有处理器
        logger.handlers.clear()

        # 创建格式器 - 使用SystemConfig的格式
        formatter = logging.Formatter(
            SystemConfig.LOG_FORMAT,
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 控制台处理器
        if self.config['logging']['console_enabled']:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        # 文件处理器 - 使用SystemConfig的方法
        if self.config['logging']['file_enabled']:
            log_file = SystemConfig.get_log_file_path("master_pipeline")
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=self.config['logging']['max_log_size'],
                backupCount=self.config['logging']['backup_count'],
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

            self.stats['files']['log_file'] = log_file

        return logger
    

    
    def run_full_pipeline(self, 
                         max_news_per_source: int = None,
                         get_detail: bool = None,
                         get_all: bool = None) -> Dict:
        """
        运行完整的新闻处理流水线
        
        Args:
            max_news_per_source: 每个来源最大新闻数量
            get_detail: 是否获取详细内容
            get_all: 是否获取所有新闻
            
        Returns:
            流水线执行结果统计
        """
        self.logger.info("🚀 开始执行完整新闻处理流水线")
        self.logger.info(f"⚙️ 配置参数: max_news={max_news_per_source}, detail={get_detail}, all={get_all}")
        
        try:
            # 阶段1: 新闻爬取
            csv_file, news_count = self._stage_1_crawling(max_news_per_source, get_detail, get_all)
            
            # 阶段2: 向量化处理
            processed_count = self._stage_2_processing(csv_file)
            
            # 阶段3: 数据库存储验证
            stored_count = self._stage_3_storage_verification()
            
            # 阶段4: 文件管理和清理
            self._stage_4_file_management(csv_file)
            
            # 生成最终报告
            final_report = self._generate_final_report()
            
            self.logger.info("🎉 完整流水线执行成功!")
            return final_report
            
        except Exception as e:
            self.logger.error(f"❌ 流水线执行失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            self.stats['errors'].append({
                'stage': 'pipeline',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            raise
        
        finally:
            # 记录总执行时间
            self.stats['total_execution_time'] = (datetime.now() - self.start_time).total_seconds()
            self.logger.info(f"⏱️ 总执行时间: {self.stats['total_execution_time']:.2f} 秒")

    def _stage_1_crawling(self, max_news_per_source: int = None,
                         get_detail: bool = None,
                         get_all: bool = None) -> Tuple[str, int]:
        """
        阶段1: 新闻爬取

        Returns:
            (csv_file_path, news_count)
        """
        stage_start = time.time()
        self.logger.info("📰 阶段1: 开始新闻爬取")

        try:
            # 更新状态
            self.stats['stages']['crawling']['status'] = 'running'

            # 使用配置或参数
            crawler_config = self.config['crawler']
            max_news = max_news_per_source or crawler_config['max_news_per_source']
            detail = get_detail if get_detail is not None else crawler_config['get_detail']
            all_news = get_all if get_all is not None else crawler_config['get_all']

            self.logger.info(f"   参数: max_news={max_news}, detail={detail}, all={all_news}")

            # 创建爬虫实例
            self.crawler = UnifiedNewsCrawler(
                max_news_per_source=max_news,
                get_detail=detail,
                get_all=all_news
            )

            # 执行爬取（带重试机制）
            csv_file, news_count, news_data = self._execute_with_retry(
                self.crawler.crawl_all,
                max_attempts=crawler_config['retry_attempts'],
                delay=crawler_config['retry_delay'],
                timeout=crawler_config['timeout'],
                operation_name="新闻爬取"
            )

            # 验证爬取结果
            self._validate_crawling_results(csv_file, news_count, news_data)

            # 更新统计信息
            self.stats['stages']['crawling'].update({
                'status': 'completed',
                'duration': time.time() - stage_start,
                'news_count': news_count
            })
            self.stats['files']['csv_file'] = csv_file

            self.logger.info(f"✅ 阶段1完成: 爬取 {news_count} 条新闻，保存到 {csv_file}")
            return csv_file, news_count

        except Exception as e:
            self.stats['stages']['crawling']['status'] = 'failed'
            self.stats['stages']['crawling']['duration'] = time.time() - stage_start
            self.logger.error(f"❌ 阶段1失败: {e}")
            raise

    def _stage_2_processing(self, csv_file: str) -> int:
        """
        阶段2: 向量化处理

        Args:
            csv_file: CSV文件路径

        Returns:
            processed_count: 处理的新闻数量
        """
        stage_start = time.time()
        self.logger.info("🔄 阶段2: 开始向量化处理")

        try:
            # 更新状态
            self.stats['stages']['processing']['status'] = 'running'

            # 检查CSV文件
            if not os.path.exists(csv_file):
                raise FileNotFoundError(f"CSV文件不存在: {csv_file}")

            self.logger.info(f"   处理文件: {csv_file}")

            # 创建处理器实例
            processor_config = self.config['processor']
            from config import get_embedding_config
            embedding_config = get_embedding_config()
            self.processor = CSVNewsVectorProcessor(
                embedding_config=embedding_config,
                vector_db_path=SystemConfig.VECTOR_DB_PATH
            )

            # 执行向量化处理（带重试机制）
            def process_csv():
                return self.processor.process_csv_file(
                    csv_file_path=csv_file,
                    batch_size=processor_config['batch_size'],
                    max_news=processor_config['max_news'],
                    max_workers=processor_config['max_workers']
                )

            self._execute_with_retry(
                process_csv,
                max_attempts=processor_config['retry_attempts'],
                delay=processor_config['retry_delay'],
                timeout=processor_config['timeout'],
                operation_name="向量化处理"
            )

            # 获取处理统计
            processor_stats = self.processor.stats
            processed_count = processor_stats.get('successful_vectorized', 0)

            # 验证处理结果
            self._validate_processing_results(processed_count, processor_stats)

            # 更新统计信息
            self.stats['stages']['processing'].update({
                'status': 'completed',
                'duration': time.time() - stage_start,
                'processed_count': processed_count,
                'processor_stats': processor_stats
            })

            self.logger.info(f"✅ 阶段2完成: 处理 {processed_count} 条新闻向量")
            return processed_count

        except Exception as e:
            self.stats['stages']['processing']['status'] = 'failed'
            self.stats['stages']['processing']['duration'] = time.time() - stage_start
            self.logger.error(f"❌ 阶段2失败: {e}")
            raise

    def _stage_3_storage_verification(self) -> int:
        """
        阶段3: 数据库存储验证

        Returns:
            stored_count: 存储的新闻数量
        """
        stage_start = time.time()
        self.logger.info("💾 阶段3: 开始数据库存储验证")

        try:
            # 更新状态
            self.stats['stages']['storage']['status'] = 'running'

            # 创建数据库实例
            db_config = self.config['database']
            self.vector_db = VectorDatabase(
                db_path=db_config['vector_db_path'],
                vector_dim=db_config['vector_dimension']
            )

            # 加载数据库
            self.vector_db.load_database()

            # 获取存储统计
            db_stats = self.vector_db.get_stats()
            stored_count = db_stats.get('total_documents', 0)

            self.logger.info(f"   数据库路径: {db_config['vector_db_path']}")
            self.logger.info(f"   存储文档数: {stored_count}")

            # 验证存储结果
            self._validate_storage_results(stored_count, db_stats)

            # 数据库优化（如果启用）
            if db_config.get('auto_optimize', False):
                self.logger.info("🔧 执行数据库优化...")
                self.vector_db.optimize_database()

            # 数据库备份（如果启用）
            if db_config.get('backup_enabled', False):
                backup_file = self._create_database_backup()
                self.logger.info(f"💾 数据库备份: {backup_file}")

            # 更新统计信息
            self.stats['stages']['storage'].update({
                'status': 'completed',
                'duration': time.time() - stage_start,
                'stored_count': stored_count,
                'database_stats': db_stats
            })

            self.logger.info(f"✅ 阶段3完成: 验证 {stored_count} 条存储记录")
            return stored_count

        except Exception as e:
            self.stats['stages']['storage']['status'] = 'failed'
            self.stats['stages']['storage']['duration'] = time.time() - stage_start
            self.logger.error(f"❌ 阶段3失败: {e}")
            raise

    def _stage_4_file_management(self, csv_file: str):
        """
        阶段4: 文件管理和清理

        Args:
            csv_file: 处理的CSV文件路径
        """
        self.logger.info("📁 阶段4: 开始文件管理和清理")

        try:
            file_config = self.config['file_management']

            # 归档处理过的CSV文件
            if file_config.get('archive_processed', True):
                archive_file = self._archive_csv_file(csv_file)
                self.stats['files']['archive_file'] = archive_file
                self.logger.info(f"📦 文件已归档: {archive_file}")

            # 清理旧文件
            if file_config.get('auto_cleanup', True):
                cleaned_count = self._cleanup_old_files(file_config.get('max_archive_days', 30))
                self.logger.info(f"🧹 清理了 {cleaned_count} 个旧文件")

            # 保留最新的CSV文件
            if not file_config.get('keep_latest_csv', True):
                if os.path.exists(csv_file):
                    os.remove(csv_file)
                    self.logger.info(f"🗑️ 删除临时CSV文件: {csv_file}")

            self.logger.info("✅ 阶段4完成: 文件管理完成")

        except Exception as e:
            self.logger.warning(f"⚠️ 文件管理部分失败: {e}")
            self.stats['warnings'].append({
                'stage': 'file_management',
                'warning': str(e),
                'timestamp': datetime.now().isoformat()
            })

    def _execute_with_retry(self, func, max_attempts: int = 3, delay: int = 5,
                           timeout: int = 300, operation_name: str = "操作"):
        """
        带重试机制的函数执行器

        Args:
            func: 要执行的函数
            max_attempts: 最大重试次数
            delay: 重试间隔(秒)
            timeout: 超时时间(秒)
            operation_name: 操作名称(用于日志)

        Returns:
            函数执行结果
        """
        import signal
        import platform

        def timeout_handler(signum, frame):
            raise TimeoutError(f"{operation_name}超时")

        for attempt in range(1, max_attempts + 1):
            try:
                self.logger.info(f"🔄 执行{operation_name} (尝试 {attempt}/{max_attempts})")

                # 设置超时 (仅在非Windows系统上)
                if platform.system() != 'Windows' and hasattr(signal, 'SIGALRM'):
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(timeout)

                try:
                    result = func()
                    if platform.system() != 'Windows' and hasattr(signal, 'SIGALRM'):
                        signal.alarm(0)  # 取消超时
                    self.logger.info(f"✅ {operation_name}成功")
                    return result
                finally:
                    if platform.system() != 'Windows' and hasattr(signal, 'SIGALRM'):
                        signal.alarm(0)  # 确保取消超时

            except Exception as e:
                self.logger.warning(f"⚠️ {operation_name}失败 (尝试 {attempt}/{max_attempts}): {e}")

                if attempt == max_attempts:
                    self.logger.error(f"❌ {operation_name}最终失败，已达到最大重试次数")
                    raise

                if attempt < max_attempts:
                    self.logger.info(f"⏳ 等待 {delay} 秒后重试...")
                    time.sleep(delay)

    def _validate_crawling_results(self, csv_file: str, news_count: int, news_data: List[Dict]):
        """验证爬取结果"""
        validation_config = self.config['validation']

        # 检查最小新闻数量
        min_count = validation_config.get('min_news_count', 1)
        if news_count < min_count:
            raise ValueError(f"爬取新闻数量({news_count})低于最小要求({min_count})")

        # 检查CSV文件
        if not csv_file or not os.path.exists(csv_file):
            raise FileNotFoundError(f"CSV文件不存在或无效: {csv_file}")

        # 检查文件大小
        file_size = os.path.getsize(csv_file)
        if file_size == 0:
            raise ValueError("CSV文件为空")

        # 质量检查
        if validation_config.get('quality_check_enabled', True):
            self._perform_quality_check(news_data)

        self.logger.info(f"✅ 爬取结果验证通过: {news_count}条新闻, 文件大小{file_size}字节")

    def _validate_processing_results(self, processed_count: int, processor_stats: Dict):
        """验证处理结果"""
        validation_config = self.config['validation']

        # 检查处理数量
        if processed_count == 0:
            # 检查是否所有新闻都被跳过（重复或低质量）
            skipped_duplicate = processor_stats.get('skipped_duplicate', 0)
            skipped_low_quality = processor_stats.get('skipped_low_quality', 0)
            total_skipped = skipped_duplicate + skipped_low_quality

            if total_skipped > 0:
                self.logger.info(f"ℹ️ 所有新闻都被跳过: 重复{skipped_duplicate}条, 低质量{skipped_low_quality}条")
            else:
                raise ValueError("没有成功处理任何新闻")

        # 检查失败率
        total_processed = processor_stats.get('total_processed', 0)
        failed_count = processor_stats.get('failed_vectorized', 0)

        if total_processed > 0:
            failure_rate = failed_count / total_processed
            if failure_rate > 0.5:  # 失败率超过50%
                raise ValueError(f"处理失败率过高: {failure_rate:.2%}")

        self.logger.info(f"✅ 处理结果验证通过: 成功{processed_count}条, 失败{failed_count}条")

    def _validate_storage_results(self, stored_count: int, db_stats: Dict):
        """验证存储结果"""
        if stored_count == 0:
            raise ValueError("数据库中没有存储任何文档")

        # 数据库完整性检查（简化版）
        if stored_count == 0:
            raise ValueError("数据库中没有存储任何文档")

        self.logger.info(f"✅ 存储结果验证通过: {stored_count}条文档")

    def _perform_quality_check(self, news_data: List[Dict]):
        """执行新闻质量检查"""
        validation_config = self.config['validation']
        min_content_length = validation_config.get('min_content_length', 20)

        quality_issues = []

        for i, news in enumerate(news_data):
            # 检查内容长度
            content = news.get('content', '')
            if len(content) < min_content_length:
                quality_issues.append(f"新闻{i+1}内容过短({len(content)}字符)")

            # 检查必要字段
            required_fields = ['title', 'url', 'media', 'content']
            for field in required_fields:
                if not news.get(field):
                    quality_issues.append(f"新闻{i+1}缺少{field}字段")

        if quality_issues:
            self.logger.warning(f"⚠️ 发现 {len(quality_issues)} 个质量问题")
            for issue in quality_issues[:5]:  # 只显示前5个问题
                self.logger.warning(f"   - {issue}")

            # 如果质量问题过多，发出警告
            if len(quality_issues) > len(news_data) * 0.3:
                self.stats['warnings'].append({
                    'stage': 'quality_check',
                    'warning': f'质量问题过多: {len(quality_issues)}/{len(news_data)}',
                    'timestamp': datetime.now().isoformat()
                })

    def _archive_csv_file(self, csv_file: str) -> str:
        """归档CSV文件"""
        if not os.path.exists(csv_file):
            return None

        # 创建归档文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = os.path.basename(csv_file)
        archive_filename = f"archived_{timestamp}_{filename}"
        archive_path = os.path.join(SystemConfig.ARCHIVE_DIR, archive_filename)

        # 复制文件到归档目录
        shutil.copy2(csv_file, archive_path)

        return archive_path

    def _cleanup_old_files(self, max_days: int) -> int:
        """清理旧文件"""
        cutoff_date = datetime.now() - timedelta(days=max_days)
        cleaned_count = 0

        # 清理归档目录
        archive_dir = Path(SystemConfig.ARCHIVE_DIR)
        if archive_dir.exists():
            for file_path in archive_dir.iterdir():
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        file_path.unlink()
                        cleaned_count += 1

        # 清理日志目录
        log_dir = Path(SystemConfig.LOG_DIR)
        if log_dir.exists():
            for file_path in log_dir.iterdir():
                if file_path.is_file() and file_path.suffix == '.log':
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        file_path.unlink()
                        cleaned_count += 1

        return cleaned_count

    def _create_database_backup(self) -> str:
        """创建数据库备份"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = os.path.join(SystemConfig.ARCHIVE_DIR, f"db_backup_{timestamp}")
        os.makedirs(backup_dir, exist_ok=True)

        # 备份向量数据库文件
        db_path = Path(SystemConfig.VECTOR_DB_PATH)
        if db_path.exists():
            for file_path in db_path.iterdir():
                if file_path.is_file():
                    shutil.copy2(file_path, os.path.join(backup_dir, file_path.name))

        return backup_dir

    def _generate_final_report(self) -> Dict:
        """生成最终报告"""
        total_time = self.stats['total_execution_time']

        report = {
            'pipeline_summary': {
                'status': 'success',
                'total_execution_time': f"{total_time:.2f}秒",
                'start_time': self.stats['pipeline_start_time'],
                'end_time': datetime.now().isoformat()
            },
            'stage_results': {},
            'file_outputs': self.stats['files'],
            'performance_metrics': {
                'crawling_speed': 0,
                'processing_speed': 0,
                'overall_efficiency': 0
            },
            'quality_metrics': {
                'error_count': len(self.stats['errors']),
                'warning_count': len(self.stats['warnings']),
                'success_rate': 0
            },
            'recommendations': []
        }

        # 计算各阶段结果
        for stage_name, stage_data in self.stats['stages'].items():
            report['stage_results'][stage_name] = {
                'status': stage_data['status'],
                'duration': f"{stage_data['duration']:.2f}秒",
                'success': stage_data['status'] == 'completed'
            }

        # 计算性能指标
        crawling_stage = self.stats['stages']['crawling']
        processing_stage = self.stats['stages']['processing']

        if crawling_stage['duration'] > 0:
            report['performance_metrics']['crawling_speed'] = \
                crawling_stage['news_count'] / crawling_stage['duration']

        if processing_stage['duration'] > 0:
            report['performance_metrics']['processing_speed'] = \
                processing_stage['processed_count'] / processing_stage['duration']

        if total_time > 0:
            total_items = crawling_stage['news_count']
            report['performance_metrics']['overall_efficiency'] = total_items / total_time

        # 计算成功率
        total_stages = len(self.stats['stages'])
        successful_stages = sum(1 for stage in self.stats['stages'].values()
                               if stage['status'] == 'completed')
        report['quality_metrics']['success_rate'] = successful_stages / total_stages

        # 生成建议
        recommendations = []

        if report['quality_metrics']['error_count'] > 0:
            recommendations.append("检查错误日志，解决失败的操作")

        if report['performance_metrics']['crawling_speed'] < 0.1:
            recommendations.append("考虑优化网络连接或增加爬虫并发数")

        if report['performance_metrics']['processing_speed'] < 0.5:
            recommendations.append("考虑增加处理线程数或优化向量化API")

        if not recommendations:
            recommendations.append("流水线运行良好，无需特别优化")

        report['recommendations'] = recommendations

        return report

    def print_final_report(self, report: Dict):
        """打印最终报告"""
        print("\n" + "="*80)
        print("🎉 新闻处理流水线执行报告")
        print("="*80)

        # 基本信息
        summary = report['pipeline_summary']
        print(f"📊 执行状态: {summary['status']}")
        print(f"⏱️ 总执行时间: {summary['total_execution_time']}")
        print(f"🕐 开始时间: {summary['start_time']}")
        print(f"🕐 结束时间: {summary['end_time']}")

        # 阶段结果
        print(f"\n📋 各阶段执行结果:")
        for stage_name, stage_result in report['stage_results'].items():
            status_icon = "✅" if stage_result['success'] else "❌"
            print(f"   {status_icon} {stage_name}: {stage_result['status']} ({stage_result['duration']})")

        # 性能指标
        print(f"\n⚡ 性能指标:")
        metrics = report['performance_metrics']
        print(f"   📰 爬取速度: {metrics['crawling_speed']:.2f} 条/秒")
        print(f"   🔄 处理速度: {metrics['processing_speed']:.2f} 条/秒")
        print(f"   🎯 整体效率: {metrics['overall_efficiency']:.2f} 条/秒")

        # 质量指标
        print(f"\n🎯 质量指标:")
        quality = report['quality_metrics']
        print(f"   ✅ 成功率: {quality['success_rate']:.1%}")
        print(f"   ❌ 错误数: {quality['error_count']}")
        print(f"   ⚠️ 警告数: {quality['warning_count']}")

        # 输出文件
        print(f"\n📁 输出文件:")
        for file_type, file_path in report['file_outputs'].items():
            if file_path:
                print(f"   📄 {file_type}: {file_path}")

        # 建议
        print(f"\n💡 优化建议:")
        for i, recommendation in enumerate(report['recommendations'], 1):
            print(f"   {i}. {recommendation}")

        print("\n" + "="*80)

    def run_search_demo(self, query: str = "人工智能"):
        """运行搜索演示"""
        try:
            self.logger.info(f"🔍 运行搜索演示: {query}")

            # 创建搜索引擎
            if not self.search_engine:
                self.search_engine = NewsSearchEngine(SystemConfig.VECTOR_DB_PATH)

            # 执行搜索
            results = self.search_engine.search(query, top_k=5)

            print(f"\n🔍 搜索演示结果 (查询: {query})")
            print("-" * 60)

            if results:
                for i, result in enumerate(results, 1):
                    print(f"{i}. {result['metadata']['title']}")
                    print(f"   来源: {result['metadata']['media']}")
                    print(f"   相似度: {result['similarity']:.3f}")
                    print(f"   URL: {result['metadata']['url']}")
                    print()
            else:
                print("没有找到相关结果")

            return results

        except Exception as e:
            self.logger.error(f"❌ 搜索演示失败: {e}")
            return []


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="新闻处理主流水线 - 一键式自动化处理",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本使用 - 每个来源30条新闻，获取详细内容
  python master_pipeline.py

  # 自定义新闻数量
  python master_pipeline.py 50

  # 获取所有新闻
  python master_pipeline.py all

  # 不获取详细内容
  python master_pipeline.py 20 false

  # 仅运行搜索演示
  python master_pipeline.py search "人工智能"

  # 显示数据库统计信息
  python master_pipeline.py stats

  # 组合使用
  python master_pipeline.py 10 true 5 2  # 10条新闻，详细内容，批次5，线程2
        """
    )

    # 位置参数 - 简洁模式
    parser.add_argument(
        'news_count', nargs='?', default='30',
        help='每个来源新闻数量 (数字) 或特殊命令 (all/stats/search)'
    )
    parser.add_argument(
        'get_detail', nargs='?', default='true',
        help='是否获取详细内容 (true/false，默认: true)'
    )
    parser.add_argument(
        'batch_size', nargs='?', type=int, default=10,
        help='批处理大小 (默认: 10)'
    )
    parser.add_argument(
        'max_workers', nargs='?', type=int, default=3,
        help='最大线程数 (默认: 3)'
    )
    parser.add_argument(
        'search_query', nargs='?', default='人工智能',
        help='搜索查询词 (仅在search模式下使用，默认: 人工智能)'
    )

    # 可选参数 (向后兼容)
    optional_group = parser.add_argument_group('可选参数')
    optional_group.add_argument(
        '--verbose', '-v', action='store_true',
        help='详细输出模式'
    )
    optional_group.add_argument(
        '--quiet', '-q', action='store_true',
        help='静默模式 (仅输出错误)'
    )
    optional_group.add_argument(
        '--config', type=str,
        help='自定义配置文件路径 (JSON格式)'
    )
    optional_group.add_argument(
        '--output-report', type=str,
        help='保存执行报告到指定文件'
    )

    return parser


def load_custom_config(config_file: str) -> Dict:
    """加载自定义配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return {}


def parse_simple_args(args):
    """解析简化的位置参数"""
    # 解析第一个参数
    news_count = args.news_count.lower()

    # 特殊命令
    if news_count == 'stats':
        return {
            'mode': 'stats_only',
            'max_news': 30,
            'get_detail': True,
            'batch_size': args.batch_size,
            'max_workers': args.max_workers,
            'search_query': args.search_query
        }
    elif news_count == 'search':
        return {
            'mode': 'search_only',
            'max_news': 30,
            'get_detail': True,
            'batch_size': args.batch_size,
            'max_workers': args.max_workers,
            'search_query': args.search_query
        }
    elif news_count == 'all':
        return {
            'mode': 'normal',
            'max_news': None,  # 获取所有
            'get_detail': True,
            'batch_size': args.batch_size,
            'max_workers': args.max_workers,
            'search_query': args.search_query
        }
    else:
        # 数字参数
        try:
            max_news = int(news_count)
        except ValueError:
            max_news = 30

        # 解析详细内容参数
        get_detail = args.get_detail.lower() not in ['false', 'f', '0', 'no']

        return {
            'mode': 'normal',
            'max_news': max_news,
            'get_detail': get_detail,
            'batch_size': args.batch_size,
            'max_workers': args.max_workers,
            'search_query': args.search_query
        }


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    try:
        # 解析简化参数
        parsed_config = parse_simple_args(args)

        # 加载自定义配置
        custom_config = {}
        if args.config:
            custom_config = load_custom_config(args.config)

        # 根据解析的参数更新配置
        if 'processor' not in custom_config:
            custom_config['processor'] = {}
        custom_config['processor']['batch_size'] = parsed_config['batch_size']
        custom_config['processor']['max_workers'] = parsed_config['max_workers']
        # 向量化处理器应该处理所有爬取到的新闻，不限制数量
        custom_config['processor']['max_news'] = None

        # 爬虫配置
        if 'crawler' not in custom_config:
            custom_config['crawler'] = {}
        custom_config['crawler']['max_news_per_source'] = parsed_config['max_news'] or 30
        custom_config['crawler']['get_detail'] = parsed_config['get_detail']
        if parsed_config['max_news'] is None:  # all模式
            custom_config['crawler']['get_all'] = True

        # 设置日志级别
        if args.verbose:
            if 'logging' not in custom_config:
                custom_config['logging'] = {}
            custom_config['logging']['level'] = 'DEBUG'
        elif args.quiet:
            if 'logging' not in custom_config:
                custom_config['logging'] = {}
            custom_config['logging']['level'] = 'ERROR'
            custom_config['logging']['console_enabled'] = False

        # 创建流水线实例
        pipeline = MasterPipeline(custom_config)

        # 仅显示统计信息
        if parsed_config['mode'] == 'stats_only':
            try:
                search_engine = NewsSearchEngine(SystemConfig.VECTOR_DB_PATH)
                search_engine.get_database_stats()
            except Exception as e:
                print(f"❌ 获取统计信息失败: {e}")
            return

        # 仅运行搜索演示
        if parsed_config['mode'] == 'search_only':
            pipeline.run_search_demo(parsed_config['search_query'])
            return

        # 运行完整流水线
        print("🚀 启动新闻处理主流水线")
        print(f"⚙️ 参数配置:")
        print(f"   📰 每个来源新闻数: {'全部' if parsed_config['max_news'] is None else parsed_config['max_news']}")
        print(f"   📝 获取详细内容: {'是' if parsed_config['get_detail'] else '否'}")
        print(f"   🔄 批处理大小: {parsed_config['batch_size']}")
        print(f"   🧵 最大线程数: {parsed_config['max_workers']}")
        print()

        # 执行流水线
        report = pipeline.run_full_pipeline(
            max_news_per_source=parsed_config['max_news'],
            get_detail=parsed_config['get_detail'],
            get_all=(parsed_config['max_news'] is None)
        )

        # 显示报告
        pipeline.print_final_report(report)

        # 保存报告
        if args.output_report:
            with open(args.output_report, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"📄 执行报告已保存: {args.output_report}")

        # 运行搜索演示
        if not args.quiet:
            print(f"\n🔍 运行搜索演示...")
            pipeline.run_search_demo(parsed_config.get('search_query', '人工智能'))

        print(f"\n🎉 流水线执行完成！")

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 流水线执行失败: {e}")
        if args.verbose:
            print(f"错误详情: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
