#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
6线程并发新闻爬虫快速演示
"""

import sys
import os
import time
from datetime import datetime

# 添加路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '澎湃新闻爬虫 - 副本'))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '环球爬虫 - 副本'))

def quick_demo():
    """快速演示6线程并发爬虫"""
    print("=" * 60)
    print("6线程并发新闻爬虫 - 快速演示")
    print("=" * 60)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        from unified_news_crawler import UnifiedNewsCrawler, THEPAPER_AVAILABLE, HUANQIU_AVAILABLE
        
        print("[状态检查]")
        print(f"  澎湃新闻爬虫: {'可用' if THEPAPER_AVAILABLE else '不可用'}")
        print(f"  环球新闻爬虫: {'可用' if HUANQIU_AVAILABLE else '不可用'}")
        
        available_count = 4  # 基础4个爬虫
        if THEPAPER_AVAILABLE:
            available_count += 1
        if HUANQIU_AVAILABLE:
            available_count += 1
            
        print(f"  可用线程数: {available_count}")
        print()
        
        print("[创建爬虫实例]")
        # 创建爬虫 - 每个来源2条新闻，不获取详细内容（演示用）
        crawler = UnifiedNewsCrawler(
            max_news_per_source=2,
            get_detail=False,  # 演示模式不获取详细内容，提高速度
            get_all=False
        )
        print("  爬虫实例创建成功")
        print()
        
        print("[准备信息]")
        print("  每个来源: 2条新闻")
        print("  获取详细内容: 否（演示模式）")
        print("  预计时间: 10-30秒")
        print()
        
        # 询问用户是否继续
        user_input = input("是否开始演示? (y/n): ").strip().lower()
        if user_input not in ['y', 'yes', '是']:
            print("演示已取消")
            return
        
        print()
        print("[开始爬取] - 请稍候...")
        start_time = time.time()
        
        # 执行爬取
        filename, total_count, all_news = crawler.crawl_all()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print()
        print("=" * 60)
        print("[演示结果]")
        print(f"  总耗时: {duration:.1f} 秒")
        print(f"  总新闻数: {total_count} 条")
        print(f"  平均速度: {total_count/duration:.1f} 条/秒")
        if filename:
            print(f"  输出文件: {filename}")
        print()
        
        print("[各源统计]")
        source_stats = {}
        for news in all_news:
            media = news.get('media', '未知')
            source_stats[media] = source_stats.get(media, 0) + 1
        
        for media, count in source_stats.items():
            print(f"  {media}: {count} 条")
        
        print()
        print("[示例新闻] (前3条)")
        for i, news in enumerate(all_news[:3], 1):
            print(f"  {i}. {news.get('title', '无标题')[:40]}...")
            print(f"     来源: {news.get('media', '未知')}")
            print(f"     时间: {news.get('formatted_time', '未知')}")
            print()
        
        if total_count > 3:
            print(f"  ... 还有 {total_count - 3} 条新闻")
        
        print("=" * 60)
        print("[演示完成]")
        print("6线程并发新闻爬虫运行正常！")
        print()
        print("[下一步操作]")
        print("1. 完整爬取: python unified_news_crawler.py 20 true")
        print("2. 向量处理: python master_pipeline.py")
        print("3. 新闻搜索: python news_search.py")
        print("=" * 60)
        
    except Exception as e:
        print(f"[错误] 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_demo()