#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成新闻系统测试脚本
验证各个模块的基本功能
"""

import sys
import os
import time
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config import print_config_status
from database_manager import DatabaseManager
from news_crawler import NewsCrawler
from topic_merger import TopicMerger
from topic_processor import TopicProcessor
from news_search import NewsSearchEngine
from article_generator import ArticleGenerator
from llm_client import LLMClient


class SystemTester:
    """系统测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_results = {}
        print("🧪 集成新闻系统测试器")
        print("=" * 50)
    
    def run_all_tests(self):
        """运行所有测试"""
        tests = [
            ("配置检查", self.test_config),
            ("数据库连接", self.test_database),
            ("LLM客户端", self.test_llm_client),
            ("新闻爬虫", self.test_news_crawler),
            ("话题合并", self.test_topic_merger),
            ("话题处理", self.test_topic_processor),
            ("新闻搜索", self.test_news_search),
            ("文章生成", self.test_article_generator)
        ]
        
        print(f"🚀 开始运行 {len(tests)} 个测试...")
        print("-" * 50)
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            print(f"\n🧪 测试: {test_name}")
            try:
                result = test_func()
                if result:
                    print(f"✅ {test_name}: 通过")
                    passed += 1
                else:
                    print(f"❌ {test_name}: 失败")
                    failed += 1
                self.test_results[test_name] = result
            except Exception as e:
                print(f"❌ {test_name}: 异常 - {e}")
                failed += 1
                self.test_results[test_name] = False
        
        print("\n" + "=" * 50)
        print(f"📊 测试结果: ✅ 通过: {passed} ❌ 失败: {failed}")
        
        if failed == 0:
            print("🎉 所有测试通过!")
            return True
        else:
            print("⚠️ 部分测试失败，请检查配置和环境")
            return False
    
    def test_config(self) -> bool:
        """测试配置"""
        try:
            return print_config_status()
        except Exception as e:
            print(f"配置测试失败: {e}")
            return False
    
    def test_database(self) -> bool:
        """测试数据库连接"""
        try:
            db_manager = DatabaseManager()
            success = db_manager.connect()
            if success:
                db_manager.disconnect()
            return success
        except Exception as e:
            print(f"数据库测试失败: {e}")
            return False
    
    def test_llm_client(self) -> bool:
        """测试LLM客户端"""
        try:
            client = LLMClient()
            
            # 简单测试
            response = client.chat("请回答：1+1等于几？", max_tokens=50)
            
            if response and len(response) > 0:
                print(f"   LLM响应: {response[:50]}...")
                return True
            else:
                print("   LLM响应为空")
                return False
                
        except Exception as e:
            print(f"LLM客户端测试失败: {e}")
            return False
    
    def test_news_crawler(self) -> bool:
        """测试新闻爬虫"""
        try:
            crawler = NewsCrawler()
            
            # 测试单个平台
            weibo_news = crawler.weibo_hot_search()
            
            if weibo_news and len(weibo_news) > 0:
                print(f"   爬取到 {len(weibo_news)} 条微博热搜")
                return True
            else:
                print("   未能爬取到新闻")
                return False
                
        except Exception as e:
            print(f"新闻爬虫测试失败: {e}")
            return False
    
    def test_topic_merger(self) -> bool:
        """测试话题合并"""
        try:
            merger = TopicMerger()
            
            # 测试数据
            test_topics = {
                'weibo': ['人工智能发展', 'AI技术突破'],
                'zhihu': ['人工智能前景', '机器学习应用']
            }
            
            merged = merger.merge_topics(test_topics, batch_size=10)
            
            if merged and len(merged) > 0:
                print(f"   合并得到 {len(merged)} 个话题")
                return True
            else:
                print("   话题合并失败")
                return False
                
        except Exception as e:
            print(f"话题合并测试失败: {e}")
            return False
    
    def test_topic_processor(self) -> bool:
        """测试话题处理"""
        try:
            processor = TopicProcessor()
            
            # 测试数据
            test_topic = {
                'merged_title': '人工智能技术发展',
                'category': '科技',
                'source_platforms': ['weibo'],
                'source_titles': ['AI技术突破'],
                'importance_score': 8
            }
            
            result = processor.analyze_topic(test_topic)
            
            if result and 'keywords' in result:
                keywords = result.get('keywords', [])
                print(f"   提取到 {len(keywords)} 个关键词")
                return True
            else:
                print("   话题处理失败")
                return False
                
        except Exception as e:
            print(f"话题处理测试失败: {e}")
            return False
    
    def test_news_search(self) -> bool:
        """测试新闻搜索"""
        try:
            search_engine = NewsSearchEngine()
            
            # 测试搜索
            test_keywords = ['人工智能', '技术']
            results = search_engine.search_by_keywords(test_keywords, top_k=3)
            
            print(f"   搜索到 {len(results)} 条相关新闻")
            return True  # 即使没有结果也算通过，因为可能向量数据库为空
            
        except Exception as e:
            print(f"新闻搜索测试失败: {e}")
            return False
    
    def test_article_generator(self) -> bool:
        """测试文章生成"""
        try:
            generator = ArticleGenerator()
            
            # 测试数据
            test_topic_analysis = {
                'original_topic': {
                    'merged_title': '人工智能技术发展',
                    'category': '科技',
                    'importance_score': 8
                },
                'keywords': ['人工智能', 'AI'],
                'background_context': '人工智能技术快速发展',
                'key_points': ['技术突破', '应用扩展'],
                'related_entities': {
                    'people': [],
                    'organizations': [],
                    'locations': [],
                    'others': []
                }
            }
            
            test_news_results = {
                'news_items': []
            }
            
            article = generator.generate_article(test_topic_analysis, test_news_results)
            
            if article and 'title' in article and 'content' in article:
                print(f"   生成文章: {article['title'][:30]}...")
                print(f"   文章字数: {article.get('word_count', 0)}")
                return True
            else:
                print("   文章生成失败")
                return False
                
        except Exception as e:
            print(f"文章生成测试失败: {e}")
            return False


def quick_test():
    """快速测试 - 只测试关键功能"""
    print("⚡ 快速测试模式")
    print("-" * 30)
    
    # 测试配置
    print("🔍 检查配置...")
    if not print_config_status():
        print("❌ 配置检查失败")
        return False
    
    # 测试LLM
    print("\n🤖 测试LLM...")
    try:
        client = LLMClient()
        response = client.chat("你好", max_tokens=20)
        if response:
            print(f"✅ LLM响应: {response[:30]}...")
        else:
            print("❌ LLM无响应")
            return False
    except Exception as e:
        print(f"❌ LLM测试失败: {e}")
        return False
    
    # 测试数据库
    print("\n🗄️ 测试数据库...")
    try:
        db = DatabaseManager()
        if db.connect():
            print("✅ 数据库连接成功")
            db.disconnect()
        else:
            print("❌ 数据库连接失败")
            return False
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False
    
    print("\n✅ 快速测试通过!")
    return True


def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        return quick_test()
    else:
        tester = SystemTester()
        return tester.run_all_tests()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
