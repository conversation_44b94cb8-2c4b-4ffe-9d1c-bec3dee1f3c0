#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
话题处理模块 - 原始版本备份
依次处理每个合并后的话题，为后续新闻检索和文章生成做准备
"""

import json
import re
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from llm_client import LLMClient


class TopicProcessor:
    """话题处理器"""
    
    def __init__(self):
        """初始化话题处理器"""
        self.llm_client = LLMClient()
    
    def analyze_topic(self, topic: Dict[str, Any], max_retries: int = 5) -> Dict[str, Any]:
        """
        分析单个话题，提取关键信息（增强版重试机制）

        Args:
            topic: 合并后的话题信息
            max_retries: 最大重试次数

        Returns:
            分析结果，包含：
            - original_topic: 原始话题信息
            - keywords: 关键词列表
            - search_queries: 搜索查询词列表
            - background_context: 背景信息
            - key_points: 关键要点
            - related_entities: 相关实体（人物、机构、地点等）
        """
        print(f"🔍 分析话题: {topic['merged_title']}")
        
        # 构建系统提示词
        system_prompt = """你是一个专业的新闻分析师。你的任务是深入分析给定的热门话题，提取关键信息以便后续进行新闻检索和文章生成。

请按照以下要求分析话题：

1. **关键词提取**：
   - 提取3-8个最重要的关键词
   - 包括人物、事件、地点、机构等核心要素
   - 关键词要准确、具体，便于搜索

2. **搜索查询词**：
   - 生成5-10个不同角度的搜索查询词
   - 包括不同的表述方式和相关概念
   - 有助于全面检索相关新闻

3. **背景信息**：
   - 简要说明话题的背景和起因
   - 解释为什么这个话题会成为热点
   - 提供必要的上下文信息

4. **关键要点**：
   - 列出话题的3-6个关键要点
   - 包括主要事实、争议点、影响等
   - 要点要简洁明确

5. **相关实体**：
   - 识别相关的人物、机构、地点、产品等
   - 按类型分类整理
   - 有助于扩展搜索范围

请严格按照以下JSON格式输出：

```json
{
  "keywords": ["关键词1", "关键词2", "关键词3"],
  "search_queries": ["搜索词1", "搜索词2", "搜索词3"],
  "background_context": "话题背景信息",
  "key_points": ["要点1", "要点2", "要点3"],
  "related_entities": {
    "people": ["人物1", "人物2"],
    "organizations": ["机构1", "机构2"],
    "locations": ["地点1", "地点2"],
    "others": ["其他实体1", "其他实体2"]
  }
}
```"""

        # 构建用户提示词
        user_prompt = f"""请分析以下热门话题：

**话题标题**: {topic['merged_title']}
**话题分类**: {topic['category']}
**重要性评分**: {topic['importance_score']}/10
**来源平台**: {', '.join(topic['source_platforms'])}

**相关原始标题**:
{chr(10).join(f"- {title}" for title in topic['source_titles'])}

请深入分析这个话题，提取关键信息用于后续的新闻检索和文章生成。"""

        # 使用重试机制进行分析
        return self._analyze_with_retry(topic, user_prompt, system_prompt, max_retries)

    def _analyze_with_retry(self, topic: Dict[str, Any], user_prompt: str, system_prompt: str, max_retries: int) -> Dict[str, Any]:
        """带重试机制的话题分析"""

        for retry_round in range(max_retries):
            try:
                print(f"🔄 第 {retry_round + 1} 次尝试分析话题...")

                # 调用LLM
                response = self.llm_client.chat(
                    prompt=user_prompt,
                    system_prompt=system_prompt,
                    max_tokens=16000,
                    temperature=0.3
                )

                # 解析响应
                analysis = self._parse_analysis_response(response)

                if analysis:
                    # 组合完整结果
                    result = {
                        'original_topic': topic,
                        'analysis_time': datetime.now().isoformat(),
                        **analysis
                    }

                    print(f"✅ 话题分析完成: {len(analysis.get('keywords', []))} 个关键词")
                    return result
                else:
                    # 解析失败，重新调用API
                    print(f"⚠️ 第 {retry_round + 1} 次解析失败")
                    print(f"📝 原始响应: {response[:500]}...")
                    if retry_round < max_retries - 1:
                        print(f"🔄 准备重新调用API...")
                    # 继续到下一次循环，重新调用API

            except Exception as e:
                print(f"❌ 第 {retry_round + 1} 次分析失败: {e}")

            # 如果还有重试机会，等待一下
            if retry_round < max_retries - 1:
                import time
                wait_time = 2 ** retry_round
                print(f"⏳ 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)

        print(f"❌ 所有重试都失败，使用备用分析方案")
        return self._fallback_analysis(topic)
    
    def _parse_analysis_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析LLM的分析响应"""
        try:
            response = response.strip()

            # 多种JSON提取和修复方式
            json_str = None

            # 方式1：提取```json```包装的内容
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1).strip()

            # 方式2：提取```包装的内容
            elif '```' in response:
                json_match = re.search(r'```\s*(.*?)\s*```', response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1).strip()

            # 方式3：查找JSON对象开始和结束
            elif '{' in response and '}' in response:
                start = response.find('{')
                end = response.rfind('}') + 1
                json_str = response[start:end].strip()

            # 方式4：直接尝试整个响应
            else:
                json_str = response

            if not json_str:
                print(f"⚠️ 无法提取JSON，原始响应: {response[:200]}...")
                return None

            # JSON修复尝试
            json_str = self._fix_json_format(json_str)

            # 解析JSON
            result = json.loads(json_str)

            # 验证必要字段
            required_fields = ['keywords', 'search_queries', 'background_context', 'key_points', 'related_entities']
            if all(field in result for field in required_fields):
                return result
            else:
                print(f"⚠️ 缺少必要字段，当前字段: {list(result.keys())}")
                return None

        except Exception as e:
            print(f"⚠️ 解析分析响应失败: {e}")
            print(f"📝 原始响应: {response[:500]}...")

        return None

    def _fix_json_format(self, json_str: str) -> str:
        """修复常见的JSON格式问题"""
        try:
            # 移除可能的前后缀文字
            json_str = json_str.strip()

            # 确保以{开头，}结尾
            if not json_str.startswith('{'):
                start_idx = json_str.find('{')
                if start_idx != -1:
                    json_str = json_str[start_idx:]

            if not json_str.endswith('}'):
                end_idx = json_str.rfind('}')
                if end_idx != -1:
                    json_str = json_str[:end_idx + 1]
                else:
                    # 如果没有结束}，尝试修复
                    json_str = json_str.rstrip(',') + '}'

            # 修复常见的引号问题
            json_str = json_str.replace("'", '"')  # 单引号改双引号

            # 修复可能的换行问题和多余空格
            json_str = re.sub(r'\n\s*', ' ', json_str)
            json_str = re.sub(r'\s+', ' ', json_str)

            # 修复多余的逗号（对象最后一个元素后的逗号）
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)

            # 修复对象内部多余的逗号
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

            # 如果JSON被截断，尝试修复最后一个不完整的对象
            if json_str.count('{') > json_str.count('}'):
                # 找到最后一个完整的字段
                last_complete = json_str.rfind('",')
                if last_complete != -1:
                    json_str = json_str[:last_complete + 1] + '}'

            return json_str
        except Exception as e:
            print(f"⚠️ JSON修复失败: {e}")
            return json_str
    
    def _fallback_analysis(self, topic: Dict[str, Any]) -> Dict[str, Any]:
        """备用分析方案"""
        print("🔄 执行备用分析方案...")
        
        # 从标题中提取简单关键词
        title = topic['merged_title']
        keywords = []
        
        # 简单的关键词提取（基于常见词汇）
        import re
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', title)
        keywords = [word for word in words if len(word) > 1][:5]
        
        return {
            'original_topic': topic,
            'analysis_time': datetime.now().isoformat(),
            'keywords': keywords,
            'search_queries': [title] + keywords,
            'background_context': f"关于'{title}'的热门话题",
            'key_points': [f"话题: {title}", f"分类: {topic['category']}"],
            'related_entities': {
                'people': [],
                'organizations': [],
                'locations': [],
                'others': []
            }
        }
    
    def process_all_topics(self, merged_topics: List[Dict[str, Any]],
                          use_concurrent: bool = True) -> List[Dict[str, Any]]:
        """
        处理所有合并后的话题

        Args:
            merged_topics: 合并后的话题列表
            use_concurrent: 是否使用并发处理

        Returns:
            处理结果列表
        """
        print(f"🚀 开始处理 {len(merged_topics)} 个话题...")
        print("=" * 60)

        if use_concurrent and len(merged_topics) > 1:
            # 使用并发处理
            return self._process_topics_concurrent(merged_topics)
        else:
            # 使用串行处理
            return self._process_topics_sequential(merged_topics)

    def _process_topics_concurrent(self, merged_topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """并发处理话题"""
        print(f"⚡ 使用并发模式处理话题...")

        # 准备并发请求
        requests = []
        for i, topic in enumerate(merged_topics):
            # 构建系统提示词
            system_prompt = """你是一个专业的新闻分析师。请分析给定的热门话题，提取关键信息。

请严格按照以下JSON格式输出：

```json
{
  "keywords": ["关键词1", "关键词2", "关键词3"],
  "search_queries": ["搜索词1", "搜索词2", "搜索词3"],
  "background_context": "话题背景信息",
  "key_points": ["要点1", "要点2", "要点3"],
  "related_entities": {
    "people": ["人物1", "人物2"],
    "organizations": ["机构1", "机构2"],
    "locations": ["地点1", "地点2"],
    "others": ["其他实体1", "其他实体2"]
  }
}
```"""

            # 构建用户提示词
            user_prompt = f"""请分析以下热门话题：

**话题标题**: {topic['merged_title']}
**话题分类**: {topic['category']}
**重要性评分**: {topic['importance_score']}/10
**来源平台**: {', '.join(topic['source_platforms'])}

**相关原始标题**:
{chr(10).join(f"- {title}" for title in topic['source_titles'])}

请深入分析这个话题，提取关键信息用于后续的新闻检索和文章生成。"""

            requests.append({
                'prompt': user_prompt,
                'system_prompt': system_prompt,
                'max_tokens': 16000,
                'temperature': 0.3,
                'topic_index': i,
                'topic': topic
            })

        # 并发处理
        results = self.llm_client.batch_chat(requests)

        # 处理结果，收集解析失败的话题进行重试
        processed_topics = []
        failed_topics = []

        for result in results:
            if result['success']:
                # 解析LLM响应
                analysis = self._parse_analysis_response(result['response'])
                if analysis:
                    # 组合完整结果
                    topic = requests[result['request_index']]['topic']
                    processed_result = {
                        'original_topic': topic,
                        'analysis_time': datetime.now().isoformat(),
                        **analysis
                    }
                    processed_topics.append(processed_result)
                    print(f"✅ 话题 {result['request_index'] + 1} 分析完成: {len(analysis.get('keywords', []))} 个关键词")
                else:
                    # 解析失败，加入重试队列
                    topic = requests[result['request_index']]['topic']
                    failed_topics.append(topic)
                    print(f"⚠️ 话题 {result['request_index'] + 1} 解析失败，加入重试队列")
                    print(f"📝 原始响应: {result['response'][:200]}...")
            else:
                # 请求失败，加入重试队列
                topic = requests[result['request_index']]['topic']
                failed_topics.append(topic)
                print(f"❌ 话题 {result['request_index'] + 1} API调用失败，加入重试队列")

        # 对解析失败的话题进行单独重试
        if failed_topics:
            print(f"🔄 开始重试 {len(failed_topics)} 个失败话题...")
            for topic in failed_topics:
                retry_result = self.analyze_topic(topic, max_retries=3)  # 使用单独重试
                processed_topics.append(retry_result)

        return processed_topics

    def _process_topics_sequential(self, merged_topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """串行处理话题"""
        print(f"🔄 使用串行模式处理话题...")

        processed_topics = []

        for i, topic in enumerate(merged_topics, 1):
            print(f"\n📋 处理进度: {i}/{len(merged_topics)}")

            try:
                # 分析话题
                analysis_result = self.analyze_topic(topic)
                processed_topics.append(analysis_result)

                # 显示简要结果
                keywords = analysis_result.get('keywords', [])
                print(f"🔑 关键词: {', '.join(keywords[:3])}{'...' if len(keywords) > 3 else ''}")

                # 请求间隔
                if i < len(merged_topics):
                    print(f"⏳ 等待 2 秒...")
                    time.sleep(2)

            except Exception as e:
                print(f"❌ 处理话题失败: {e}")
                # 添加错误记录
                processed_topics.append({
                    'original_topic': topic,
                    'error': str(e),
                    'analysis_time': datetime.now().isoformat()
                })

        print("\n" + "=" * 60)
        print(f"✅ 话题处理完成! 成功处理 {len(processed_topics)} 个话题")

        return processed_topics


if __name__ == "__main__":
    # 测试话题处理器
    test_topic = {
        'merged_title': '人工智能技术发展',
        'category': '科技',
        'source_platforms': ['weibo', 'zhihu'],
        'source_titles': ['AI技术突破', '人工智能前景'],
        'importance_score': 8
    }
    
    processor = TopicProcessor()
    result = processor.analyze_topic(test_topic)
    
    print(f"\n分析结果：")
    print(f"关键词: {result.get('keywords', [])}")
    print(f"搜索词: {result.get('search_queries', [])}")
    print(f"背景: {result.get('background_context', '')}")
    print(f"要点: {result.get('key_points', [])}")