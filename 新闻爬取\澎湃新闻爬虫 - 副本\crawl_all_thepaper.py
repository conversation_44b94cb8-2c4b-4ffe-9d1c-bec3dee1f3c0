#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
澎湃新闻全量爬取脚本
获取所有板块的今日新闻，包含详细内容
"""

from thepaper_master_crawler import get_news
import json
import csv
from datetime import datetime

def save_to_json(news_data, filename=None):
    """保存为JSON格式"""
    if not filename:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"thepaper_all_news_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(news_data, f, ensure_ascii=False, indent=2)
    
    print(f"JSON数据已保存到: {filename}")
    return filename

def save_to_csv(news_data, filename=None):
    """保存为CSV格式"""
    if not filename:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"thepaper_all_news_{timestamp}.csv"
    
    if not news_data:
        print("没有数据可保存")
        return
    
    # 标准字段
    fieldnames = ['title', 'url', 'content', 'category', 'publish_time', 'source']
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for news in news_data:
            # 确保所有字段都存在
            row = {field: news.get(field, '') for field in fieldnames}
            writer.writerow(row)
    
    print(f"CSV数据已保存到: {filename}")
    return filename

def print_statistics(news_data):
    """打印统计信息"""
    if not news_data:
        print("没有数据可统计")
        return
    
    print(f"\n=== 爬取统计 ===")
    print(f"总新闻数量: {len(news_data)}")
    
    # 按板块统计
    category_stats = {}
    content_stats = {'有内容': 0, '无内容': 0}
    
    for news in news_data:
        category = news.get('category', '未知')
        category_stats[category] = category_stats.get(category, 0) + 1
        
        if news.get('content') and len(news['content']) > 50:
            content_stats['有内容'] += 1
        else:
            content_stats['无内容'] += 1
    
    print(f"\n各板块新闻数量:")
    for category, count in sorted(category_stats.items()):
        print(f"  {category}: {count} 条")
    
    print(f"\n内容获取情况:")
    for status, count in content_stats.items():
        print(f"  {status}: {count} 条")
    
    # 时间分布
    time_stats = {}
    for news in news_data:
        pub_time = news.get('publish_time', '')
        if pub_time:
            hour = pub_time.split(' ')[1][:2] if ' ' in pub_time else pub_time[:2]
            time_stats[hour] = time_stats.get(hour, 0) + 1
    
    if time_stats:
        print(f"\n发布时间分布（按小时）:")
        for hour in sorted(time_stats.keys()):
            print(f"  {hour}时: {time_stats[hour]} 条")

def print_sample_news(news_data, count=5):
    """打印样本新闻"""
    if not news_data:
        return
    
    print(f"\n=== 前{count}条新闻预览 ===")
    for i, news in enumerate(news_data[:count]):
        print(f"\n第{i+1}条:")
        print(f"标题: {news.get('title', 'N/A')}")
        print(f"分类: {news.get('category', 'N/A')}")
        print(f"时间: {news.get('publish_time', 'N/A')}")
        print(f"链接: {news.get('url', 'N/A')}")
        
        content = news.get('content', '')
        if content:
            print(f"内容: {content[:100]}...")
            print(f"内容长度: {len(content)} 字符")
        else:
            print("内容: 无")

def main():
    """主函数"""
    print("澎湃新闻全量爬取器")
    print("=" * 50)
    print("功能: 获取所有板块的今日新闻，包含详细内容")
    print("板块: 要闻、时事、国际、财经、深度")
    print("=" * 50)
    
    try:
        print("\n开始全量爬取...")
        print("注意: 这可能需要较长时间，请耐心等待")
        print("提示: 按 Ctrl+C 可以中断爬取")
        
        # 开始爬取
        start_time = datetime.now()
        news_data = get_news(max_news=None, get_detail=True)
        end_time = datetime.now()
        
        duration = end_time - start_time
        print(f"\n爬取完成! 耗时: {duration}")
        
        if news_data:
            # 打印统计信息
            print_statistics(news_data)
            
            # 打印样本新闻
            print_sample_news(news_data)
            
            # 保存数据
            print(f"\n=== 保存数据 ===")
            json_file = save_to_json(news_data)
            csv_file = save_to_csv(news_data)
            
            print(f"\n=== 完成 ===")
            print(f"JSON文件: {json_file}")
            print(f"CSV文件: {csv_file}")
            print(f"总新闻数: {len(news_data)} 条")
            print(f"爬取时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
        else:
            print("未获取到任何新闻数据")
            
    except KeyboardInterrupt:
        print("\n\n用户中断爬取")
        print("已获取的数据将尝试保存...")
        
        # 尝试保存已获取的数据
        try:
            if 'news_data' in locals() and news_data:
                save_to_json(news_data, "thepaper_interrupted.json")
                save_to_csv(news_data, "thepaper_interrupted.csv")
                print("中断前的数据已保存")
        except:
            pass
            
    except Exception as e:
        print(f"\n程序异常: {e}")
        import traceback
        traceback.print_exc()
        
        # 尝试保存已获取的数据
        try:
            if 'news_data' in locals() and news_data:
                save_to_json(news_data, "thepaper_error.json")
                print("异常前的数据已保存到 thepaper_error.json")
        except:
            pass

if __name__ == "__main__":
    main()
