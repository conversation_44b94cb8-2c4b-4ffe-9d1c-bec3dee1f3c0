#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
话题合并模块
使用LLM将各平台的热门话题进行智能合并和去重
"""

import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from llm_client import LLMClient


class TopicMerger:
    """话题合并器"""
    
    def __init__(self):
        """初始化话题合并器"""
        self.llm_client = LLMClient()
        self.platform_names = {
            'weibo': '微博热搜',
            'baidu': '百度热搜', 
            'zhihu': '知乎热榜',
            'toutiao': '今日头条',
            'bilibili': 'B站热搜',
            'github': 'GitHub趋势',
            'v2ex': 'V2EX热门',
            'ithome': 'IT之家'
        }
        
    def _prepare_topics_text(self, topics_data: Dict[str, List[str]]) -> str:
        """准备话题文本用于LLM处理"""
        text_parts = []
        total_count = 0
        
        for platform, titles in topics_data.items():
            if not titles:
                continue
                
            platform_name = self.platform_names.get(platform, platform.upper())
            text_parts.append(f"\n【{platform_name}】({len(titles)}条):")
            
            for i, title in enumerate(titles, 1):
                text_parts.append(f"{i:2d}. {title}")
            
            total_count += len(titles)
        
        header = f"以下是从{len(topics_data)}个平台收集到的{total_count}条热门话题："
        return header + "\n" + "\n".join(text_parts)
    
    def _prepare_batch_text(self, batch: List[Dict[str, str]]) -> str:
        """准备批次文本"""
        text_parts = []
        for i, item in enumerate(batch, 1):
            platform_name = self.platform_names.get(item['platform'], item['platform'].upper())
            text_parts.append(f"{i:2d}. {item['title']} ({platform_name})")
        return "\n".join(text_parts)
    
    def _parse_llm_response(self, response: str) -> List[Dict[str, Any]]:
        """解析LLM的JSON响应（超强化版）"""
        if not response or not response.strip():
            print("⚠️ 响应为空")
            return []

        try:
            response = response.strip()

            # 多种JSON提取和修复方式
            json_str = None

            # 方式1：提取```json```包装的内容
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1).strip()

            # 方式2：提取```包装的内容
            elif '```' in response:
                json_match = re.search(r'```\s*(.*?)\s*```', response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1).strip()

            # 方式3：查找JSON数组开始和结束
            elif '[' in response and ']' in response:
                start = response.find('[')
                end = response.rfind(']') + 1
                json_str = response[start:end].strip()

            # 方式4：直接尝试整个响应
            else:
                json_str = response

            if not json_str:
                print(f"⚠️ 无法提取JSON，原始响应: {response[:200]}...")
                return []

            # JSON修复尝试
            json_str = self._fix_json_format(json_str)

            # 解析JSON
            result = json.loads(json_str)

            # 验证和转换格式（适应简化JSON）
            if isinstance(result, list):
                validated_result = []
                for item in result:
                    if isinstance(item, dict):
                        # 适应简化的JSON格式
                        validated_item = {
                            'merged_title': item.get('title', item.get('merged_title', '未知话题')),
                            'category': item.get('category', '其他'),
                            'source_titles': item.get('sources', item.get('source_titles', [])),
                            'source_platforms': ['mixed'],  # 简化平台信息
                            'importance_score': item.get('score', item.get('importance_score', 5))
                        }
                        validated_result.append(validated_item)

                return validated_result

        except Exception as e:
            print(f"⚠️ 解析LLM响应失败: {e}")
            print(f"📝 原始响应: {response[:500]}...")

        return []

    def _fix_json_format(self, json_str: str) -> str:
        """修复常见的JSON格式问题"""
        try:
            # 移除可能的前后缀文字
            json_str = json_str.strip()

            # 确保以[开头，]结尾
            if not json_str.startswith('['):
                start_idx = json_str.find('[')
                if start_idx != -1:
                    json_str = json_str[start_idx:]

            if not json_str.endswith(']'):
                end_idx = json_str.rfind(']')
                if end_idx != -1:
                    json_str = json_str[:end_idx + 1]
                else:
                    # 如果没有结束]，尝试修复
                    json_str = json_str.rstrip(',') + ']'

            # 修复常见的引号问题
            json_str = json_str.replace("'", '"')  # 单引号改双引号

            # 修复可能的换行问题和多余空格
            json_str = re.sub(r'\n\s*', ' ', json_str)
            json_str = re.sub(r'\s+', ' ', json_str)

            # 修复多余的逗号（数组最后一个元素后的逗号）
            json_str = re.sub(r',\s*]', ']', json_str)
            json_str = re.sub(r',\s*}', '}', json_str)

            # 修复对象内部多余的逗号
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

            # 如果JSON被截断，尝试修复最后一个不完整的对象
            if json_str.count('{') > json_str.count('}'):
                # 找到最后一个完整的对象
                last_complete = json_str.rfind('},')
                if last_complete != -1:
                    json_str = json_str[:last_complete + 1] + ']'
                else:
                    # 如果没有完整对象，找到最后一个{并截断
                    last_brace = json_str.rfind('{')
                    if last_brace > 0:
                        # 找到这个{之前的最后一个完整对象
                        before_last = json_str[:last_brace].rstrip(',')
                        json_str = before_last + ']'

            return json_str
        except Exception as e:
            print(f"⚠️ JSON修复失败: {e}")
            return json_str
    
    def merge_topics(self, topics_data: Dict[str, List[str]], batch_size: int = 100, max_retries: int = 5) -> List[Dict[str, Any]]:
        """
        分批并发合并相似话题（增强版重试机制）

        Args:
            topics_data: 各平台的话题数据 {platform: [titles]}
            batch_size: 每批处理的话题数量
            max_retries: 每批最大重试次数

        Returns:
            合并后的话题列表
        """
        print("🔄 开始分批并发合并相似话题...")

        # 将所有话题平铺成列表，并记录原始信息
        all_topics = []
        original_count_by_platform = {}

        for platform, titles in topics_data.items():
            original_count_by_platform[platform] = len(titles)
            for title in titles:
                all_topics.append({
                    'title': title,
                    'platform': platform
                })

        total_topics = len(all_topics)
        total_batches = (total_topics + batch_size - 1) // batch_size
        print(f"📊 总共 {total_topics} 个话题，将分 {total_batches} 批处理（每批最多 {batch_size} 个）")

        # 分批处理
        batches = []
        for i in range(0, total_topics, batch_size):
            batch = all_topics[i:i + batch_size]
            batches.append(batch)

        # 构建系统提示词
        system_prompt = """你是新闻话题分析师。

任务：合并相似话题
分类：时政、娱乐、科技、体育、社会、国际、财经、其他

**重要：只输出JSON数组，不要任何其他文字！**

格式：
[
{"title":"合并标题","category":"分类","sources":["原标题1","原标题2"],"score":8}
]

要求：
- 开头直接是[
- 不要```包装
- 用双引号
- score是1-10数字
- 最后元素后无逗号

直接输出："""

        # 准备并发请求
        requests = []
        for i, batch in enumerate(batches):
            batch_text = self._prepare_batch_text(batch)
            user_prompt = f"""合并{len(batch)}个话题：

{batch_text}

输出JSON："""

            requests.append({
                'prompt': user_prompt,
                'system_prompt': system_prompt,
                'max_tokens': 16000,
                'temperature': 0.3,
                'batch_index': i
            })

        try:
            # 增强重试机制处理
            return self._process_batches_with_retry(requests, batches, total_topics, max_retries, topics_data)
        except Exception as e:
            print(f"❌ 分批合并失败: {e}")
            print("🔄 使用备用合并方案...")
            return self._fallback_merge(topics_data)

    def _process_batches_with_retry(self, requests: List[Dict], batches: List[List], total_topics: int,
                                   max_retries: int, topics_data: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """
        增强重试机制处理批次
        """
        all_merged_topics = []
        failed_batches = []

        for retry_round in range(max_retries):
            if retry_round == 0:
                print(f"🚀 第 {retry_round + 1} 轮：并发处理 {len(requests)} 批话题...")
                current_requests = requests
                current_batches = batches
            else:
                print(f"🔄 第 {retry_round + 1} 轮：重试 {len(failed_batches)} 个失败批次...")
                current_requests = []
                current_batches = []
                for batch_info in failed_batches:
                    current_requests.append(batch_info['request'])
                    current_batches.append(batch_info['batch'])
                failed_batches = []

            if not current_requests:
                break

            # 并发处理当前批次
            results = self.llm_client.batch_chat(current_requests)

            # 处理结果
            successful_batches = 0
            for i, result in enumerate(results):
                batch_index = result.get('request_index', i)
                batch_data = current_batches[i] if i < len(current_batches) else []

                if result.get('success', False) and result.get('error') is None:
                    response = result.get('response', '')
                    print(f"🔍 批次 {batch_index} 原始回复: {response[:200]}...")

                    batch_topics = self._parse_llm_response(response)
                    if batch_topics:
                        all_merged_topics.extend(batch_topics)
                        successful_batches += 1
                        print(f"✅ 批次 {batch_index} 解析成功，得到 {len(batch_topics)} 个话题")

                        # 验证话题数量合理性
                        if len(batch_topics) > len(batch_data) * 2:
                            print(f"⚠️ 批次 {batch_index} 话题数量异常：输入 {len(batch_data)} 个，输出 {len(batch_topics)} 个")
                    else:
                        print(f"⚠️ 批次 {batch_index} 解析失败，加入重试队列")
                        failed_batches.append({
                            'request': current_requests[i],
                            'batch': batch_data,
                            'index': batch_index
                        })
                else:
                    print(f"❌ 批次 {batch_index} 处理失败: {result.get('error', 'Unknown error')}，加入重试队列")
                    failed_batches.append({
                        'request': current_requests[i],
                        'batch': batch_data,
                        'index': batch_index
                    })

            print(f"📊 第 {retry_round + 1} 轮结果：成功 {successful_batches}/{len(current_requests)} 批次")

            # 如果所有批次都成功，退出重试循环
            if not failed_batches:
                break

            # 如果还有重试机会，等待一下
            if retry_round < max_retries - 1 and failed_batches:
                import time
                wait_time = 2 ** retry_round
                print(f"⏳ 等待 {wait_time} 秒后进行下一轮重试...")
                time.sleep(wait_time)

        # 最终统计
        total_processed_topics = sum(len(batch['batch']) for batch in failed_batches) if failed_batches else 0
        total_successful_topics = total_topics - total_processed_topics

        print(f"📊 最终统计：")
        print(f"   原始话题: {total_topics} 个")
        print(f"   成功处理: {total_successful_topics} 个")
        print(f"   合并结果: {len(all_merged_topics)} 个")
        print(f"   压缩比例: {len(all_merged_topics)/total_topics*100:.1f}%")

        if failed_batches:
            print(f"⚠️ 仍有 {len(failed_batches)} 个批次处理失败，将使用备用方案处理")
            # 对失败的批次使用备用方案
            fallback_topics = self._process_failed_batches_fallback(failed_batches)
            all_merged_topics.extend(fallback_topics)

        return all_merged_topics

    def _process_failed_batches_fallback(self, failed_batches: List[Dict]) -> List[Dict[str, Any]]:
        """处理失败批次的备用方案"""
        fallback_topics = []

        for batch_info in failed_batches:
            batch_data = batch_info['batch']
            print(f"🔄 备用处理批次 {batch_info['index']}，包含 {len(batch_data)} 个话题")

            # 简单的基于关键词的合并
            for topic in batch_data:
                fallback_topics.append({
                    'merged_title': topic['title'],
                    'category': '其他',
                    'source_titles': [topic['title']],
                    'source_platforms': [topic['platform']],
                    'importance_score': 5
                })

        return fallback_topics

    def _fallback_merge(self, topics_data: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """备用合并方案"""
        print("🔄 执行备用合并方案...")
        
        fallback_topics = []
        topic_id = 1
        
        for platform, titles in topics_data.items():
            platform_name = self.platform_names.get(platform, platform.upper())
            
            for title in titles:
                fallback_topics.append({
                    'merged_title': title,
                    'category': '其他',
                    'source_titles': [title],
                    'source_platforms': [platform],
                    'importance_score': 5
                })
                topic_id += 1
        
        print(f"✅ 备用方案完成，生成 {len(fallback_topics)} 个话题")
        return fallback_topics
    
    def second_merge(self, merged_topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """二次合并 - 按分类进行深度合并"""
        print("🔄 开始二次智能合并...")
        
        # 按分类分组
        categories = {}
        for topic in merged_topics:
            category = topic.get('category', '其他')
            if category not in categories:
                categories[category] = []
            categories[category].append(topic)
        
        # 统计分类
        print("📊 分类统计:", end=" ")
        for category, topics in categories.items():
            print(f"{category}: {len(topics)}个", end=" ")
        print()
        
        # 准备二次合并的API请求
        requests = []
        category_mapping = {}
        all_second_merged = []

        for category, topics in categories.items():
            if len(topics) <= 4:  # 提高阈值，减少过度合并
                all_second_merged.extend(topics)
                print(f"📋 {category} 话题数量适中，直接保留 {len(topics)} 个")
            elif len(topics) <= 8:  # 中等数量，谨慎合并
                print(f"🔄 对 {category} 分类的 {len(topics)} 个话题进行谨慎合并...")
                # 只对明显相关的话题进行合并，大部分保留
                merged_in_category = self._conservative_merge(topics, category)
                all_second_merged.extend(merged_in_category)
            else:
                # 进行二次合并 - 调用API
                print(f"🔄 对 {category} 分类的 {len(topics)} 个话题进行二次合并...")

                # 构建API请求
                system_prompt = f"""你是一个专业的新闻话题分析师。请对以下{category}分类的话题进行智能合并。

⚠️ 重要合并原则：
1. **严格区分不同事件**：只有真正相关的同一事件或同一主题才能合并
2. **避免过度合并**：不同的人物、不同的地点、不同的时间的事件绝对不能合并
3. **保持话题独立性**：即使同属一个分类，不相关的独立事件必须分开
4. **合并条件严格**：只有以下情况才能合并：
   - 同一个人的不同新闻
   - 同一个事件的不同报道角度
   - 同一个政策的不同方面
   - 同一个公司/机构的相关新闻

❌ 禁止合并的情况：
- 不同国家的独立事件（如"特朗普关税"和"中国游客被抢"）
- 不同人物的独立事件（如"王勇受贿"和"李某贪污"）
- 不同公司的独立事件（如"苹果财报"和"特斯拉召回"）
- 不同地区的独立事件（如"北京暴雨"和"上海台风"）

合并策略：
- 宁可保留更多独立话题，也不要错误合并不相关事件
- 合并后的话题数量可以是原来的70%-90%
- 每个话题必须有明确的主题焦点

输出格式：JSON数组，每个话题包含：
- title: 合并后的标题（必须准确反映具体事件）
- category: 分类
- sources: 合并的原始标题列表
- score: 重要性评分(1-10)

只输出JSON，不要其他内容。"""

                topics_text = ""
                for i, topic in enumerate(topics, 1):
                    sources_text = '", "'.join(topic.get('source_titles', []))
                    topics_text += f'{i}. 标题: "{topic.get("merged_title", "")}" 来源: ["{sources_text}"] 评分: {topic.get("importance_score", 5)}\n'

                user_prompt = f"""请对以下{len(topics)}个{category}话题进行智能合并：

{topics_text}

请合并成更少但更有代表性的话题："""

                requests.append({
                    'prompt': user_prompt,
                    'system_prompt': system_prompt,
                    'max_retries': 5
                })
                category_mapping[len(requests) - 1] = {
                    'category': category,
                    'batch_id': category,
                    'original_topics': topics,
                    'skip': False
                }

        # 并发处理需要合并的分类
        if requests:
            print(f"🚀 开始并发处理 {len(requests)} 个分类批次...")

            # 使用LLM并发处理
            results = self.llm_client.batch_chat(requests)

            # 处理结果
            processed_count = 0
            for i, result in enumerate(results):
                if i not in category_mapping:
                    continue

                info = category_mapping[i]
                original_topics = info['original_topics']

                if result.get('success', False):
                    # 解析成功
                    merged_result = self._parse_llm_response(result['response'])
                    if merged_result:
                        all_second_merged.extend(merged_result)
                        print(f"✅ {info['batch_id']} 合并：{len(original_topics)} → {len(merged_result)} 个话题")
                        processed_count += len(original_topics)
                    else:
                        # 解析失败，保留原话题
                        all_second_merged.extend(original_topics)
                        print(f"⚠️ {info['batch_id']} 解析失败，保留原 {len(original_topics)} 个话题")
                        processed_count += len(original_topics)
                else:
                    # 请求失败，保留原话题
                    all_second_merged.extend(original_topics)
                    print(f"❌ {info['batch_id']} 请求失败，保留原 {len(original_topics)} 个话题")
                    processed_count += len(original_topics)

        # 按重要性排序
        all_second_merged.sort(key=lambda x: x.get('importance_score', x.get('score', 0)), reverse=True)

        print(f"✅ 二次合并完成：{len(merged_topics)} → {len(all_second_merged)} 个话题")
        return all_second_merged

    def _conservative_merge(self, topics: List[Dict[str, Any]], category: str) -> List[Dict[str, Any]]:
        """保守合并策略：只合并明显相关的话题"""
        print(f"   🔍 使用保守合并策略处理 {len(topics)} 个话题...")

        # 简单的相似度检查，只合并标题高度相似的话题
        merged_groups = []
        used_indices = set()

        for i, topic1 in enumerate(topics):
            if i in used_indices:
                continue

            group = [topic1]
            used_indices.add(i)

            title1 = topic1.get('merged_title', '').lower()

            # 查找相似话题
            for j, topic2 in enumerate(topics[i+1:], i+1):
                if j in used_indices:
                    continue

                title2 = topic2.get('merged_title', '').lower()

                # 简单的相似度检查：共同关键词比例
                words1 = set(title1.split())
                words2 = set(title2.split())

                if len(words1) > 0 and len(words2) > 0:
                    common_words = words1.intersection(words2)
                    similarity = len(common_words) / min(len(words1), len(words2))

                    # 只有高度相似才合并（阈值0.6）
                    if similarity >= 0.6:
                        group.append(topic2)
                        used_indices.add(j)

            merged_groups.append(group)

        # 生成合并结果
        result = []
        for group in merged_groups:
            if len(group) == 1:
                # 单个话题直接保留
                result.append(group[0])
            else:
                # 多个话题合并
                merged_topic = {
                    'merged_title': group[0]['merged_title'],  # 使用第一个作为主标题
                    'category': category,
                    'source_titles': [],
                    'source_platforms': [],
                    'importance_score': max(t.get('importance_score', 5) for t in group)
                }

                for topic in group:
                    merged_topic['source_titles'].extend(topic.get('source_titles', []))
                    merged_topic['source_platforms'].extend(topic.get('source_platforms', []))

                # 去重
                merged_topic['source_titles'] = list(dict.fromkeys(merged_topic['source_titles']))
                merged_topic['source_platforms'] = list(dict.fromkeys(merged_topic['source_platforms']))

                result.append(merged_topic)

        print(f"   ✅ 保守合并：{len(topics)} → {len(result)} 个话题")
        return result

    def _merge_category_topics(self, topics: List[Dict[str, Any]], category: str) -> List[Dict[str, Any]]:
        """合并同分类下的话题"""
        # 简化版本：保留重要性评分最高的话题，合并相似的
        topics_sorted = sorted(topics, key=lambda x: x.get('importance_score', 0), reverse=True)
        
        # 保留前几个重要话题
        keep_count = max(2, len(topics) // 3)
        return topics_sorted[:keep_count]


if __name__ == "__main__":
    # 测试话题合并器
    test_data = {
        'weibo': ['人工智能发展', 'AI技术突破', '机器学习应用'],
        'zhihu': ['人工智能前景', '深度学习算法', '自动驾驶技术'],
        'toutiao': ['AI芯片发展', '智能机器人', '语言模型进展']
    }
    
    merger = TopicMerger()
    result = merger.merge_topics(test_data)
    
    print(f"\n合并结果：")
    for i, topic in enumerate(result, 1):
        print(f"{i}. {topic['merged_title']} ({topic['category']})")
        print(f"   来源: {', '.join(topic['source_titles'])}")
        print(f"   评分: {topic['importance_score']}")
        print()
