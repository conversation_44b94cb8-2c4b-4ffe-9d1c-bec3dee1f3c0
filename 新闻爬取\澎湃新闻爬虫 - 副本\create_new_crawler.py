#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
澎湃新闻板块爬虫快速生成器
基于标准模板快速创建新板块爬虫
"""

import os
import re
import shutil
from datetime import datetime

class CrawlerGenerator:
    def __init__(self):
        self.template_file = "thepaper_finance_crawler.py"
        self.template_class = "ThepaperFinanceCrawler"
        self.template_channel_id = "25951"
        self.template_channel_name_cn = "财经"
        self.template_channel_name_en = "finance"
        
    def generate_crawler(self, channel_name_cn, channel_name_en, channel_id, channel_url=None):
        """生成新的板块爬虫"""
        
        # 验证输入
        if not all([channel_name_cn, channel_name_en, channel_id]):
            raise ValueError("板块名称（中英文）和ID都不能为空")
        
        if not channel_url:
            channel_url = f"https://m.thepaper.cn/channel_{channel_id}"
        
        # 生成文件名和类名
        new_filename = f"thepaper_{channel_name_en}_crawler.py"
        new_class_name = f"Thepaper{channel_name_en.capitalize()}Crawler"
        
        print(f"正在生成 {channel_name_cn} 板块爬虫...")
        print(f"文件名: {new_filename}")
        print(f"类名: {new_class_name}")
        print(f"板块ID: {channel_id}")
        print(f"板块URL: {channel_url}")
        
        # 检查模板文件是否存在
        if not os.path.exists(self.template_file):
            raise FileNotFoundError(f"模板文件 {self.template_file} 不存在")
        
        # 检查目标文件是否已存在
        if os.path.exists(new_filename):
            overwrite = input(f"文件 {new_filename} 已存在，是否覆盖？(y/N): ").strip().lower()
            if overwrite != 'y':
                print("操作已取消")
                return False
        
        # 读取模板文件
        with open(self.template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 执行替换
        content = self._replace_content(content, channel_name_cn, channel_name_en, channel_id, channel_url, new_class_name)
        
        # 写入新文件
        with open(new_filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 成功生成 {new_filename}")
        
        # 生成测试脚本
        self._generate_test_script(channel_name_cn, channel_name_en, new_filename)
        
        return True
    
    def _replace_content(self, content, channel_name_cn, channel_name_en, channel_id, channel_url, new_class_name):
        """执行内容替换"""
        
        replacements = [
            # 类名替换
            (self.template_class, new_class_name),
            
            # URL替换
            (f"channel_{self.template_channel_id}", f"channel_{channel_id}"),
            
            # channelId替换
            (f'"channelId": "{self.template_channel_id}"', f'"channelId": "{channel_id}"'),
            (f"channelId={self.template_channel_id}", f"channelId={channel_id}"),
            
            # channel标识替换
            ("'channel': 'finance'", f"'channel': '{channel_name_en}'"),
            
            # 中文名称替换
            (self.template_channel_name_cn, channel_name_cn),
            
            # 英文名称替换（注意顺序，避免部分替换）
            (f"thepaper_finance_", f"thepaper_{channel_name_en}_"),
            (f"finance_", f"{channel_name_en}_"),
            ("finance板块", f"{channel_name_cn}板块"),
            ("财经板块", f"{channel_name_cn}板块"),
            ("财经新闻", f"{channel_name_cn}新闻"),
            ("财经API", f"{channel_name_cn}API"),
            
            # 文档字符串替换
            ("澎湃新闻财经板块爬虫", f"澎湃新闻{channel_name_cn}板块爬虫"),
            
            # 主函数中的替换
            ("澎湃新闻财经板块爬虫启动", f"澎湃新闻{channel_name_cn}板块爬虫启动"),
            ("持续爬取财经板块", f"持续爬取{channel_name_cn}板块"),
            ("获取财经新闻详细内容", f"获取{channel_name_cn}新闻详细内容"),
            ("财经板块爬取完成", f"{channel_name_cn}板块爬取完成"),
            ("开始爬取财经板块", f"开始爬取{channel_name_cn}板块"),
            
            # 变量名替换
            ("ThepaperFinanceCrawler", new_class_name),
        ]
        
        # 执行替换
        for old, new in replacements:
            content = content.replace(old, new)
        
        return content
    
    def _generate_test_script(self, channel_name_cn, channel_name_en, crawler_filename):
        """生成测试脚本"""
        test_filename = f"test_{channel_name_en}_crawler.py"
        
        test_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{channel_name_cn}板块爬虫测试脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from {crawler_filename[:-3]} import Thepaper{channel_name_en.capitalize()}Crawler

def test_basic_functionality():
    """测试基本功能"""
    print("=== {channel_name_cn}板块爬虫基本功能测试 ===")
    
    crawler = Thepaper{channel_name_en.capitalize()}Crawler()
    
    # 测试1: HTML首页获取
    print("\\n1. 测试HTML首页获取...")
    try:
        html_news = crawler.get_first_page_html()
        if html_news:
            print(f"✅ 成功获取 {{len(html_news)}} 条HTML新闻")
            print(f"   示例标题: {{html_news[0].get('title', 'N/A')[:50]}}...")
        else:
            print("❌ HTML首页获取失败")
            return False
    except Exception as e:
        print(f"❌ HTML首页获取异常: {{e}}")
        return False
    
    # 测试2: API接口测试
    print("\\n2. 测试API接口...")
    try:
        api_result = crawler.get_news_by_api(page_num=2)
        if api_result and api_result.get('news_items'):
            print(f"✅ 成功获取 {{len(api_result['news_items'])}} 条API新闻")
            print(f"   示例标题: {{api_result['news_items'][0].get('title', 'N/A')[:50]}}...")
        else:
            print("❌ API接口测试失败")
            return False
    except Exception as e:
        print(f"❌ API接口测试异常: {{e}}")
        return False
    
    # 测试3: 时间过滤测试
    print("\\n3. 测试时间过滤...")
    try:
        import time
        current_timestamp = int(time.time() * 1000)
        is_today = crawler.is_today_news(current_timestamp)
        if is_today:
            print("✅ 时间过滤功能正常")
        else:
            print("❌ 时间过滤功能异常")
            return False
    except Exception as e:
        print(f"❌ 时间过滤测试异常: {{e}}")
        return False
    
    print("\\n🎉 所有基本功能测试通过！")
    return True

def test_quick_crawl():
    """快速爬取测试"""
    print("\\n=== 快速爬取测试 ===")
    
    crawler = Thepaper{channel_name_en.capitalize()}Crawler()
    
    try:
        # 只爬取前2页进行测试
        print("开始快速爬取测试（仅前2页）...")
        
        # 获取首页
        html_news = crawler.get_first_page_html()
        all_news = html_news.copy()
        
        # 获取第2页
        api_result = crawler.get_news_by_api(page_num=2)
        if api_result and api_result.get('news_items'):
            all_news.extend(api_result['news_items'])
        
        if all_news:
            # 保存测试数据
            test_filename = f"test_{channel_name_en}_{{datetime.now().strftime('%Y%m%d_%H%M%S')}}.csv"
            crawler.save_to_csv(all_news, test_filename)
            
            print(f"✅ 快速爬取测试完成")
            print(f"   总计获取: {{len(all_news)}} 条新闻")
            print(f"   测试数据已保存到: {{test_filename}}")
            
            # 显示前5条新闻
            print("\\n前5条新闻:")
            for i, news in enumerate(all_news[:5]):
                title = news.get('title', 'N/A')
                source = news.get('source', 'N/A')
                pub_time = news.get('pub_time', 'N/A')
                print(f"  {{i+1}}. {{title[:40]}}... ({{source}} - {{pub_time}})")
            
            return True
        else:
            print("❌ 快速爬取测试失败：未获取到数据")
            return False
            
    except Exception as e:
        print(f"❌ 快速爬取测试异常: {{e}}")
        return False

if __name__ == "__main__":
    print(f"开始测试 {channel_name_cn} 板块爬虫...")
    
    # 基本功能测试
    if test_basic_functionality():
        # 快速爬取测试
        test_quick_crawl()
    else:
        print("\\n❌ 基本功能测试失败，请检查配置")
        sys.exit(1)
    
    print(f"\\n🎉 {channel_name_cn} 板块爬虫测试完成！")
'''
        
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"✅ 成功生成测试脚本 {test_filename}")

def main():
    """主函数"""
    print("澎湃新闻板块爬虫生成器")
    print("=" * 50)
    
    generator = CrawlerGenerator()
    
    try:
        # 获取用户输入
        print("请输入新板块信息:")
        channel_name_cn = input("板块中文名称 (如: 科技): ").strip()
        channel_name_en = input("板块英文标识 (如: tech): ").strip().lower()
        channel_id = input("板块ID (如: 25952): ").strip()
        
        # 验证输入
        if not channel_name_cn:
            print("❌ 板块中文名称不能为空")
            return
        
        if not channel_name_en:
            print("❌ 板块英文标识不能为空")
            return
        
        if not channel_id.isdigit():
            print("❌ 板块ID必须是数字")
            return
        
        # 确认信息
        print(f"\\n确认信息:")
        print(f"板块名称: {channel_name_cn}")
        print(f"英文标识: {channel_name_en}")
        print(f"板块ID: {channel_id}")
        print(f"板块URL: https://m.thepaper.cn/channel_{channel_id}")
        print(f"生成文件: thepaper_{channel_name_en}_crawler.py")
        
        confirm = input("\\n确认生成？(Y/n): ").strip().lower()
        if confirm in ['', 'y', 'yes']:
            # 生成爬虫
            if generator.generate_crawler(channel_name_cn, channel_name_en, channel_id):
                print(f"\\n🎉 {channel_name_cn} 板块爬虫生成完成！")
                print("\\n下一步:")
                print(f"1. 运行测试: python test_{channel_name_en}_crawler.py")
                print(f"2. 正式使用: python thepaper_{channel_name_en}_crawler.py")
            else:
                print("❌ 生成失败")
        else:
            print("操作已取消")
    
    except KeyboardInterrupt:
        print("\\n\\n操作已中断")
    except Exception as e:
        print(f"\\n❌ 生成过程中出现错误: {e}")

if __name__ == "__main__":
    main()
