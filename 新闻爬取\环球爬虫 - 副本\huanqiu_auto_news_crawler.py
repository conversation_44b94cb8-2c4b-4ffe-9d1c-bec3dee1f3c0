#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环球网汽车新闻爬虫
按照新闻爬虫开发规范实现
"""

import requests
import json
import time
import csv
from datetime import datetime
from bs4 import BeautifulSoup
from fake_useragent import UserAgent


class HuanqiuAutoNewsCrawler:
    def __init__(self):
        self.base_url = "https://auto.huanqiu.com"
        self.api_url = "https://auto.huanqiu.com/api/list"
        
        # 汽车板块的节点ID
        self.nodes = [
            "/e3pmh24qk/e3pmh25cs",
            "/e3pmh24qk/e3pmh3bo0",
            "/e3pmh24qk/e3pss28ab",
            "/e3pmh24qk/e3pmtj57c",
            "/e3pmh24qk/e3pmtju0i",
            "/e3pmh24qk/e3pn4eqdt",
            "/e3pmh24qk/e3pn4el6u",
            "/e3pmh24qk/e3pn02mp3",
            "/e3pmh24qk/e3pn0asn6",
            "/e3pmh24qk/e3pmtkgc2",
            "/e3pmh24qk/e3pofth7r",
            "/e3pmh24qk/e3prcrrt0",
            "/e3pmh24qk/e3pmtj0sq",
            "/e3pmh24qk/e3pmtj3a3",
            "/e3pmh24qk/e3pn4ens6",
            "/e3pmh24qk/e3pn6fl79"
        ]
        
        self.ua = UserAgent()
        self.session = requests.Session()
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://auto.huanqiu.com/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        
        self.session.headers.update(self.headers)
    
    def get_news_list(self, max_news=None):
        """
        获取新闻列表
        
        Args:
            max_news (int): 最大新闻数量，None表示获取当天全部
            
        Returns:
            list: 新闻基本信息列表
        """
        news_list = []
        offset = 0
        limit = 24
        max_pages = 50  # 最大页数限制，防止无限循环
        page = 1
        
        print(f"开始获取今日汽车新闻列表...")
        
        while page <= max_pages:
            if max_news and len(news_list) >= max_news:
                break
                
            params = {
                'node': ','.join([f'"{node}"' for node in self.nodes]),
                'offset': offset,
                'limit': limit
            }
            
            try:
                response = self.session.get(self.api_url, params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                articles = data.get('list', [])
                
                if not articles:
                    print(f"第{page}页没有更多文章，停止获取")
                    break
                
                # 只获取今天的文章
                today = datetime.now().date()
                today_articles = []
                non_today_count = 0
                
                for article in articles:
                    # 检查是否为今日文章
                    ctime = article.get('ctime', 0)
                    if ctime and str(ctime).isdigit():
                        try:
                            if len(str(ctime)) > 10:
                                timestamp = int(ctime) / 1000
                            else:
                                timestamp = int(ctime)
                            
                            article_date = datetime.fromtimestamp(timestamp).date()
                            if article_date != today:
                                non_today_count += 1
                                continue
                        except:
                            continue
                    
                    article_id = article.get('aid')
                    title = article.get('title', '')
                    
                    if article_id and title:
                        news_item = {
                            'id': article_id,
                            'title': title,
                            'url': f"https://auto.huanqiu.com/article/{article_id}",
                            'summary': article.get('summary', ''),
                            'source_name': article.get('source', {}).get('name', '环球网'),
                            'ctime': ctime
                        }
                        today_articles.append(news_item)
                
                print(f"第{page}页: 共{len(articles)}篇文章，今日文章{len(today_articles)}篇")
                
                if not today_articles:
                    print("本页没有今日文章，停止获取")
                    break
                
                # 如果非今日文章过多，可能已经爬取完今日文章
                if non_today_count > len(today_articles) * 2 and page > 3:
                    print("非今日文章较多，可能已接近今日文章末尾")
                    news_list.extend(today_articles)
                    break
                
                news_list.extend(today_articles)
                
                # 如果指定了最大数量，检查是否已达到
                if max_news and len(news_list) >= max_news:
                    news_list = news_list[:max_news]
                    break
                
                offset += limit
                page += 1
                time.sleep(1)  # 避免请求过快
                
            except Exception as e:
                print(f"获取新闻列表失败: {e}")
                break
        
        print(f"共获取到 {len(news_list)} 条今日汽车新闻")
        return news_list
    
    def get_news_detail(self, url):
        """
        获取新闻详细内容
        
        Args:
            url (str): 新闻URL
            
        Returns:
            str: 新闻正文内容
        """
        try:
            # 随机更换User-Agent
            headers = {
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://auto.huanqiu.com/',
                'Connection': 'keep-alive',
            }
            
            response = self.session.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取文章内容
            content = ""
            content_div = soup.find('div', class_='content')
            if content_div:
                article_tag = content_div.find('article')
                if article_tag:
                    # 提取所有段落，过滤掉广告等元素
                    paragraphs = article_tag.find_all('p')
                    content_parts = []
                    for p in paragraphs:
                        # 跳过包含广告的段落
                        if p.find('adv-loader'):
                            continue
                        # 跳过图片说明文字
                        if p.find('i', class_='pic-con'):
                            continue
                        # 跳过视频相关内容
                        if p.find('video') or p.find('div', class_='video-con'):
                            continue
                        
                        text = p.get_text(strip=True)
                        if text and len(text) > 5:  # 过滤掉太短的文本
                            content_parts.append(text)
                    content = '\n\n'.join(content_parts)
            
            # 如果上面的方法没找到内容，尝试其他选择器
            if not content:
                selectors = [
                    '.la-content',
                    '.article-content', 
                    '.content article',
                    '.main-content',
                    '[data-type="rtext"]'
                ]
                
                for selector in selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        paragraphs = content_elem.find_all(['p', 'div'])
                        content_parts = []
                        for p in paragraphs:
                            # 跳过广告相关内容
                            if (p.find('adv-loader') or p.find('i', class_='pic-con') or 
                                p.find('video') or p.find('div', class_='video-con')):
                                continue
                            
                            text = p.get_text(strip=True)
                            if text and len(text) > 10:
                                content_parts.append(text)
                        if content_parts:
                            content = '\n\n'.join(content_parts)
                            break
            
            return content
            
        except Exception as e:
            print(f"获取文章内容失败 {url}: {e}")
            return ""
    
    def format_time(self, timestamp):
        """
        格式化时间为 MM-DD HH:MM 格式
        
        Args:
            timestamp: 时间戳
            
        Returns:
            str: 格式化后的时间字符串
        """
        try:
            if len(str(timestamp)) > 10:
                timestamp = int(timestamp) / 1000
            else:
                timestamp = int(timestamp)
            
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%m-%d %H:%M')
        except:
            return datetime.now().strftime('%m-%d %H:%M')
    
    def get_news(self, max_news=10, get_detail=True):
        """
        获取新闻数据 - 主入口函数
        
        Args:
            max_news (int): 最大新闻数量，默认10
            get_detail (bool): 是否获取详细内容，默认True
            
        Returns:
            list: 新闻数据列表，符合规范格式
        """
        print(f"开始爬取环球网汽车新闻，目标数量: {max_news if max_news else '全部'}")
        
        # 获取新闻列表
        news_list = self.get_news_list(max_news)
        
        if not news_list:
            print("未获取到新闻列表")
            return []
        
        print(f"获取到 {len(news_list)} 条新闻基本信息")
        
        # 构建返回数据
        result = []
        
        for i, news_item in enumerate(news_list, 1):
            print(f"正在处理第 {i} 条新闻: {news_item['title'][:30]}...")
            
            # 获取详细内容
            content = ""
            if get_detail:
                content = self.get_news_detail(news_item['url'])
            
            # 按照规范格式构建数据
            news_data = {
                'title': news_item['title'],
                'url': news_item['url'],
                'content': content,
                'category': '汽车',  # 汽车板块
                'publish_time': self.format_time(news_item['ctime']),
                'source': 'huanqiu_auto'  # 来源标识
            }
            
            result.append(news_data)
            
            # 避免请求过快
            if get_detail:
                time.sleep(1.5)
        
        print(f"爬取完成！共获取 {len(result)} 条新闻")
        return result


def get_news(max_news=10, get_detail=True):
    """
    获取环球网汽车新闻数据 - 外部调用接口
    
    Args:
        max_news (int): 最大新闻数量，默认10
        get_detail (bool): 是否获取详细内容，默认True
        
    Returns:
        list: 新闻数据列表
    """
    crawler = HuanqiuAutoNewsCrawler()
    return crawler.get_news(max_news, get_detail)


if __name__ == "__main__":
    # 爬取当天全部汽车新闻
    print("=== 环球网汽车新闻爬虫 ===")
    print("正在爬取当天全部汽车新闻...")
    
    news_data = get_news(max_news=None, get_detail=True)  # max_news=None 表示获取全部
    
    print(f"\n=== 爬取结果 ===")
    print(f"爬取到 {len(news_data)} 条今日汽车新闻")
    
    if news_data:
        # 保存到文件
        # 保存JSON格式
        json_filename = f"huanqiu_auto_news_{datetime.now().strftime('%Y%m%d')}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(news_data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {json_filename}")
        
        # 保存CSV格式
        csv_filename = f"huanqiu_auto_news_{datetime.now().strftime('%Y%m%d')}.csv"
        with open(csv_filename, 'w', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['标题', '链接', '分类', '发布时间', '来源', '内容长度', '内容'])
            for news in news_data:
                writer.writerow([
                    news['title'],
                    news['url'],
                    news['category'],
                    news['publish_time'],
                    news['source'],
                    len(news['content']),
                    news['content']
                ])
        print(f"数据已保存到: {csv_filename}")
        
        # 显示统计信息
        valid_content = [news for news in news_data if news['content']]
        if valid_content:
            avg_content_len = sum(len(news['content']) for news in valid_content) / len(valid_content)
            print(f"\n=== 统计信息 ===")
            print(f"平均内容长度: {avg_content_len:.0f} 字")
            print(f"有效内容文章数: {len(valid_content)}")
        
        # 显示前3篇文章
        print(f"\n=== 前3篇文章预览 ===")
        for i, news in enumerate(news_data[:3], 1):
            print(f"\n第{i}条:")
            print(f"标题: {news['title']}")
            print(f"时间: {news['publish_time']}")
            print(f"内容长度: {len(news['content'])}字")
            if news['content']:
                print(f"内容预览: {news['content'][:100]}...")
            print("-" * 50)
    else:
        print("未获取到任何新闻数据")
