#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
话题处理模块 - 分步走版本，避免JSON解析问题
依次处理每个合并后的话题，为后续新闻检索和文章生成做准备
"""

import json
import re
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from llm_client import LLMClient


class TopicProcessor:
    """话题处理器 - 分步走版本"""
    
    def __init__(self):
        """初始化话题处理器"""
        self.llm_client = LLMClient()
    
    def analyze_topic(self, topic: Dict[str, Any], max_retries: int = 3, use_parallel: bool = True) -> Dict[str, Any]:
        """
        分步骤分析话题，避免JSON解析问题

        Args:
            topic: 合并后的话题信息
            max_retries: 最大重试次数（对每个步骤）
            use_parallel: 是否使用并发处理

        Returns:
            分析结果，包含：
            - original_topic: 原始话题信息
            - keywords: 关键词列表
            - search_queries: 搜索查询词列表
            - background_context: 背景信息
            - key_points: 关键要点
            - related_entities: 相关实体（人物、机构、地点等）
        """
        print(f"🔍 开始分步分析话题: {topic['merged_title']}")
        
        result = {
            'original_topic': topic,
            'analysis_time': datetime.now().isoformat()
        }
        
        # 基础信息
        topic_title = topic['merged_title']
        topic_context = f"""
话题：{topic_title}
分类：{topic['category']}
重要性：{topic['importance_score']}/10
来源平台：{', '.join(topic['source_platforms'])}
相关标题：{', '.join(topic['source_titles'][:3])}
"""
        
        try:
            # 优先尝试优化的JSON方式（快速且可靠）
            print("   🚀 优先使用JSON方式...")
            json_result = self._try_json_analysis(topic, max_retries=2)
            if json_result and json_result.get('keywords'):
                print("   ✅ JSON方式成功")
                return json_result
            
            print("   🔄 JSON方式失败，使用分步方式...")
            if use_parallel:
                return self._analyze_parallel(topic_title, topic_context, result, max_retries)
            else:
                return self._analyze_sequential(topic_title, topic_context, result, max_retries)
            
        except Exception as e:
            print(f"❌ 分步分析失败: {e}")
            return self._fallback_analysis(topic)
    
    def _analyze_parallel(self, topic_title: str, topic_context: str, result: Dict[str, Any], max_retries: int) -> Dict[str, Any]:
        """并行分析各个步骤"""
        print("   🚀 使用并行模式分析...")
        
        # 定义任务函数
        def extract_keywords():
            return ('keywords', self._extract_keywords(topic_title, topic_context, max_retries))
        
        def generate_queries():
            return ('search_queries', self._generate_search_queries(topic_title, topic_context, max_retries))
        
        def analyze_background():
            return ('background_context', self._analyze_background(topic_title, topic_context, max_retries))
        
        def extract_points():
            return ('key_points', self._extract_key_points(topic_title, topic_context, max_retries))
        
        def identify_entities():
            return ('related_entities', self._identify_entities(topic_title, topic_context, max_retries))
        
        # 并行执行
        tasks = [extract_keywords, generate_queries, analyze_background, extract_points, identify_entities]
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            # 提交所有任务
            future_to_task = {executor.submit(task): task.__name__ for task in tasks}
            
            # 收集结果
            for future in as_completed(future_to_task):
                task_name = future_to_task[future]
                try:
                    key, value = future.result()
                    result[key] = value
                    print(f"   ✅ {task_name} 完成")
                except Exception as e:
                    print(f"   ❌ {task_name} 失败: {e}")
        
        print(f"✅ 并行分析完成")
        return result
    
    def _analyze_sequential(self, topic_title: str, topic_context: str, result: Dict[str, Any], max_retries: int) -> Dict[str, Any]:
        """串行分析各个步骤"""
        print("   🔄 使用串行模式分析...")
        
        # 步骤1: 提取关键词
        print("   📝 步骤1: 提取关键词...")
        result['keywords'] = self._extract_keywords(topic_title, topic_context, max_retries)
        
        # 步骤2: 生成搜索查询
        print("   🔍 步骤2: 生成搜索查询...")
        result['search_queries'] = self._generate_search_queries(topic_title, topic_context, max_retries)
        
        # 步骤3: 分析背景信息
        print("   📖 步骤3: 分析背景信息...")
        result['background_context'] = self._analyze_background(topic_title, topic_context, max_retries)
        
        # 步骤4: 提取关键要点
        print("   📋 步骤4: 提取关键要点...")
        result['key_points'] = self._extract_key_points(topic_title, topic_context, max_retries)
        
        # 步骤5: 识别相关实体
        print("   👥 步骤5: 识别相关实体...")
        result['related_entities'] = self._identify_entities(topic_title, topic_context, max_retries)
        
        print(f"✅ 串行分析完成")
        return result
    
    def _try_json_analysis(self, topic: Dict[str, Any], max_retries: int = 3) -> Optional[Dict[str, Any]]:
        """
        使用优化的JSON方式分析话题（借鉴话题合并的成功机制）
        """
        
        # 简化的系统提示词
        system_prompt = """你是专业的新闻分析师。请分析话题并返回JSON格式结果。

输出格式（严格按此格式）：
```json
{
  "keywords": ["关键词1", "关键词2", "关键词3"],
  "queries": ["搜索词1", "搜索词2", "搜索词3"],
  "background": "背景信息一段话",
  "points": ["要点1", "要点2", "要点3"],
  "people": ["人物1", "人物2"],
  "orgs": ["机构1", "机构2"],
  "places": ["地点1", "地点2"]
}
```

只输出JSON，不要其他内容。"""

        # 简化的用户提示词
        user_prompt = f"""分析话题：{topic['merged_title']}
分类：{topic['category']}
评分：{topic['importance_score']}/10
平台：{', '.join(topic['source_platforms'])}

相关标题：
{chr(10).join(f"- {title}" for title in topic['source_titles'][:3])}

请输出JSON分析结果："""

        for retry in range(max_retries):
            try:
                response = self.llm_client.chat(
                    prompt=user_prompt,
                    system_prompt=system_prompt,
                    max_tokens=2000,
                    temperature=0.3
                )
                
                if response and response.strip():
                    # 使用话题合并的JSON解析方法
                    parsed_result = self._parse_json_response(response)
                    if parsed_result:
                        # 转换为标准格式
                        return {
                            'original_topic': topic,
                            'analysis_time': datetime.now().isoformat(),
                            'keywords': parsed_result.get('keywords', []),
                            'search_queries': parsed_result.get('queries', []),
                            'background_context': parsed_result.get('background', ''),
                            'key_points': parsed_result.get('points', []),
                            'related_entities': {
                                'persons': parsed_result.get('people', []),
                                'organizations': parsed_result.get('orgs', []),
                                'locations': parsed_result.get('places', []),
                                'others': []
                            }
                        }
                
                pass  # 静默失败，让外层处理
                
            except Exception as e:
                pass  # 静默失败，让外层处理
        
        # 返回None表示JSON方式失败，让外层决定使用分步方式
        return None
    
    def _parse_json_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析JSON响应（借鉴话题合并的方法）"""
        if not response or not response.strip():
            return None

        try:
            response = response.strip()
            json_str = None

            # 方式1：提取```json```包装的内容
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1).strip()

            # 方式2：提取```包装的内容
            elif '```' in response:
                json_match = re.search(r'```\s*(.*?)\s*```', response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1).strip()

            # 方式3：查找JSON对象开始和结束
            elif '{' in response and '}' in response:
                start = response.find('{')
                end = response.rfind('}') + 1
                json_str = response[start:end].strip()

            # 方式4：直接尝试整个响应
            else:
                json_str = response

            if not json_str:
                return None

            # JSON修复
            json_str = self._fix_json_format(json_str)

            # 解析JSON
            result = json.loads(json_str)
            
            if isinstance(result, dict):
                return result

        except Exception as e:
            print(f"⚠️ JSON解析失败: {e}")

        return None
    
    def _fix_json_format(self, json_str: str) -> str:
        """修复JSON格式（借鉴话题合并的方法）"""
        try:
            json_str = json_str.strip()

            # 确保以{开头，}结尾
            if not json_str.startswith('{'):
                start_idx = json_str.find('{')
                if start_idx != -1:
                    json_str = json_str[start_idx:]

            if not json_str.endswith('}'):
                end_idx = json_str.rfind('}')
                if end_idx != -1:
                    json_str = json_str[:end_idx + 1]
                else:
                    json_str = json_str.rstrip(',') + '}'

            # 修复引号问题
            json_str = json_str.replace("'", '"')
            
            return json_str

        except Exception:
            return json_str
    
    def _extract_keywords(self, topic_title: str, context: str, max_retries: int = 3) -> List[str]:
        """步骤1: 提取关键词 - 返回纯文本列表"""
        
        prompt = f"""请为以下话题提取3-8个最重要的关键词：

{context}

要求：
- 只返回关键词，用逗号分隔
- 不要编号，不要解释，不要其他文字
- 关键词要准确、具体，便于搜索
- 例如：人工智能,ChatGPT,技术发展,AI应用

请提取关键词："""

        for retry in range(max_retries):
            try:
                response = self.llm_client.chat(
                    prompt=prompt,
                    max_tokens=200,
                    temperature=0.3
                )
                
                # 解析逗号分隔的关键词
                if response and response.strip():
                    # 清理响应，去除可能的前缀文字
                    cleaned_response = response.strip()
                    
                    # 如果包含"关键词"等前缀，尝试提取
                    if '：' in cleaned_response:
                        cleaned_response = cleaned_response.split('：')[-1]
                    if ':' in cleaned_response:
                        cleaned_response = cleaned_response.split(':')[-1]
                    
                    keywords = [kw.strip() for kw in cleaned_response.split(',') if kw.strip()]
                    # 过滤掉过长或包含无关文字的关键词
                    valid_keywords = []
                    for kw in keywords:
                        if len(kw) <= 20 and not any(x in kw for x in ['请', '如下', '分别是', '等等']):
                            valid_keywords.append(kw)
                    
                    if valid_keywords:
                        return valid_keywords[:8]  # 限制数量
                
                print(f"⚠️ 关键词提取第{retry+1}次尝试结果为空")
                
            except Exception as e:
                print(f"⚠️ 关键词提取第{retry+1}次失败: {e}")
        
        print(f"❌ 关键词提取失败，使用备用方案")
        return self._fallback_keywords(topic_title)
    
    def _generate_search_queries(self, topic_title: str, context: str, max_retries: int = 3) -> List[str]:
        """步骤2: 生成搜索查询 - 返回纯文本列表"""
        
        prompt = f"""请为以下话题生成5-8个不同角度的搜索查询词：

{context}

要求：
- 每行一个搜索查询词
- 不要编号，不要解释
- 包含不同表述方式和角度
- 每个查询词5-20字
- 例如：
人工智能最新发展
ChatGPT技术突破
AI对社会的影响

请生成搜索查询："""

        for retry in range(max_retries):
            try:
                response = self.llm_client.chat(
                    prompt=prompt,
                    max_tokens=500,
                    temperature=0.3
                )
                
                if response and response.strip():
                    # 按行分割搜索查询
                    lines = response.strip().split('\n')
                    queries = []
                    
                    for line in lines:
                        # 清理每行
                        cleaned = line.strip()
                        # 去除开头的数字编号
                        cleaned = re.sub(r'^\d+[\.\)]\s*', '', cleaned)
                        # 去除项目符号
                        cleaned = re.sub(r'^[•·-]\s*', '', cleaned)
                        
                        if cleaned and 5 <= len(cleaned) <= 30:
                            queries.append(cleaned)
                    
                    if queries:
                        return queries[:8]  # 限制数量
                
                print(f"⚠️ 搜索查询生成第{retry+1}次尝试结果为空")
                
            except Exception as e:
                print(f"⚠️ 搜索查询生成第{retry+1}次失败: {e}")
        
        print(f"❌ 搜索查询生成失败，使用备用方案")
        return self._fallback_queries(topic_title)
    
    def _analyze_background(self, topic_title: str, context: str, max_retries: int = 3) -> str:
        """步骤3: 分析背景信息 - 返回纯文本"""
        
        prompt = f"""请为以下话题写一段背景信息（100-200字）：

{context}

要求：
- 解释话题的背景和起因
- 说明为什么成为热点
- 只返回背景文字，不要标题或格式
- 客观描述，不要主观评论
- 一段话即可，不要分段

请写背景信息："""

        for retry in range(max_retries):
            try:
                response = self.llm_client.chat(
                    prompt=prompt,
                    max_tokens=800,
                    temperature=0.3
                )
                
                if response and response.strip():
                    # 清理响应
                    cleaned = response.strip()
                    
                    # 去除可能的前缀
                    for prefix in ['背景信息：', '背景：', '分析：', '说明：']:
                        if cleaned.startswith(prefix):
                            cleaned = cleaned[len(prefix):].strip()
                    
                    if len(cleaned) >= 20:  # 确保有足够内容
                        return cleaned
                
                print(f"⚠️ 背景分析第{retry+1}次尝试结果为空")
                
            except Exception as e:
                print(f"⚠️ 背景分析第{retry+1}次失败: {e}")
        
        print(f"❌ 背景分析失败，使用备用方案")
        return f"{topic_title}相关事件成为近期热点话题，引发广泛关注和讨论。"
    
    def _extract_key_points(self, topic_title: str, context: str, max_retries: int = 3) -> List[str]:
        """步骤4: 提取关键要点 - 返回纯文本列表"""
        
        prompt = f"""请为以下话题列出3-6个关键要点：

{context}

要求：
- 每行一个要点
- 不要编号，不要解释
- 要点要简洁明确，每个要点10-30字
- 包括主要事实、争议点、影响等
- 例如：
技术突破带来新机遇
引发行业竞争加剧
对就业市场产生影响

请列出关键要点："""

        for retry in range(max_retries):
            try:
                response = self.llm_client.chat(
                    prompt=prompt,
                    max_tokens=600,
                    temperature=0.3
                )
                
                if response and response.strip():
                    # 按行分割要点
                    lines = response.strip().split('\n')
                    points = []
                    
                    for line in lines:
                        # 清理每行
                        cleaned = line.strip()
                        # 去除开头的数字编号
                        cleaned = re.sub(r'^\d+[\.\)]\s*', '', cleaned)
                        # 去除项目符号
                        cleaned = re.sub(r'^[•·-]\s*', '', cleaned)
                        
                        if cleaned and 5 <= len(cleaned) <= 50:
                            points.append(cleaned)
                    
                    if points:
                        return points[:6]  # 限制数量
                
                print(f"⚠️ 关键要点提取第{retry+1}次尝试结果为空")
                
            except Exception as e:
                print(f"⚠️ 关键要点提取第{retry+1}次失败: {e}")
        
        print(f"❌ 关键要点提取失败，使用备用方案")
        return [f"{topic_title}成为热点话题", "引发社会广泛关注", "相关讨论持续升温"]
    
    def _identify_entities(self, topic_title: str, context: str, max_retries: int = 3) -> Dict[str, List[str]]:
        """步骤5: 识别相关实体 - 分别获取不同类型实体"""
        
        result = {
            'persons': [],
            'organizations': [], 
            'locations': [],
            'others': []
        }
        
        # 5.1 识别人物
        persons_prompt = f"""从以下话题中识别相关人物：

{context}

要求：
- 只返回人物姓名，用逗号分隔
- 不要解释，不要职务，不要其他文字
- 例如：张三,李四,王五

相关人物："""
        
        for retry in range(max_retries):
            try:
                persons_response = self.llm_client.chat(
                    prompt=persons_prompt,
                    max_tokens=200,
                    temperature=0.3
                )
                
                if persons_response and persons_response.strip():
                    # 清理响应
                    cleaned = persons_response.strip()
                    for prefix in ['相关人物：', '人物：', '姓名：']:
                        if cleaned.startswith(prefix):
                            cleaned = cleaned[len(prefix):].strip()
                    
                    if cleaned and cleaned != '无' and cleaned != '暂无':
                        persons = [p.strip() for p in cleaned.split(',') if p.strip()]
                        # 过滤无关内容
                        valid_persons = []
                        for person in persons:
                            if len(person) <= 10 and not any(x in person for x in ['先生', '女士', '部长', '总统']):
                                valid_persons.append(person)
                        result['persons'] = valid_persons[:5]
                    break
                    
            except Exception as e:
                print(f"⚠️ 人物识别第{retry+1}次失败: {e}")
        
        # 5.2 识别机构
        orgs_prompt = f"""从以下话题中识别相关机构组织：

{context}

要求：
- 只返回机构名称，用逗号分隔
- 包括公司、政府部门、组织等
- 例如：苹果公司,教育部,联合国

相关机构："""
        
        for retry in range(max_retries):
            try:
                orgs_response = self.llm_client.chat(
                    prompt=orgs_prompt,
                    max_tokens=200,
                    temperature=0.3
                )
                
                if orgs_response and orgs_response.strip():
                    cleaned = orgs_response.strip()
                    for prefix in ['相关机构：', '机构：', '组织：']:
                        if cleaned.startswith(prefix):
                            cleaned = cleaned[len(prefix):].strip()
                    
                    if cleaned and cleaned != '无' and cleaned != '暂无':
                        orgs = [o.strip() for o in cleaned.split(',') if o.strip()]
                        valid_orgs = [org for org in orgs if len(org) <= 20]
                        result['organizations'] = valid_orgs[:5]
                    break
                    
            except Exception as e:
                print(f"⚠️ 机构识别第{retry+1}次失败: {e}")
        
        # 5.3 识别地点
        locations_prompt = f"""从以下话题中识别相关地点：

{context}

要求：
- 只返回地点名称，用逗号分隔
- 包括城市、国家、地区等
- 例如：北京,美国,欧洲

相关地点："""
        
        for retry in range(max_retries):
            try:
                locations_response = self.llm_client.chat(
                    prompt=locations_prompt,
                    max_tokens=200,
                    temperature=0.3
                )
                
                if locations_response and locations_response.strip():
                    cleaned = locations_response.strip()
                    for prefix in ['相关地点：', '地点：', '地区：']:
                        if cleaned.startswith(prefix):
                            cleaned = cleaned[len(prefix):].strip()
                    
                    if cleaned and cleaned != '无' and cleaned != '暂无':
                        locations = [l.strip() for l in cleaned.split(',') if l.strip()]
                        valid_locations = [loc for loc in locations if len(loc) <= 15]
                        result['locations'] = valid_locations[:5]
                    break
                    
            except Exception as e:
                print(f"⚠️ 地点识别第{retry+1}次失败: {e}")
        
        return result
    
    def _fallback_keywords(self, topic_title: str) -> List[str]:
        """备用关键词提取"""
        # 简单的关键词提取逻辑
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', topic_title)
        return [word for word in words if len(word) > 1][:5]
    
    def _fallback_queries(self, topic_title: str) -> List[str]:
        """备用搜索查询生成"""
        return [
            f"{topic_title}最新情况",
            f"{topic_title}详细报道", 
            f"{topic_title}分析评论",
            f"{topic_title}相关新闻"
        ]
    
    def _fallback_analysis(self, topic: Dict[str, Any]) -> Dict[str, Any]:
        """完全失败时的备用分析"""
        topic_title = topic['merged_title']
        
        return {
            'original_topic': topic,
            'analysis_time': datetime.now().isoformat(),
            'keywords': self._fallback_keywords(topic_title),
            'search_queries': self._fallback_queries(topic_title),
            'background_context': f"{topic_title}成为近期热点话题，引发广泛关注。",
            'key_points': [f"{topic_title}引发关注", "相关讨论持续", "影响逐步显现"],
            'related_entities': {
                'persons': [],
                'organizations': [],
                'locations': [],
                'others': []
            }
        }
    
    # 保留原版本作为备用方法
    def analyze_topic_original(self, topic: Dict[str, Any], max_retries: int = 5) -> Dict[str, Any]:
        """
        原版本分析方法（JSON方式）- 作为备用
        """
        print(f"🔄 使用原版本分析方法: {topic['merged_title']}")
        
        # 这里可以调用原始的JSON解析版本
        # 如果需要，可以从backup文件导入
        return self._fallback_analysis(topic)
    
    def process_all_topics(self, merged_topics: List[Dict[str, Any]],
                          use_concurrent: bool = True) -> List[Dict[str, Any]]:
        """
        处理所有合并后的话题

        Args:
            merged_topics: 合并后的话题列表
            use_concurrent: 是否使用并发处理

        Returns:
            处理结果列表
        """
        print(f"🚀 开始处理 {len(merged_topics)} 个话题...")
        print("=" * 60)

        # 支持并发和串行两种模式
        if use_concurrent:
            return self._process_topics_concurrent(merged_topics)
        else:
            return self._process_topics_sequential(merged_topics)

    def _process_topics_concurrent(self, merged_topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """并发处理话题"""
        print(f"🚀 使用并发模式处理话题...")
        
        processed_topics = []
        
        # 定义单个话题处理函数
        def process_single_topic(topic_with_index):
            index, topic = topic_with_index
            try:
                print(f"📋 处理话题 {index+1}/{len(merged_topics)}: {topic['merged_title']}")
                result = self.analyze_topic(topic)
                
                # 显示简要结果
                keywords = result.get('keywords', [])
                print(f"🔑 话题{index+1}关键词: {', '.join(keywords[:3])}{'...' if len(keywords) > 3 else ''}")
                
                return result
                
            except Exception as e:
                print(f"❌ 处理话题{index+1}失败: {e}")
                return {
                    'original_topic': topic,
                    'error': str(e),
                    'analysis_time': datetime.now().isoformat()
                }
        
        # 使用ThreadPoolExecutor并发处理（针对4C16G服务器优化）
        max_workers = min(8, len(merged_topics))  # 最多8个并发
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(process_single_topic, (i, topic)): i 
                for i, topic in enumerate(merged_topics)
            }
            
            # 收集结果（按原顺序）
            results = [None] * len(merged_topics)
            
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results[index] = result
                except Exception as e:
                    print(f"❌ 话题{index+1}处理异常: {e}")
                    results[index] = {
                        'original_topic': merged_topics[index],
                        'error': str(e),
                        'analysis_time': datetime.now().isoformat()
                    }
            
            # 过滤None值
            processed_topics = [r for r in results if r is not None]
        
        print("\n" + "=" * 60)
        print(f"✅ 并发话题处理完成! 成功处理 {len(processed_topics)} 个话题")
        
        return processed_topics

    def _process_topics_sequential(self, merged_topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """串行处理话题"""
        print(f"🔄 使用串行模式处理话题...")

        processed_topics = []

        for i, topic in enumerate(merged_topics, 1):
            print(f"\n📋 处理进度: {i}/{len(merged_topics)}")

            try:
                # 分析话题（使用新的分步走方法）
                analysis_result = self.analyze_topic(topic)
                processed_topics.append(analysis_result)

                # 显示简要结果
                keywords = analysis_result.get('keywords', [])
                print(f"🔑 关键词: {', '.join(keywords[:3])}{'...' if len(keywords) > 3 else ''}")

                # 请求间隔
                if i < len(merged_topics):
                    print(f"⏳ 等待 1 秒...")
                    time.sleep(1)

            except Exception as e:
                print(f"❌ 处理话题失败: {e}")
                # 添加错误记录
                processed_topics.append({
                    'original_topic': topic,
                    'error': str(e),
                    'analysis_time': datetime.now().isoformat()
                })

        print("\n" + "=" * 60)
        print(f"✅ 话题处理完成! 成功处理 {len(processed_topics)} 个话题")

        return processed_topics


if __name__ == "__main__":
    # 测试话题处理器
    test_topic = {
        'merged_title': '人工智能技术发展',
        'category': '科技',
        'source_platforms': ['weibo', 'zhihu'],
        'source_titles': ['AI技术突破', '人工智能前景'],
        'importance_score': 8
    }
    
    processor = TopicProcessor()
    result = processor.analyze_topic(test_topic)
    
    print(f"\n分析结果：")
    print(f"关键词: {result.get('keywords', [])}")
    print(f"搜索词: {result.get('search_queries', [])}")
    print(f"背景: {result.get('background_context', '')}")
    print(f"要点: {result.get('key_points', [])}")
    print(f"实体: {result.get('related_entities', {})}")