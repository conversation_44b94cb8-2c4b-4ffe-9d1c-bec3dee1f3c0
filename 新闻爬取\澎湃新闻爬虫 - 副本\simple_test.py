#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试澎湃新闻主控制器
"""

from thepaper_master_crawler import get_news
import json

def main():
    print("澎湃新闻主控制器简单测试")
    print("=" * 40)
    
    try:
        # 测试获取少量新闻
        print("正在获取5条新闻...")
        news_data = get_news(max_news=5, get_detail=False)
        
        if news_data:
            print(f"成功获取 {len(news_data)} 条新闻")
            
            # 保存为JSON
            with open('test_result.json', 'w', encoding='utf-8') as f:
                json.dump(news_data, f, ensure_ascii=False, indent=2)
            
            print("\n新闻预览:")
            for i, news in enumerate(news_data):
                print(f"{i+1}. {news['title']}")
                print(f"   分类: {news['category']}")
                print(f"   时间: {news['publish_time']}")
                print(f"   来源: {news['source']}")
                print()
                
            print("测试结果已保存到 test_result.json")
        else:
            print("未获取到新闻数据")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
