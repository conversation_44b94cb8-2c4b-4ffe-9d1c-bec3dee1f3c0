#!/bin/bash
# 文章生成流水线定时任务脚本
# 每4小时执行一次

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 设置日志文件
LOG_DIR="logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/article_generation_$(date +%Y%m%d).log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log "🚀 开始文章生成流水线任务..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    log "❌ Python3 未找到"
    exit 1
fi

# 检查必要文件
if [ ! -f "quick_start.py" ]; then
    log "❌ quick_start.py 文件不存在"
    exit 1
fi

# 检查是否有新闻数据
NEWS_FILES=$(find news_output -name "*.json" -mmin -240 2>/dev/null | wc -l)
if [ "$NEWS_FILES" -eq 0 ]; then
    log "⚠️ 警告: 4小时内没有新的新闻数据文件"
fi

# 执行文章生成流水线 (--auto 参数表示自动化无交互模式)
log "📝 执行文章生成流水线..."
log "🔄 包含: 话题合并 → 深度分析 → 新闻检索 → 文章生成"

if python3 quick_start.py --auto >> "$LOG_FILE" 2>&1; then
    log "✅ 文章生成流水线完成"
    
    # 检查生成的文章数量
    ARTICLE_COUNT=$(python3 -c "
import mysql.connector
from config_loader import ConfigLoader
try:
    config = ConfigLoader()
    db_config = config.get_database_config()
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM articles WHERE created_at >= DATE_SUB(NOW(), INTERVAL 4 HOUR)')
    count = cursor.fetchone()[0]
    print(count)
    conn.close()
except:
    print(0)
" 2>/dev/null)
    
    log "📊 本次生成文章数: ${ARTICLE_COUNT:-0}"
    
    # 清理旧日志 (保留7天)
    find "$LOG_DIR" -name "article_generation_*.log" -mtime +7 -delete 2>/dev/null
    
    exit 0
else
    log "❌ 文章生成流水线失败"
    exit 1
fi
