#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试深度板块爬虫
"""

from thepaper_master_crawler import ThepaperMasterCrawler

def main():
    print("测试深度板块爬虫")
    print("=" * 40)
    
    try:
        crawler = ThepaperMasterCrawler()
        
        # 只测试深度板块
        news_data = crawler.get_news(max_news=5, get_detail=False, channels=['depth'], parallel=False)
        
        if news_data:
            print(f"深度板块获取到 {len(news_data)} 条新闻")
            
            for i, news in enumerate(news_data):
                print(f"\n第{i+1}条:")
                print(f"标题: {news['title']}")
                print(f"分类: {news['category']}")
                print(f"时间: {news['publish_time']}")
                print(f"链接: {news['url']}")
        else:
            print("深度板块未获取到数据")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
