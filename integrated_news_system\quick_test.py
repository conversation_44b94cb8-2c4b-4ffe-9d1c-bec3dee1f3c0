#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 简化版
测试集成新闻系统的核心功能
"""

import os
import sys
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main_pipeline import IntegratedNewsPipeline

def print_header(title):
    """打印标题"""
    print(f"\n{'='*50}")
    print(f"🧪 {title}")
    print('='*50)

def quick_test():
    """快速测试"""
    print_header("集成新闻系统快速测试")
    
    start_time = time.time()
    
    try:
        # 初始化
        print("🔧 初始化系统...")
        pipeline = IntegratedNewsPipeline()
        print("✅ 系统初始化成功")
        
        # 模拟少量新闻数据进行测试
        print("\n📰 使用模拟数据进行测试...")
        
        # 创建测试数据
        test_news_data = {
            'weibo': [
                {
                    'title': '大三女生和男友双双坠亡',
                    'url': 'test_url_1',
                    'hot_value': '100万',
                    'platform': 'weibo'
                },
                {
                    'title': '蜜雪冰城员工用脚关水桶',
                    'url': 'test_url_2', 
                    'hot_value': '80万',
                    'platform': 'weibo'
                },
                {
                    'title': '特朗普关税政策影响',
                    'url': 'test_url_3',
                    'hot_value': '60万',
                    'platform': 'weibo'
                }
            ],
            'zhihu': [
                {
                    'title': '如何看待学生放弃清北选择其他学校',
                    'url': 'test_url_4',
                    'hot_value': '50万',
                    'platform': 'zhihu'
                },
                {
                    'title': '女子坐11小时飞机后心跳骤停',
                    'url': 'test_url_5',
                    'hot_value': '40万',
                    'platform': 'zhihu'
                }
            ]
        }
        
        # 转换为列表格式
        all_news = []
        for platform, news_list in test_news_data.items():
            all_news.extend(news_list)
        
        print(f"✅ 模拟数据准备完成: {len(all_news)} 条新闻")
        
        # 步骤2: 话题合并
        print("\n🔄 测试话题合并...")
        merge_result = pipeline._step2_merge_topics(test_news_data, max_topics=3)
        
        if not merge_result['success']:
            print(f"❌ 话题合并失败: {merge_result.get('error')}")
            return False
        
        print(f"✅ 话题合并成功: {len(all_news)} → {merge_result['merged_count']} 个话题")
        
        # 显示合并后的话题
        for i, topic in enumerate(merge_result['merged_topics'][:3], 1):
            print(f"   {i}. {topic.get('merged_title', '未知话题')}")
        
        # 步骤3: 深度分析 (只分析第一个话题)
        print("\n🧠 测试深度分析...")
        test_topics = merge_result['merged_topics'][:1]  # 只分析1个话题
        
        analysis_result = pipeline._step3_process_topics(test_topics)
        
        if not analysis_result['success']:
            print(f"❌ 深度分析失败: {analysis_result.get('error')}")
            return False
        
        print(f"✅ 深度分析成功: {analysis_result['processed_count']} 个话题")
        
        # 显示分析结果
        if analysis_result['processed_topics']:
            topic = analysis_result['processed_topics'][0]
            print(f"   话题: {topic.get('original_topic', {}).get('merged_title', '未知')}")
            print(f"   关键词: {', '.join(topic.get('keywords', [])[:3])}")
            print(f"   搜索词: {len(topic.get('search_queries', []))} 个")
        
        # 步骤4: 新闻搜索
        print("\n🔍 测试新闻搜索...")
        search_result = pipeline._step4_search_news(analysis_result['processed_topics'])
        
        if not search_result['success']:
            print(f"❌ 新闻搜索失败: {search_result.get('error')}")
            return False
        
        print(f"✅ 新闻搜索成功: {search_result['searched_topics']} 个话题")
        
        # 显示搜索结果
        if search_result['search_results']:
            result = search_result['search_results'][0]
            print(f"   找到相关新闻: {result.get('total_found', 0)} 条")
        
        # 步骤5: 文章生成
        print("\n📝 测试文章生成...")
        article_result = pipeline._step5_generate_articles(
            analysis_result['processed_topics'][:1],  # 只生成1篇
            search_result['search_results'][:1]
        )
        
        if not article_result['success']:
            print(f"❌ 文章生成失败: {article_result.get('error')}")
            return False
        
        print(f"✅ 文章生成成功: {len(article_result['articles'])} 篇")
        
        # 显示文章信息
        if article_result['articles']:
            article = article_result['articles'][0]
            print(f"   标题: {article.get('title', '无标题')}")
            print(f"   字数: {article.get('word_count', 0)}")
            print(f"   分类: {article.get('category', '未知')}")
        
        # 步骤6: 保存到数据库
        print("\n💾 测试数据库保存...")
        save_result = pipeline._step6_save_to_database(article_result['articles'])
        
        if not save_result['success']:
            print(f"❌ 数据库保存失败: {save_result.get('error')}")
            return False
        
        print(f"✅ 数据库保存成功: {save_result['saved_count']} 篇")
        
        # 测试完成
        end_time = time.time()
        total_time = end_time - start_time
        
        print_header("测试完成")
        print(f"🎉 快速测试全部通过!")
        print(f"⏱️  总耗时: {total_time:.1f} 秒")
        print(f"\n📊 测试摘要:")
        print(f"   📰 模拟新闻: {len(all_news)} 条")
        print(f"   🔄 合并话题: {merge_result['merged_count']} 个")
        print(f"   🧠 深度分析: {analysis_result['processed_count']} 个")
        print(f"   🔍 搜索话题: {search_result['searched_topics']} 个")
        print(f"   📝 生成文章: {len(article_result['articles'])} 篇")
        print(f"   💾 保存文章: {save_result['saved_count']} 篇")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 集成新闻系统快速测试工具")
    print("   使用模拟数据测试核心功能")
    
    try:
        success = quick_test()
        
        if success:
            print(f"\n🎉 测试成功! 系统运行正常")
            print(f"💡 提示: 可以运行 python quick_start.py 进行完整测试")
        else:
            print(f"\n❌ 测试失败! 请检查配置和错误信息")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
