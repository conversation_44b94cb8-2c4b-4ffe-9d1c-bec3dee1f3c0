#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有API key的状态
"""

import requests
import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from config import get_llm_config
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

def test_single_api_key(api_key: str, key_index: int) -> bool:
    """测试单个API key"""
    print(f"\n🧪 测试API Key {key_index}: {api_key[:20]}...")
    
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "DeepSeek-V3-Fast",
            "messages": [
                {"role": "user", "content": "你好，这是一个测试请求，请简单回复。"}
            ],
            "max_tokens": 50,
            "temperature": 0.3
        }
        
        response = requests.post(
            "https://www.sophnet.com/api/open-apis/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"   📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ API Key {key_index} 正常工作")
            return True
        elif response.status_code == 422:
            try:
                error_detail = response.json()
                print(f"   ❌ API Key {key_index} 错误: {error_detail}")
                if error_detail.get('status') == 20109:
                    print(f"   💰 API Key {key_index} 余额不足")
                return False
            except:
                print(f"   ❌ API Key {key_index} 422错误: {response.text}")
                return False
        else:
            print(f"   ❌ API Key {key_index} 其他错误: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ API Key {key_index} 测试异常: {e}")
        return False

def test_all_api_keys():
    """测试所有API key"""
    print("🚀 开始测试所有API Key状态")
    print("=" * 60)
    
    try:
        llm_config = get_llm_config()
        api_keys = llm_config.get("api_keys", [])
        
        if not api_keys:
            print("❌ 没有找到API Key配置")
            return
        
        print(f"📋 找到 {len(api_keys)} 个API Key")
        
        working_keys = []
        failed_keys = []
        
        for i, api_key in enumerate(api_keys, 1):
            if test_single_api_key(api_key, i):
                working_keys.append(i)
            else:
                failed_keys.append(i)
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"   ✅ 正常工作的API Key: {len(working_keys)} 个 - {working_keys}")
        print(f"   ❌ 有问题的API Key: {len(failed_keys)} 个 - {failed_keys}")
        
        if working_keys:
            print(f"🎉 有 {len(working_keys)} 个API Key可以正常使用")
        else:
            print("⚠️ 所有API Key都有问题，请检查配置或充值")
            
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")

if __name__ == "__main__":
    test_all_api_keys()
