#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的主控制器
"""

from thepaper_master_crawler import get_news

def main():
    print("测试修复后的主控制器")
    print("=" * 40)
    
    try:
        # 测试少量新闻，串行模式
        print("开始测试（串行模式，少量数据）...")
        news_data = get_news(max_news=20, get_detail=False)
        
        if news_data:
            print(f"\n总计获取: {len(news_data)} 条新闻")
            
            # 按板块统计
            category_stats = {}
            for news in news_data:
                category = news['category']
                category_stats[category] = category_stats.get(category, 0) + 1
            
            print("\n各板块新闻数量:")
            for category, count in category_stats.items():
                print(f"  {category}: {count} 条")
            
            # 显示前几条
            print(f"\n前5条新闻:")
            for i, news in enumerate(news_data[:5]):
                print(f"{i+1}. [{news['category']}] {news['title'][:50]}...")
                
        else:
            print("未获取到新闻数据")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
