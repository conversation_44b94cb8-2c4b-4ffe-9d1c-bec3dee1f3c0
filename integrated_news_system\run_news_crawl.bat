@echo off
REM 新闻爬取和向量化定时任务脚本 (Windows版本)
REM 每2小时执行一次

setlocal enabledelayedexpansion

REM 设置工作目录
cd /d "%~dp0"

REM 设置日志目录
if not exist "logs" mkdir logs
set LOG_FILE=logs\news_crawl_%date:~0,4%%date:~5,2%%date:~8,2%.log

REM 日志函数
set TIMESTAMP=%date% %time%
echo [%TIMESTAMP%] 🚀 开始新闻爬取和向量化任务... >> %LOG_FILE%

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo [%TIMESTAMP%] ❌ Python 未找到 >> %LOG_FILE%
    exit /b 1
)

REM 检查必要文件
if not exist "master_pipeline.py" (
    echo [%TIMESTAMP%] ❌ master_pipeline.py 文件不存在 >> %LOG_FILE%
    exit /b 1
)

REM 执行新闻爬取 (all参数表示爬取所有新闻)
echo [%TIMESTAMP%] 📰 执行新闻爬取... >> %LOG_FILE%
python master_pipeline.py all >> %LOG_FILE% 2>&1

if errorlevel 1 (
    echo [%TIMESTAMP%] ❌ 新闻爬取失败 >> %LOG_FILE%
    exit /b 1
) else (
    echo [%TIMESTAMP%] ✅ 新闻爬取和向量化完成 >> %LOG_FILE%
    
    REM 检查生成的文件
    for /f %%i in ('dir /b news_output\*.json 2^>nul ^| find /c /v ""') do set NEWS_COUNT=%%i
    echo [%TIMESTAMP%] 📊 news_output目录文件数: !NEWS_COUNT! >> %LOG_FILE%
    
    exit /b 0
)
