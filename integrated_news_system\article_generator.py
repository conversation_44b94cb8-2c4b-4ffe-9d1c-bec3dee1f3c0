#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文章生成器模块
基于话题分析和相关新闻生成完整的深度分析文章
"""

import json
import re
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from llm_client import LL<PERSON>lient
from config import get_system_config


class ArticleGenerator:
    """文章生成器"""
    
    def __init__(self):
        """初始化文章生成器"""
        self.llm_client = LLMClient()
        self.config = get_system_config()
        
        # 文章结构配置
        self.article_sections = [
            "引言",
            "背景分析", 
            "关键要点",
            "深度解读",
            "总结展望"
        ]
        
        print("✅ 文章生成器初始化完成")
    
    def generate_article(self, topic_analysis: Dict[str, Any],
                        news_search_results: Dict[str, Any], max_retries: int = 5) -> Dict[str, Any]:
        """
        生成完整文章（增强版重试机制）

        Args:
            topic_analysis: 话题分析结果
            news_search_results: 新闻搜索结果
            max_retries: 最大重试次数

        Returns:
            生成的文章数据
        """
        topic_title = topic_analysis.get('original_topic', {}).get('merged_title', '未知话题')
        print(f"📝 开始生成文章: {topic_title}")
        
        try:
            # 1. 准备文章生成的上下文信息
            context = self._prepare_context(topic_analysis, news_search_results)
            
            # 2. 并发生成标题和内容
            title_and_content = self._generate_title_and_content_concurrent(topic_analysis, context, max_retries)
            article_title = title_and_content['title']
            article_content = title_and_content['content']

            # 注意：不生成摘要，自媒体发布只需要标题和内容
            
            # 4. 组装完整文章数据（只包含标题和内容，适合自媒体发布）
            article_data = {
                'title': article_title,
                'content': article_content,
                'category': topic_analysis.get('original_topic', {}).get('category', '其他'),
                'keywords': topic_analysis.get('keywords', []),
                'source_topics': topic_analysis.get('original_topic', {}),
                'related_news': self._extract_news_summary(news_search_results),
                'importance_score': topic_analysis.get('original_topic', {}).get('importance_score', 5),
                'word_count': len(article_content),
                'generation_time': datetime.now().isoformat(),
                'status': 'draft'
            }
            
            print(f"✅ 文章生成完成:")
            print(f"   📰 标题: {article_title}")
            print(f"   📊 字数: {article_data['word_count']}")
            print(f"   🏷️  分类: {article_data['category']}")
            print(f"   ⭐ 重要性: {article_data['importance_score']}/10")
            print(f"   📰 相关新闻: {len(context['related_news'])} 条完整内容")
            
            return article_data
            
        except Exception as e:
            print(f"❌ 文章生成失败: {e}")
            return {
                'error': str(e),
                'topic_title': topic_title,
                'generation_time': datetime.now().isoformat()
            }

    def generate_articles_concurrent(self, tasks: List[Dict], max_workers: int = 5) -> List[Dict]:
        """
        并发生成多篇文章

        Args:
            tasks: 任务列表，每个任务包含 {'topic': ..., 'search_result': ..., 'index': ...}
            max_workers: 最大并发线程数

        Returns:
            生成的文章列表
        """
        articles = []

        def generate_single_article(task):
            """单个文章生成任务"""
            try:
                topic = task['topic']
                search_result = task['search_result']
                index = task['index']

                print(f"📝 线程 {index+1}: 开始生成文章...")
                article = self.generate_article(topic, search_result)

                if 'error' not in article:
                    print(f"✅ 线程 {index+1}: 文章生成完成")
                    return article
                else:
                    print(f"❌ 线程 {index+1}: 文章生成失败")
                    return None

            except Exception as e:
                print(f"❌ 线程 {index+1}: 生成异常 - {e}")
                return None

        # 使用线程池并发执行
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_task = {executor.submit(generate_single_article, task): task for task in tasks}

            # 收集结果
            completed_count = 0
            successful_count = 0
            for future in as_completed(future_to_task):
                completed_count += 1

                try:
                    article = future.result()
                    if article:
                        articles.append(article)
                        successful_count += 1
                        print(f"📊 文章生成进度: {completed_count}/{len(tasks)} 完成 (成功: {successful_count})")
                    else:
                        print(f"📊 文章生成进度: {completed_count}/{len(tasks)} 完成 (成功: {successful_count})")
                except Exception as e:
                    print(f"❌ 获取结果失败: {e}")
                    print(f"📊 文章生成进度: {completed_count}/{len(tasks)} 完成 (成功: {successful_count})")

        print(f"🎉 文章生成完成: ✅ 成功 {successful_count} 篇 ❌ 失败 {len(tasks)-successful_count} 篇 📈 成功率: {successful_count/len(tasks)*100:.1f}%")
        return articles

    def _generate_title_and_content_concurrent(self, topic_analysis: Dict[str, Any],
                                             context: Dict[str, Any], max_retries: int = 5) -> Dict[str, str]:
        """
        并发生成标题和内容

        Args:
            topic_analysis: 话题分析结果
            context: 上下文信息
            max_retries: 最大重试次数

        Returns:
            包含title和content的字典
        """
        print("   📝 并发生成标题和内容...")

        # 准备标题生成请求 - 虎扑风格强钩子标题
        title_system_prompt = """你是虎扑体育的资深编辑，擅长创作具有强烈吸引力的文章标题。

## 🎯 标题生成策略（10-18字）

### 强钩子优先
标题必须有吸引力，让人忍不想点开！

### 高冲击力钩子技巧
- **引用式**："XXX：下赛季我罩着你"
- **反转式**："手术成功！哈利伯顿：即使跟腱撕裂也不后悔"
- **悬念式**："这个决定，改变了整个联盟格局"
- **对比式**："从替补到核心，他只用了一个赛季"
- **冲突式**："球迷怒了：这笔交易简直是抢劫"
- **年龄焦虑式**："他35岁还一事无成..."
- **努力无果式**："为什么你努力了这么久，还是比不过别人？"
- **身份反差式**："从天才少年到边缘人，只用了3年"
- **时间节点式**："最后30秒，他做了一个影响生涯的决定"
- **命运转折式**："一场伤病，彻底改变了他的人生轨迹"
- **残酷现实式**："努力10年，不如天赋3秒"
- **扎心拷问式**："30岁了，你还在做梦吗？"
- **现实打脸式**："说出来你可能不信..."
- **共鸣痛点式**："这种痛，只有失败者才懂"
- **直击要害式**："你以为努力就够了？"

### 示例实操钩子（可替换关键词）
- **条件判断式**："这三种习惯你占一个，晚年可能会过得很难"
- **时间无果式**："为什么你工作10年，还买不起房？很多人都忽略了这几点"
- **隐藏危机式**："看似普通的一种病，背后竟藏着致命风险"
- **坚持奇迹式**："每天坚持做这件小事，一个月后我惊呆了"
- **方法颠覆式**："别再傻傻节食了，真正让你瘦下来的，其实是……"
- **认知颠覆式**："你以为是运气，其实背后藏着这个秘密"
- **错误提醒式**："90%的人都在犯这个错误，难怪..."
- **真相揭秘式**："他成功的真正原因，不是你想的那样"
- **后果警告式**："再这样下去，你可能会后悔一辈子"
- **内幕爆料式**："业内人士不会告诉你的真相"

### 心理触发技巧
- **制造好奇缺口**：用"..."、"？"留下悬念
- **情感共鸣点**：年龄焦虑、努力无果、天赋浪费
- **戏剧张力**：逆转、反差、意外、争议
- **时间紧迫感**：关键时刻、最后机会、转折点

### 技巧要点
- 适当使用"震撼"、"意外"、"逆转"、"爆发"等情感词汇
- 用具体数字和年龄增强冲击力
- 制造"这是怎么回事？"的强烈好奇心
- 触及读者内心痛点（年龄、努力、天赋等）
- 让人看到标题就忍不住想点开

🔥 **核心要求**：标题是文章成功的关键！必须有强烈的钩子！

只输出标题，不要其他内容。"""

        title_user_prompt = f"""请为以下话题生成一个具有强烈吸引力的虎扑风格标题：

**核心事件**：{context['topic_info'].get('merged_title', '')}
**分类**：{context['topic_info'].get('category', '')}
**关键词**：{', '.join(context['keywords'])}
**背景信息**：{context['background_context'][:300]}

**要求**：
- 标题长度：10-18字
- 必须有强钩子，让人忍不住想点开
- 优先考虑高冲击力技巧：年龄悬念式、疑问痛点式、身份反差式、时间节点式、命运转折式
- 触及读者内心痛点：年龄焦虑、努力无果、天赋浪费、命运转折
- 制造强烈的"这是怎么回事？"好奇心
- 适当使用"..."或"？"增强悬念感

请生成标题："""

        # 准备内容生成请求（先用临时标题）
        temp_title = context['topic_info'].get('merged_title', '热点分析')
        content_system_prompt, content_user_prompt = self._prepare_content_prompts(topic_analysis, context, temp_title)

        # 并发请求 - 虎扑风格参数设置
        requests = [
            {
                'prompt': title_user_prompt,
                'system_prompt': title_system_prompt,
                'max_tokens': 16000,     # 标题生成
                'temperature': 0.3       # 虎扑风格：较低温度保证质量
            },
            {
                'prompt': content_user_prompt,
                'system_prompt': content_system_prompt,
                'max_tokens': 16000,     # 文章内容生成
                'temperature': 0.3       # 虎扑风格：较低温度保证客观性
            }
        ]

        # 执行并发请求
        results = self.llm_client.batch_chat(requests, max_workers=2)

        # 处理结果
        title = temp_title  # 默认标题
        content = self._generate_fallback_content(topic_analysis, context, temp_title)  # 默认内容

        if len(results) >= 2:
            # 处理标题结果
            if results[0].get('success') and results[0].get('response'):
                title = results[0]['response'].strip().strip('"').strip("'")
                print("✅ 标题生成成功")
            else:
                print("⚠️ 标题生成失败，使用默认标题")

            # 处理内容结果
            if results[1].get('success') and results[1].get('response'):
                content = results[1]['response'].strip()
                if len(content) >= 200:
                    print("✅ 内容生成成功")
                else:
                    print("⚠️ 内容太短，使用备用方案")
                    content = self._generate_fallback_content(topic_analysis, context, title)
            else:
                print("⚠️ 内容生成失败，使用备用方案")
                content = self._generate_fallback_content(topic_analysis, context, title)

        return {
            'title': title,
            'content': content
        }

    def _prepare_content_prompts(self, topic_analysis: Dict[str, Any],
                               context: Dict[str, Any], title: str) -> tuple:
        """准备内容生成的提示词"""
        system_prompt = """你是虎扑体育的资深编辑，擅长写出客观、流畅、有深度的新闻文章。

## 🚨 核心写作纪律
**本文必须且只能围绕核心事件展开。** 严禁发散到其他不相关的人物或事件上。你的任务是把这一个事件讲深、讲透，而不是做一个新闻盘点。从第一个字到最后一个字，都必须服务于这个核心事件。

## 虎扑风格写作要求

### 1. 文章定位
- **客观叙述**：以第三人称视角，客观描述事件和人物
- **深度挖掘**：不只是表面新闻，要挖掘背后的故事和细节
- **通俗易懂**：基于事实进行分析，但语言要让普通读者也能看懂

### 2. 文章结构（总计800-900字）

**开头段（120-150字）- 场景描述**
- 用具体的时间、地点、人物开场
- 描述一个具体的场景或事件
- 可以用有趣的细节或对比作为钩子，让读者想继续看下去
- 语言自然一些，不要太正式

**主体部分（550-650字）- 层层展开**
- **🚨 绝对不要用（1）（2）（3）分点！**
- 按时间线或逻辑线自然展开
- **🔥 段落字数严格控制要求（必须遵守！）**：
  * **正文段落：严格控制在50-120字之间，绝不超过120字！**
  * **过渡段：严格控制在30-60字之间**
  * **段首句：20-40字，用于设悬念、提出观点**
  * **每段3-5行，适合手机屏幕阅读**
  * **超过120字的段落必须拆分成2-3个短段落**
- 多用具体的细节和数据，说话要有根据
- 引用当事人的话语和行为，让读者感受到现场感
- 挖掘事件背后的原因，告诉读者"为什么会这样"
- 可以用对比、反转等手法，让文章更有意思

**结尾段（100-130字）- 总结收尾**
- 总结事件的意义或影响，和开头呼应一下
- 可以说说这事儿可能带来什么影响
- 结尾可以问个问题，让读者思考一下
- 语调自然一些，不要太煽情

### 3. 写作技巧

**客观叙述**：
- 以第三人称视角描述，不要说"我觉得"、"我认为"
- 用事实说话，让读者自己判断
- 保持中性立场，不要带太多个人情感

**细节刻画**：
- 用具体的时间、地点、数据让文章更真实
- 描述人物的具体行为和表情，让读者有画面感
- 引用原话时要准确，这样更有说服力

**逻辑连贯**：
- 段落之间要自然过渡，不要突然跳转
- 按照时间顺序或因果关系来写
- 每个段落都要围绕主题，不要跑偏

### 4. 语言风格
- **自然朴实**：语言自然一些，不要太华丽，也不要太煽情，别说废话
- **准确可信**：该用的专业词汇要用准，数据要真实
- **好读易懂**：句子简洁有力，逻辑清楚，适合手机上看
- **有点深度**：不只是表面信息，要分析一下为什么，给读者一些思考

### 5. 🚨 绝对禁止（重要！）
- ❌ **使用（1）（2）（3）或第一、第二、第三等分点**
- ❌ **使用第一人称"我"、"我们"等**
- ❌ **过度煽情或主观评判**
- ❌ **使用过于网络化的流行语**
- ❌ **夸大事实或添加不实信息**
- ❌ **写出超过120字的段落（必须严格控制！）**
- ❌ **段落过长导致手机阅读困难**

### 6. 虎扑风格检查清单
✅ 开头用具体场景描述，让读者有画面感
✅ 全文第三人称客观叙述，不说废话
✅ **正文段落严格控制在50-120字，绝不超过120字！**
✅ **过渡段严格控制在30-60字，用于话题转换**
✅ **起承转合段首句20-40字，设悬念、提出观点**
✅ 结尾段100-130字，呼应开头，可以问个问题让读者思考
✅ **每段3-5行，完美适配手机屏幕阅读**
✅ 多用具体细节和准确数据
✅ 逻辑清楚，段落间自然过渡
✅ 语言自然易懂，适当用一些叙述技巧让文章更有意思

### 7. 📱 段落字数检查（写完后必须自检！）
**写完每个段落后，立即检查字数：**
- 正文段落：数一下字数，必须在50-120字之间
- 如果超过120字，立即拆分成2个段落
- 如果少于50字，适当补充细节或合并到其他段落
- 确保每段在手机上显示为3-5行

**🎯 最终要求**：写出一篇像虎扑编辑写的文章，客观、有深度、有料，但语言要自然一些！

**🔥 段落字数控制最终提醒（必须严格执行！）**：
- **每个正文段落必须控制在50-120字之间**
- **绝对不允许出现超过120字的段落**
- **写完后请自检每段字数，确保符合手机阅读习惯**
- **长段落必须拆分，短段落适当补充**

**重要提醒**：
- 充分利用提供的完整新闻内容，不要只是概括
- 确保文章内容丰富、观点明确、分析深入
- 只输出文章正文内容，不要添加标题或其他格式"""

        # 构建详细的用户提示词 - 传递完整新闻内容
        news_content = ""
        if context['related_news']:
            news_content = "\n\n=== 相关新闻完整内容参考 ===\n"
            for i, news in enumerate(context['related_news'][:5], 1):
                content_text = news['content'].strip()
                if content_text:
                    news_content += f"\n【新闻{i}】{news['title']}\n"
                    news_content += f"来源：{news['source']}\n"
                    news_content += f"内容：{content_text}\n"
                    news_content += f"相似度：{news.get('similarity', 0):.2f}\n"
                    news_content += "-" * 50 + "\n"
            news_content += "\n=== 新闻参考结束 ===\n"

        user_prompt = f"""请基于以下新闻要点，围绕事件"{context['topic_info'].get('merged_title', '')}"写一篇虎扑风格的深度文章。

**核心事件**: {context['topic_info'].get('merged_title', '')}

**新闻要点 (已确认全部与核心事件相关)**:

**背景信息**：
{context['background_context']}

**关键要点**：
{chr(10).join(f"• {point}" for point in context['key_points'])}

**相关实体**：
- 人物：{', '.join(context['related_entities'].get('persons', []))}
- 机构：{', '.join(context['related_entities'].get('organizations', []))}
- 地点：{', '.join(context['related_entities'].get('locations', []))}

{news_content}

**🚨 重要提醒**：
- 本文必须且只能围绕"{context['topic_info'].get('merged_title', '')}"这一个核心事件展开
- 严禁发散到其他不相关的人物或事件上
- 要把这一个事件讲深、讲透，而不是做新闻盘点
- 从第一个字到最后一个字，都必须服务于这个核心事件

**📱 段落字数严格要求（必须遵守！）**：
- **每个正文段落必须严格控制在50-120字之间**
- **绝对不允许写出超过120字的段落**
- **每段3-5行，适合手机屏幕阅读**
- **写完每段立即检查字数，超过120字必须拆分**

请创作文章："""

        return system_prompt, user_prompt

    def _prepare_context(self, topic_analysis: Dict[str, Any],
                        news_search_results: Dict[str, Any]) -> Dict[str, Any]:
        """准备文章生成的上下文信息"""
        
        # 提取相关新闻的关键信息
        news_items = news_search_results.get('news_items', [])
        news_summaries = []
        
        for news in news_items[:5]:  # 只取前5条最相关的新闻，但保留完整内容
            metadata = news.get('metadata', {})
            full_content = metadata.get('content', '')
            news_summaries.append({
                'title': metadata.get('title', ''),
                'content': full_content,  # 保留完整新闻内容
                'source': metadata.get('source', ''),
                'similarity': news.get('similarity', 0),
                'publish_time': metadata.get('publish_time', '')
            })
        
        context = {
            'topic_info': topic_analysis.get('original_topic', {}),
            'keywords': topic_analysis.get('keywords', []),
            'background_context': topic_analysis.get('background_context', ''),
            'key_points': topic_analysis.get('key_points', []),
            'related_entities': topic_analysis.get('related_entities', {}),
            'related_news': news_summaries,
            'news_count': len(news_items)
        }
        
        return context
    
    def _generate_title(self, topic_analysis: Dict[str, Any], context: Dict[str, Any], max_retries: int = 5) -> str:
        """生成文章标题"""
        print("   📝 生成文章标题...")
        
        system_prompt = """你是一个专业的新闻编辑。请为给定的话题生成一个吸引人且准确的文章标题。

要求：
1. 标题要准确反映话题的核心内容
2. 具有新闻价值和吸引力
3. 长度控制在15-30个字符
4. 避免夸张和误导性表述
5. 体现深度分析的特点

请只输出标题，不要其他内容。"""

        topic_info = context['topic_info']
        user_prompt = f"""请为以下话题生成一个专业的文章标题：

话题：{topic_info.get('merged_title', '')}
分类：{topic_info.get('category', '')}
关键词：{', '.join(context['keywords'])}
背景：{context['background_context']}

相关新闻数量：{context['news_count']} 条

请生成一个准确、吸引人的文章标题："""

        # 使用重试机制生成标题
        fallback_title = f"{topic_info.get('merged_title', '热点话题')}引关注"
        return self._generate_with_retry(
            "标题",
            user_prompt,
            system_prompt,
            max_tokens=16000,    # 标题生成
            temperature=0.3,     # 虎扑风格：较低温度
            fallback=fallback_title,
            max_retries=max_retries
        )
    
    def _generate_content(self, topic_analysis: Dict[str, Any],
                         context: Dict[str, Any], title: str, max_retries: int = 5) -> str:
        """生成文章内容"""
        print("   📝 生成文章内容...")
        
        system_prompt = """你是一个资深的新闻分析师和专业作者。请基于提供的深度分析报告和完整的相关新闻内容，撰写一篇适合自媒体发布的深度分析文章。

文章要求：
1. 结构清晰，逻辑严密，适合手机阅读
2. 内容客观、准确、有深度，充分利用提供的完整新闻内容
3. 语言专业但易懂，杜绝废话，句子简洁有力
4. 字数控制在800-900字
5. 段落字数要求：
   - 正文段落：50-120字，3-5行
   - 过渡段：30-60字
   - 起承转合段首句：20-40字
   - 结尾段：100-130字，可加问句提升互动
6. 包含以下结构：
   - 引言：简要介绍话题背景和重要性
   - 背景分析：基于提供的新闻内容详细分析事件来龙去脉
   - 关键要点：结合新闻内容列出并分析核心要点
   - 深度解读：基于新闻事实提供专业见解和分析
   - 总结展望：总结观点并展望未来

重要提醒：
- 充分利用提供的完整新闻内容，不要只是概括
- 确保文章内容丰富、观点明确、分析深入
- 只输出文章正文内容，不要添加标题或其他格式"""

        # 构建详细的用户提示词 - 传递完整新闻内容
        news_content = ""
        if context['related_news']:
            news_content = "\n\n=== 相关新闻完整内容参考 ===\n"
            for i, news in enumerate(context['related_news'][:5], 1):
                content = news['content'].strip()
                if content:
                    news_content += f"\n【新闻{i}】{news['title']}\n"
                    news_content += f"来源：{news['source']}\n"
                    news_content += f"内容：{content}\n"
                    news_content += f"相似度：{news.get('similarity', 0):.2f}\n"
                    news_content += "-" * 50 + "\n"
            news_content += "\n=== 新闻参考结束 ===\n"

        user_prompt = f"""请基于以下信息撰写一篇深度分析文章：

文章标题：{title}

话题信息：
- 主题：{context['topic_info'].get('merged_title', '')}
- 分类：{context['topic_info'].get('category', '')}
- 重要性评分：{context['topic_info'].get('importance_score', 5)}/10

关键词：{', '.join(context['keywords'])}

背景信息：
{context['background_context']}

关键要点：
{chr(10).join(f"- {point}" for point in context['key_points'])}

相关实体：
- 人物：{', '.join(context['related_entities'].get('people', []))}
- 机构：{', '.join(context['related_entities'].get('organizations', []))}
- 地点：{', '.join(context['related_entities'].get('locations', []))}
- 其他：{', '.join(context['related_entities'].get('others', []))}

{news_content}

请撰写一篇结构完整、内容深入的分析文章："""

        # 使用重试机制生成内容
        fallback_content = self._generate_fallback_content(topic_analysis, context, title)
        content = self._generate_with_retry(
            "内容",
            user_prompt,
            system_prompt,
            max_tokens=16000,    # 文章内容生成
            temperature=0.3,     # 虎扑风格：较低温度保证客观性
            fallback=fallback_content,
            max_retries=max_retries
        )

        # 检查内容长度
        if len(content) < 200:
            print("⚠️ 生成的内容太短，使用备用方案")
            return fallback_content

        return content
    
    def _generate_fallback_content(self, topic_analysis: Dict[str, Any],
                                  context: Dict[str, Any], title: str) -> str:
        """生成备用文章内容 - 虎扑风格"""
        print("   🔄 使用虎扑风格备用内容生成方案...")

        topic_info = context['topic_info']
        topic_title = topic_info.get('merged_title', '')

        # 虎扑风格的备用内容，不使用分点格式
        content_parts = [
            # 开头段 - 场景描述
            f"近日，{topic_title}成为了热议话题。这一事件的发生，让人们再次将目光聚焦到相关领域。",
            f"从目前掌握的信息来看，该事件涉及多个层面的问题，值得深入分析。",
            "",

            # 背景分析
            context.get('background_context', f'{topic_title}的相关情况正在持续发展中。'),
            "",

            # 关键信息展开 - 不使用分点
            "从各方面的信息汇总来看，",
        ]

        # 将关键要点自然融入段落，不使用分点
        if context['key_points']:
            key_points_text = "、".join(context['key_points'][:3])  # 取前3个要点
            content_parts.append(f"此次事件主要涉及{key_points_text}等方面。")

        content_parts.extend([
            f"这些因素的相互作用，使得{topic_title}具有了更深层次的意义。",
            "",

            # 深度分析
            f"值得注意的是，{topic_title}并非孤立事件。",
            f"从更广的视角来看，它反映了当前{topic_info.get('category', '相关领域')}的一些深层次问题。",
            "",

            # 相关实体信息
        ])

        # 添加实体信息（如果有的话）
        entities = context.get('related_entities', {})
        if entities.get('persons'):
            content_parts.append(f"在这一过程中，{entities['persons'][0]}等人物的表现尤为引人关注。")

        content_parts.extend([
            "",
            # 结尾段 - 总结展望
            f"总的来说，{topic_title}的发展仍在继续。",
            "从目前的情况来看，相关各方都在积极应对，事态的进一步发展值得持续关注。",
            f"无论最终结果如何，这一事件都将对{topic_info.get('category', '相关领域')}产生一定的影响。"
        ])

        return "\n".join(content_parts)
    
    def _generate_summary(self, content: str, topic_analysis: Dict[str, Any], max_retries: int = 5) -> str:
        """生成文章摘要"""
        print("   📝 生成文章摘要...")
        
        system_prompt = """你是一个专业的编辑。请为给定的文章生成一个简洁准确的摘要。

要求：
1. 摘要长度控制在100-200字
2. 准确概括文章的核心内容
3. 突出关键信息和观点
4. 语言简洁明了

请只输出摘要内容，不要其他文字。"""

        user_prompt = f"""请为以下文章生成摘要：

文章内容：
{content[:1000]}...

话题关键词：{', '.join(topic_analysis.get('keywords', []))}

请生成文章摘要："""

        # 使用重试机制生成摘要
        topic_title = topic_analysis.get('original_topic', {}).get('merged_title', '热点话题')
        fallback_summary = f"本文深入分析了{topic_title}的相关情况，从多个角度探讨了其背景、要点和影响，为读者提供了全面的解读。"

        return self._generate_with_retry(
            "摘要",
            user_prompt,
            system_prompt,
            max_tokens=16000,
            temperature=0.5,
            fallback=fallback_summary,
            max_retries=max_retries
        )
    
    def _extract_news_summary(self, news_search_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取新闻摘要信息"""
        news_items = news_search_results.get('news_items', [])
        
        summaries = []
        for news in news_items[:5]:  # 只保留前5条最相关的
            metadata = news.get('metadata', {})
            summaries.append({
                'title': metadata.get('title', ''),
                'source': metadata.get('source', ''),
                'similarity': news.get('similarity', 0),
                'publish_time': metadata.get('publish_time', '')
            })
        
        return summaries

    def _generate_with_retry(self, task_name: str, user_prompt: str, system_prompt: str,
                           max_tokens: int, temperature: float, fallback: str, max_retries: int = 5) -> str:
        """通用的带重试机制的生成方法"""

        for retry_round in range(max_retries):
            try:
                print(f"🔄 第 {retry_round + 1} 次尝试生成{task_name}...")

                response = self.llm_client.chat(
                    prompt=user_prompt,
                    system_prompt=system_prompt,
                    max_tokens=max_tokens,
                    temperature=temperature
                )

                if response and response.strip():
                    # 清理响应
                    cleaned_response = response.strip().strip('"').strip("'")
                    if cleaned_response:
                        print(f"✅ {task_name}生成成功")
                        return cleaned_response

                print(f"⚠️ 第 {retry_round + 1} 次{task_name}生成为空，准备重试...")

            except Exception as e:
                print(f"❌ 第 {retry_round + 1} 次{task_name}生成失败: {e}")

            # 如果还有重试机会，等待一下
            if retry_round < max_retries - 1:
                import time
                wait_time = 2 ** retry_round
                print(f"⏳ 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)

        print(f"❌ 所有重试都失败，使用备用{task_name}")
        return fallback


if __name__ == "__main__":
    # 测试文章生成器
    test_topic_analysis = {
        'original_topic': {
            'merged_title': '人工智能技术发展',
            'category': '科技',
            'importance_score': 8
        },
        'keywords': ['人工智能', 'AI', '技术发展'],
        'background_context': '人工智能技术在近年来取得了重大突破',
        'key_points': ['技术突破', '应用场景扩展', '行业影响'],
        'related_entities': {
            'people': ['研究人员'],
            'organizations': ['科技公司'],
            'locations': ['全球'],
            'others': ['算法']
        }
    }
    
    test_news_results = {
        'news_items': [
            {
                'metadata': {
                    'title': '人工智能新突破',
                    'content': '最新的人工智能技术取得重大进展...',
                    'source': '科技日报'
                },
                'similarity': 0.85
            }
        ]
    }
    
    generator = ArticleGenerator()
    article = generator.generate_article(test_topic_analysis, test_news_results)
    
    print(f"\n生成的文章：")
    print(f"标题: {article.get('title', '')}")
    print(f"摘要: {article.get('summary', '')}")
    print(f"字数: {article.get('word_count', 0)}")
