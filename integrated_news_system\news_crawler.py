#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻爬虫脚本 - 支持多个主流平台的热点新闻抓取
集成版本，专注于核心功能
"""

import json
import re
import time
from typing import Dict, List, Optional, Any
from urllib.parse import quote, urlencode
import requests
from bs4 import BeautifulSoup
import feedparser
from datetime import datetime
from pathlib import Path

# 导入配置
try:
    from crawler_config import PLATFORM_LIMITS, NETWORK_CONFIG, PROXY_CONFIG, OUTPUT_CONFIG, PLATFORM_ENABLED
except ImportError:
    # 如果没有配置文件，使用默认配置
    PLATFORM_LIMITS = {'zhihu': 50, 'bilibili': 50}
    NETWORK_CONFIG = {'timeout': 10, 'max_retries': 3, 'retry_delay': 1, 'request_interval': 1}
    PROXY_CONFIG = {'enabled': False}
    OUTPUT_CONFIG = {'save_to_file': True, 'show_summary': True, 'summary_preview_count': 5}
    PLATFORM_ENABLED = {'weibo': True, 'baidu': True, 'zhihu': True, 'toutiao': True, 'bilibili': True}


class NewsCrawler:
    """新闻爬虫主类"""
    
    def __init__(self, proxy: Optional[str] = None, timeout: int = None):
        """
        初始化爬虫

        Args:
            proxy: 代理地址，格式如 "http://127.0.0.1:7890"
            timeout: 请求超时时间（秒）
        """
        self.session = requests.Session()
        self.timeout = timeout or NETWORK_CONFIG['timeout']
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
        })
        
        # 设置代理
        if proxy:
            self.session.proxies = {
                'http': proxy,
                'https': proxy
            }
    
    def _fetch_with_retry(self, url: str, max_retries: int = 3, **kwargs) -> requests.Response:
        """带重试的请求方法"""
        for attempt in range(max_retries + 1):
            try:
                response = self.session.get(url, timeout=self.timeout, **kwargs)
                response.raise_for_status()
                return response
            except Exception as e:
                if attempt == max_retries:
                    raise e
                print(f"请求失败，正在重试 ({attempt + 1}/{max_retries}): {e}")
                time.sleep(2 ** attempt)  # 指数退避
    
    def weibo_hot_search(self) -> List[Dict[str, Any]]:
        """微博热搜"""
        try:
            url = "https://weibo.com/ajax/side/hotSearch"
            response = self._fetch_with_retry(url)
            data = response.json()

            results = []
            for item in data.get('data', {}).get('realtime', []):
                if item.get('is_ad'):  # 跳过广告
                    continue

                keyword = item.get('word_scheme') or f"#{item.get('word')}#"
                results.append({
                    'id': item.get('word'),
                    'title': item.get('word'),
                    'url': f"https://s.weibo.com/weibo?q={quote(keyword)}",
                    'mobile_url': f"https://m.weibo.cn/search?containerid=231522type%3D1%26q%3D{quote(keyword)}&_T_WM=16922097837&v_p=42",
                    'extra': {
                        'icon': item.get('icon'),
                        'rank': item.get('rank')
                    }
                })

            print(f"✅ 微博热搜: 获取到 {len(results)} 条数据")
            return results

        except Exception as e:
            print(f"❌ 微博热搜获取失败: {e}")
            return []
    
    def baidu_hot_search(self) -> List[Dict[str, Any]]:
        """百度热搜"""
        try:
            url = "https://top.baidu.com/board?tab=realtime"
            response = self._fetch_with_retry(url)

            # 从HTML中提取JSON数据
            html_content = response.text
            json_match = re.search(r'<!--s-data:(.*?)-->', html_content, re.DOTALL)
            if not json_match:
                raise ValueError("无法从页面中提取数据")

            data = json.loads(json_match.group(1))

            results = []
            content_list = data.get('data', {}).get('cards', [{}])[0].get('content', [])

            for item in content_list:
                if item.get('isTop'):  # 跳过置顶
                    continue

                results.append({
                    'id': item.get('rawUrl'),
                    'title': item.get('word'),
                    'url': item.get('rawUrl'),
                    'extra': {
                        'hover': item.get('desc')
                    }
                })

            print(f"✅ 百度热搜: 获取到 {len(results)} 条数据")
            return results

        except Exception as e:
            print(f"❌ 百度热搜获取失败: {e}")
            return []
    
    def zhihu_hot_list(self) -> List[Dict[str, Any]]:
        """知乎热榜"""
        try:
            limit = PLATFORM_LIMITS.get('zhihu', 20)
            url = f"https://www.zhihu.com/api/v3/feed/topstory/hot-list-web?limit={limit}&desktop=true"
            response = self._fetch_with_retry(url)
            data = response.json()

            results = []
            for item in data.get('data', []):
                target = item.get('target', {})
                link = target.get('link', {})

                # 提取ID
                url_match = re.search(r'(\d+)$', link.get('url', ''))
                item_id = url_match.group(1) if url_match else link.get('url')

                results.append({
                    'id': item_id,
                    'title': target.get('title_area', {}).get('text'),
                    'url': link.get('url'),
                    'extra': {
                        'info': target.get('metrics_area', {}).get('text'),
                        'hover': target.get('excerpt_area', {}).get('text')
                    }
                })

            print(f"✅ 知乎热榜: 获取到 {len(results)} 条数据")
            return results

        except Exception as e:
            print(f"❌ 知乎热榜获取失败: {e}")
            return []
    
    def toutiao_hot(self) -> List[Dict[str, Any]]:
        """今日头条热榜"""
        try:
            url = "https://www.toutiao.com/hot-event/hot-board/?origin=toutiao_pc"
            response = self._fetch_with_retry(url)
            data = response.json()

            results = []
            for item in data.get('data', []):
                results.append({
                    'id': item.get('ClusterIdStr'),
                    'title': item.get('Title'),
                    'url': f"https://www.toutiao.com/trending/{item.get('ClusterIdStr')}/",
                    'extra': {
                        'hot_value': item.get('HotValue'),
                        'icon': item.get('LabelUri', {}).get('url') if item.get('LabelUri') else None
                    }
                })

            print(f"✅ 今日头条: 获取到 {len(results)} 条数据")
            return results

        except Exception as e:
            print(f"❌ 今日头条获取失败: {e}")
            return []
    
    def bilibili_hot_search(self) -> List[Dict[str, Any]]:
        """B站热搜"""
        try:
            limit = PLATFORM_LIMITS.get('bilibili', 30)
            url = f"https://s.search.bilibili.com/main/hotword?limit={limit}"
            response = self._fetch_with_retry(url)
            data = response.json()

            results = []
            for item in data.get('list', []):
                results.append({
                    'id': item.get('keyword'),
                    'title': item.get('show_name'),
                    'url': f"https://search.bilibili.com/all?keyword={quote(item.get('keyword', ''))}",
                    'extra': {
                        'icon': item.get('icon'),
                        'score': item.get('score')
                    }
                })

            print(f"✅ B站热搜: 获取到 {len(results)} 条数据")
            return results

        except Exception as e:
            print(f"❌ B站热搜获取失败: {e}")
            return []







    def get_all_news(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有平台的新闻"""
        print("🚀 开始抓取新闻数据...")
        print("=" * 50)

        # 使用与原始代码相同的平台配置
        all_sources = {
            'weibo': self.weibo_hot_search,
            'baidu': self.baidu_hot_search,
            'zhihu': self.zhihu_hot_list,
            'toutiao': self.toutiao_hot,
            'bilibili': self.bilibili_hot_search,
        }

        # 根据配置过滤启用的平台
        sources = {name: func for name, func in all_sources.items()
                  if PLATFORM_ENABLED.get(name, True)}

        platform_names = {
            'weibo': '微博热搜',
            'baidu': '百度热搜',
            'zhihu': '知乎热榜',
            'toutiao': '今日头条',
            'bilibili': 'B站热搜'
        }

        results = {}
        total_count = 0

        for source_name, source_func in sources.items():
            try:
                platform_name = platform_names.get(source_name, source_name.upper())
                print(f"📰 正在获取 {platform_name}...")

                news_list = source_func()
                results[source_name] = news_list
                count = len(news_list)
                total_count += count

                if count > 0:
                    print(f"✅ {platform_name}: 获取到 {count} 条数据")
                else:
                    print(f"⚠️ {platform_name}: 获取到 0 条数据")

                # 请求间隔，避免请求过快
                time.sleep(NETWORK_CONFIG.get('request_interval', 1))

            except Exception as e:
                print(f"❌ {platform_names.get(source_name, source_name)}: 获取失败 - {e}")
                results[source_name] = []

        print("=" * 50)
        print(f"📊 抓取完成! 总计获取 {total_count} 条新闻数据")

        return results
    
    def save_to_json(self, data: Dict[str, List[Dict[str, Any]]], filename: str = None) -> str:
        """保存数据到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"news_data_{timestamp}.json"
        
        # 确保输出目录存在
        output_dir = Path("news_output")
        output_dir.mkdir(exist_ok=True)
        
        filepath = output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 数据已保存到: {filepath}")
        return str(filepath)


def main():
    """主函数"""
    crawler = NewsCrawler()
    news_data = crawler.get_all_news()
    
    # 保存数据
    output_file = crawler.save_to_json(news_data)
    
    # 显示摘要
    print(f"\n📋 数据摘要:")
    for platform, items in news_data.items():
        if items:
            print(f"  {platform}: {len(items)} 条")
            # 显示前3条标题
            for i, item in enumerate(items[:3], 1):
                print(f"    {i}. {item['title']}")
            if len(items) > 3:
                print(f"    ... 还有 {len(items) - 3} 条")


if __name__ == "__main__":
    main()
