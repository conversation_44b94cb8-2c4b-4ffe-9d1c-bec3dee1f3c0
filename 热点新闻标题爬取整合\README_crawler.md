# 新闻爬虫脚本

基于newsnow项目的Python实现，支持多个主流平台的热点新闻抓取。

## 功能特性

✅ **多平台支持** - 支持8个主流平台的新闻抓取  
✅ **智能重试** - 自动重试失败的请求，指数退避策略  
✅ **代理支持** - 支持HTTP/HTTPS代理  
✅ **数据导出** - 自动保存为JSON格式  
✅ **错误处理** - 优雅处理各种异常情况  
✅ **实时反馈** - 显示抓取进度和结果统计  

## 支持的平台

| 平台 | 数据源 | 获取方式 | 数据类型 |
|------|--------|----------|----------|
| 微博 | 热搜榜 | API调用 | 热搜关键词 |
| 百度 | 热搜榜 | HTML解析 | 热搜关键词 |
| 知乎 | 热榜 | API调用 | 热门问题 |
| 今日头条 | 热榜 | API调用 | 热门新闻 |
| B站 | 热搜 | API调用 | 热搜关键词 |
| GitHub | 趋势 | API调用 | 热门仓库 |
| V2EX | 热门 | RSS解析 | 热门帖子 |
| IT之家 | 新闻 | HTML解析 | 最新新闻 |

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```bash
python news_crawler.py
```

### 使用代理

编辑 `news_crawler.py` 文件，取消注释并设置代理：

```python
# 修改这一行
crawler = NewsCrawler(proxy="http://127.0.0.1:7890")
```

### 单独获取某个平台数据

```python
from news_crawler import NewsCrawler

crawler = NewsCrawler()

# 获取微博热搜
weibo_data = crawler.weibo_hot_search()

# 获取知乎热榜
zhihu_data = crawler.zhihu_hot_list()

# 获取GitHub趋势
github_data = crawler.github_trending()
```

## 输出格式

### 控制台输出
```
🚀 开始抓取新闻数据...
==================================================
✅ 微博热搜: 获取到 20 条数据
✅ 百度热搜: 获取到 15 条数据
✅ 知乎热榜: 获取到 20 条数据
...
==================================================
📊 抓取完成!
总计获取 XXX 条新闻数据
💾 数据已保存到: news_data_20241206_143022.json
```

### JSON数据格式
```json
{
  "weibo": [
    {
      "id": "关键词",
      "title": "热搜标题",
      "url": "https://s.weibo.com/weibo?q=...",
      "mobile_url": "https://m.weibo.cn/search?...",
      "extra": {
        "icon": "图标URL",
        "rank": 1
      }
    }
  ],
  "zhihu": [
    {
      "id": "123456",
      "title": "问题标题",
      "url": "https://www.zhihu.com/question/123456",
      "extra": {
        "info": "XXX 万热度",
        "hover": "问题描述"
      }
    }
  ]
}
```

## 配置选项

### 超时设置
```python
crawler = NewsCrawler(timeout=15)  # 设置15秒超时
```

### 重试次数
```python
# 在_fetch_with_retry方法中修改max_retries参数
response = self._fetch_with_retry(url, max_retries=5)
```

### 请求间隔
```python
# 在get_all_news方法中修改sleep时间
time.sleep(2)  # 设置2秒间隔
```

## 注意事项

1. **请求频率**: 脚本已内置请求间隔，避免过于频繁的请求
2. **网络环境**: 某些平台可能需要特定的网络环境才能访问
3. **数据时效**: 热搜数据实时变化，建议定期运行获取最新数据
4. **异常处理**: 单个平台失败不会影响其他平台的数据获取
5. **合规使用**: 请遵守各平台的robots.txt和使用条款

## 扩展开发

### 添加新的数据源

1. 在`NewsCrawler`类中添加新方法：

```python
def new_platform_hot(self) -> List[Dict[str, Any]]:
    """新平台热榜"""
    try:
        url = "https://api.newplatform.com/hot"
        response = self._fetch_with_retry(url)
        data = response.json()
        
        results = []
        for item in data.get('items', []):
            results.append({
                'id': item.get('id'),
                'title': item.get('title'),
                'url': item.get('url'),
                'extra': {}
            })
        
        print(f"✅ 新平台: 获取到 {len(results)} 条数据")
        return results
        
    except Exception as e:
        print(f"❌ 新平台获取失败: {e}")
        return []
```

2. 在`get_all_news`方法的`sources`字典中添加：

```python
sources = {
    # ... 现有平台
    'new_platform': self.new_platform_hot,
}
```

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 尝试使用代理
   - 检查防火墙设置

2. **某个平台数据获取失败**
   - 平台API可能发生变化
   - 检查平台是否可正常访问
   - 查看错误日志信息

3. **数据格式异常**
   - 平台返回数据格式可能发生变化
   - 需要更新对应的解析代码

### 调试模式

在代码中添加调试信息：

```python
import logging

logging.basicConfig(level=logging.DEBUG)
```

## 许可证

MIT License - 请遵守相关法律法规和平台使用条款。
