# 集成新闻系统依赖包
# Integrated News System Requirements

# 核心依赖
requests>=2.28.0
beautifulsoup4>=4.11.0
feedparser>=6.0.0
numpy>=1.21.0
mysql-connector-python>=8.0.0

# 核心依赖
python-dotenv>=0.19.0

# 数据处理
json5>=0.9.0

# 时间处理
python-dateutil>=2.8.0

# 注意: concurrent.futures 和 pathlib 在 Python 3.2+ 中是标准库，无需安装

# 可选依赖 (用于增强功能)
# pandas>=1.5.0          # 数据分析
# scikit-learn>=1.1.0    # 机器学习
# jieba>=0.42.1          # 中文分词
# lxml>=4.9.0            # XML解析
# selenium>=4.0.0        # 浏览器自动化 (如需要)
# fake-useragent>=1.1.0  # 随机User-Agent

# 开发依赖 (可选)
# pytest>=7.0.0         # 单元测试
# black>=22.0.0          # 代码格式化
# flake8>=5.0.0          # 代码检查
