# 🚀 集成新闻系统自动化部署指南

## 📋 部署概述

本系统采用**双流水线**架构，通过定时任务实现自动化运行：

### 🔄 **流水线1：新闻爬取和向量化** (每2小时)
```bash
python3 master_pipeline.py all
```
- 📰 爬取所有平台热点新闻
- 🔢 向量化处理新闻内容
- 💾 追加到向量数据库

### 📝 **流水线2：文章生成** (每4小时)
```bash
python3 quick_start.py --auto
```
- 🔄 话题合并
- 🧠 深度分析
- 🔍 新闻检索
- 📝 文章生成
- 💾 保存到数据库

---

## 🖥️ **服务器要求**

### 推荐配置
- **CPU**: 4核心 (支持5线程并发)
- **内存**: 16GB (充足的缓存空间)
- **存储**: 50GB+ SSD (向量数据库和日志)
- **网络**: 稳定的互联网连接
- **系统**: Ubuntu 20.04+ / CentOS 8+

### 软件依赖
- Python 3.8+
- MySQL 8.0+
- Git

---

## 📦 **部署步骤**

### 1. 环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python和MySQL
sudo apt install python3 python3-pip mysql-server git -y

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql
```

### 2. 项目部署

```bash
# 创建项目目录
sudo mkdir -p /opt/news_system
cd /opt/news_system

# 上传项目文件 (使用scp、rsync或git)
# 方式1: 使用git (如果有仓库)
git clone <your-repo-url> .

# 方式2: 使用scp上传
# scp -r ./integrated_news_system/ user@server:/opt/news_system/

# 设置权限
sudo chown -R $USER:$USER /opt/news_system
```

### 3. Python环境配置

```bash
# 安装依赖
cd /opt/news_system
pip3 install -r requirements.txt

# 或手动安装主要依赖
pip3 install requests mysql-connector-python numpy python-dotenv
```

### 4. 数据库配置

```bash
# 登录MySQL
sudo mysql -u root -p

# 创建数据库和用户
CREATE DATABASE news_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'news_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON news_system.* TO 'news_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 5. 配置文件设置

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
nano .env
```

**配置示例**：
```env
# LLM配置
LLM_API_KEYS=["key1","key2","key3","key4","key5"]
LLM_BASE_URL=https://api.deepseek.com
LLM_MODEL=deepseek-chat

# Embedding配置
EMBEDDING_API_KEY=your_embedding_key
EMBEDDING_BASE_URL=https://api.deepseek.com
EMBEDDING_MODEL=deepseek-embedder

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=news_user
DB_PASSWORD=your_password
DB_NAME=news_system

# 向量数据库路径
VECTOR_DB_PATH=../新闻爬取/news_vectors
```

### 6. 初始化系统

```bash
# 检查配置
python3 check_config.py

# 初始化数据库
python3 quick_start.py --init-db

# 快速测试
python3 quick_test.py
```

---

## ⏰ **定时任务配置**

### 1. 设置脚本权限

```bash
chmod +x run_news_crawl.sh
chmod +x run_article_generation.sh
```

### 2. 配置Crontab

```bash
# 编辑定时任务
crontab -e

# 添加以下内容
# 新闻爬取：每2小时执行 (0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22点)
0 */2 * * * cd /opt/news_system && ./run_news_crawl.sh

# 文章生成：每4小时执行，错开30分钟 (0:30, 4:30, 8:30, 12:30, 16:30, 20:30)
30 */4 * * * cd /opt/news_system && ./run_article_generation.sh
```

### 3. 验证定时任务

```bash
# 查看定时任务
crontab -l

# 查看cron日志
sudo tail -f /var/log/cron

# 手动测试脚本
cd /opt/news_system
./run_news_crawl.sh
./run_article_generation.sh
```

---

## 📊 **监控和维护**

### 1. 日志监控

```bash
# 查看新闻爬取日志
tail -f logs/news_crawl_$(date +%Y%m%d).log

# 查看文章生成日志
tail -f logs/article_generation_$(date +%Y%m%d).log

# 查看系统资源
htop
```

### 2. 数据库监控

```bash
# 检查文章数量
mysql -u news_user -p news_system -e "SELECT COUNT(*) as total_articles FROM articles;"

# 检查最近生成的文章
mysql -u news_user -p news_system -e "SELECT title, created_at FROM articles ORDER BY created_at DESC LIMIT 10;"
```

### 3. 性能优化

```bash
# 查看磁盘使用
df -h

# 清理旧日志 (保留30天)
find /opt/news_system/logs -name "*.log" -mtime +30 -delete

# 清理旧新闻文件 (保留7天)
find /opt/news_system/news_output -name "*.json" -mtime +7 -delete
```

---

## 🔧 **故障排除**

### 常见问题

1. **定时任务不执行**
   ```bash
   # 检查cron服务
   sudo systemctl status cron
   
   # 重启cron服务
   sudo systemctl restart cron
   ```

2. **Python模块找不到**
   ```bash
   # 检查Python路径
   which python3
   
   # 在crontab中指定完整路径
   0 */2 * * * cd /opt/news_system && /usr/bin/python3 master_pipeline.py all
   ```

3. **数据库连接失败**
   ```bash
   # 检查MySQL服务
   sudo systemctl status mysql
   
   # 测试连接
   python3 quick_start.py --test-db
   ```

4. **API调用失败**
   ```bash
   # 检查网络连接
   curl -I https://api.deepseek.com
   
   # 检查API Key配置
   python3 check_config.py
   ```

---

## 📈 **扩展配置**

### 高频率配置 (资源充足时)
```bash
# 新闻爬取：每1小时
0 * * * * cd /opt/news_system && ./run_news_crawl.sh

# 文章生成：每2小时
30 */2 * * * cd /opt/news_system && ./run_article_generation.sh
```

### 低频率配置 (资源有限时)
```bash
# 新闻爬取：每4小时
0 */4 * * * cd /opt/news_system && ./run_news_crawl.sh

# 文章生成：每8小时
30 */8 * * * cd /opt/news_system && ./run_article_generation.sh
```

---

## 🎯 **部署检查清单**

- [ ] 服务器环境准备完成
- [ ] Python依赖安装完成
- [ ] MySQL数据库配置完成
- [ ] .env配置文件设置完成
- [ ] 配置检查通过 (`python3 check_config.py`)
- [ ] 数据库初始化完成 (`python3 quick_start.py --init-db`)
- [ ] 快速测试通过 (`python3 quick_test.py`)
- [ ] 脚本权限设置完成
- [ ] 定时任务配置完成
- [ ] 手动测试脚本执行成功
- [ ] 日志监控配置完成

---

## 📞 **技术支持**

部署过程中遇到问题，请按以下顺序排查：

1. 运行 `python3 check_config.py` 检查配置
2. 查看相关日志文件
3. 检查系统资源使用情况
4. 验证网络连接和API服务

**🎉 部署完成后，系统将自动运行，每天生成6批高质量文章！**
