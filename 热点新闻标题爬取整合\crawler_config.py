#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫配置文件
可以在这里调整各平台的数据获取数量和其他参数
"""

# 各平台数据获取数量配置
PLATFORM_LIMITS = {
    'zhihu': 50,        # 知乎热榜数量 (API支持最大50)
    'bilibili': 50,     # B站热搜数量 (API支持最大50)  
    'github': 50,       # GitHub趋势数量 (API支持最大100)
    'v2ex': 50,         # V2EX热门数量 (手动限制)
    'ithome': 50,       # IT之家新闻数量 (手动限制)
}

# 网络请求配置
NETWORK_CONFIG = {
    'timeout': 10,          # 请求超时时间（秒）
    'max_retries': 3,       # 最大重试次数
    'retry_delay': 1,       # 重试间隔（秒）
    'request_interval': 2,  # 请求间隔（秒）- 增加到2秒更安全
}

# 各平台特定的速率限制配置
PLATFORM_RATE_LIMITS = {
    'github': {
        'requests_per_hour': 60,        # GitHub未认证限制: 60/小时
        'min_interval': 60,             # 最小间隔60秒 (3600/60)
        'burst_limit': 5,               # 突发请求限制
    },
    'weibo': {
        'requests_per_hour': 100,       # 保守估计
        'min_interval': 36,             # 36秒间隔
        'burst_limit': 3,
    },
    'zhihu': {
        'requests_per_hour': 120,       # 保守估计
        'min_interval': 30,             # 30秒间隔
        'burst_limit': 3,
    },
    'baidu': {
        'requests_per_hour': 100,
        'min_interval': 36,
        'burst_limit': 3,
    },
    'bilibili': {
        'requests_per_hour': 120,
        'min_interval': 30,
        'burst_limit': 3,
    },
    'toutiao': {
        'requests_per_hour': 60,        # 字节跳动可能更严格
        'min_interval': 60,
        'burst_limit': 2,
    },
    'v2ex': {
        'requests_per_hour': 300,       # RSS相对宽松
        'min_interval': 12,
        'burst_limit': 5,
    },
    'ithome': {
        'requests_per_hour': 100,
        'min_interval': 36,
        'burst_limit': 3,
    }
}

# 代理配置（如果需要）
PROXY_CONFIG = {
    'enabled': False,
    'http_proxy': 'http://127.0.0.1:7890',
    'https_proxy': 'http://127.0.0.1:7890',
}

# 输出配置
OUTPUT_CONFIG = {
    'save_to_file': True,           # 是否保存到文件
    'show_summary': True,           # 是否显示摘要
    'summary_preview_count': 5,     # 摘要中每个平台显示的条目数
    'filename_format': 'news_data_{timestamp}.json',  # 文件名格式
}

# 平台开关配置（可以关闭某些平台）
PLATFORM_ENABLED = {
    'weibo': True,      # 微博热搜
    'baidu': True,      # 百度热搜
    'zhihu': True,      # 知乎热榜
    'toutiao': True,    # 今日头条
    'bilibili': True,   # B站热搜
    'github': True,     # GitHub趋势
    'v2ex': True,       # V2EX热门
    'ithome': True,     # IT之家
}
