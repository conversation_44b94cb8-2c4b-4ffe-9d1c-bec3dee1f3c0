#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
澎湃新闻时事板块爬虫 - 基于真实API接口
参照 thepaper_depth_crawler.py 的代码结构开发
"""

import requests
import json
import time
import csv
import re
from datetime import datetime
from bs4 import BeautifulSoup

class ThepaperNewsCrawler:
    def __init__(self):
        self.base_url = "https://m.thepaper.cn/channel_25950"
        self.news_api_url = "https://api.thepaper.cn/contentapi/nodeCont/getByChannelId"
        self.detail_api_url = "https://m.thepaper.cn/_next/data/eiysMZXyiUe6qMsiQkGMm/detail/{}.json"
        
        # 根据抓包结果设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://m.thepaper.cn/channel_25950',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Origin': 'https://m.thepaper.cn'
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 存储已获取的新闻ID，用于去重
        self.collected_news_ids = []
        
    def get_first_page_html(self):
        """获取时事板块首页HTML，提取初始数据"""
        try:
            print("正在获取时事板块首页HTML数据...")
            response = self.session.get(self.base_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            news_items = []
            
            # 时事板块使用与要闻板块相同的HTML结构
            wrappers = soup.find_all('div', class_='index_wrapper__9rz3z')
            print(f"在时事板块首页找到 {len(wrappers)} 个新闻项")
            
            for wrapper in wrappers:
                news_item = self.parse_html_news_item(wrapper)
                if news_item and news_item.get('news_id'):
                    news_items.append(news_item)
                    # 收集新闻ID用于后续API请求
                    self.collected_news_ids.append(int(news_item['news_id']))
                    
            print(f"成功解析 {len(news_items)} 个时事新闻项")
            return news_items
            
        except Exception as e:
            print(f"获取时事板块首页HTML数据失败: {e}")
            return []
    
    def parse_html_news_item(self, wrapper):
        """解析HTML中的单个新闻项"""
        try:
            news_item = {}
            
            # 获取新闻链接和ID
            title_link = wrapper.find('a', href=True)
            if title_link:
                news_item['url'] = title_link['href']
                if news_item['url'].startswith('/'):
                    news_item['url'] = 'https://m.thepaper.cn' + news_item['url']
                
                # 提取新闻ID
                match = re.search(r'/newsDetail_forward_(\d+)', news_item['url'])
                if match:
                    news_item['news_id'] = match.group(1)
            
            # 获取标题
            title_elem = wrapper.find('h3', class_='index_title__aGAqD')
            if title_elem:
                news_item['title'] = title_elem.get_text().strip()
            
            # 获取图片
            img_elem = wrapper.find('img')
            if img_elem:
                news_item['image_url'] = img_elem.get('src', '')
            
            # 获取来源
            extra_elem = wrapper.find('div', class_='index_extra__M2kfN')
            if extra_elem:
                source_link = extra_elem.find('a')
                if source_link:
                    news_item['source'] = source_link.get_text().strip()
            
            # 获取发布时间和评论数
            time_spans = wrapper.find_all('span')
            for span in time_spans:
                text = span.get_text().strip()
                if '前' in text or '小时' in text or '分钟' in text or '天前' in text or '刚刚' in text:
                    news_item['pub_time'] = text
                elif '评' in text:
                    news_item['comment_count'] = text
            
            # 检查是否为视频
            video_elem = wrapper.find('span', class_='watermark_duration')
            if video_elem:
                duration_elem = video_elem.find('i', class_='watermark_num')
                if duration_elem:
                    news_item['video_duration'] = duration_elem.get_text().strip()
                    news_item['content_type'] = 'video'
            else:
                news_item['content_type'] = 'article'
            
            news_item['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            news_item['data_source'] = 'html'
            news_item['channel'] = 'news'
            
            return news_item
            
        except Exception as e:
            print(f"解析HTML新闻项失败: {e}")
            return None
    
    def get_news_by_api(self, page_num=2, start_time=None):
        """通过时事板块API获取新闻列表"""
        try:
            # 使用当前时间戳或传入的startTime
            if start_time is None:
                start_time = int(time.time() * 1000)

            # 时事板块的API参数 (与要闻板块相同，但channelId不同)
            # 使用excludeContIds参数排除已获取的新闻ID，减少重复
            payload = {
                "channelId": "25950",  # 时事板块的channelId
                "excludeContIds": self.collected_news_ids,  # 排除已获取的新闻ID
                "listRecommendIds": [],
                "pageSize": 10,
                "startTime": start_time,
                "pageNum": page_num
            }
            
            print(f"时事API请求参数: pageNum={page_num}, startTime={start_time}, channelId=25950")
            print(f"排除的新闻ID数量: {len(self.collected_news_ids)}")
            
            # 添加超时设置
            response = self.session.post(self.news_api_url, json=payload, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            print(f"时事API响应状态码: {data.get('code')}")
            
            if data.get('code') == 200:
                api_data = data.get('data', {})
                news_list = api_data.get('list', [])
                has_next = api_data.get('hasNext', False)
                returned_start_time = api_data.get('startTime')
                
                print(f"获取到 {len(news_list)} 条时事新闻")
                print(f"返回的startTime: {returned_start_time}")
                
                # 解析新闻数据
                news_items = []
                duplicate_count = 0
                for news_data in news_list:
                    news_item = self.parse_api_news_item(news_data)
                    if news_item:
                        # 检查是否重复（基于news_id）
                        news_id = news_item.get('news_id')
                        if news_id and int(news_id) not in self.collected_news_ids:
                            news_items.append(news_item)
                            self.collected_news_ids.append(int(news_id))
                        else:
                            duplicate_count += 1
                            print(f"跳过重复新闻: ID:{news_id} - {news_item.get('title', '')[:50]}...")

                if duplicate_count > 0:
                    print(f"本页发现 {duplicate_count} 条重复新闻，新增 {len(news_items)} 条")

                return {
                    'news_items': news_items,
                    'has_next': has_next,
                    'start_time': returned_start_time  # 使用API返回的startTime
                }
            else:
                print(f"时事API返回错误: {data}")
                return None
                
        except Exception as e:
            print(f"获取时事新闻列表失败: {e}")
            return None
    
    def parse_api_news_item(self, news_data):
        """解析时事板块API返回的新闻数据"""
        try:
            news_item = {
                'news_id': str(news_data.get('contId', '')),
                'title': news_data.get('name', ''),
                'url': f"https://m.thepaper.cn/newsDetail_forward_{news_data.get('contId')}",
                'image_url': news_data.get('smallPic', ''),
                'pub_time': news_data.get('pubTime', ''),
                'pub_time_new': news_data.get('pubTimeNew', ''),
                'pub_time_long': news_data.get('pubTimeLong', ''),
                'praise_times': news_data.get('praiseTimes', '0'),
                'interaction_num': news_data.get('interactionNum', ''),
                'content_type': 'video' if news_data.get('contType') == 1 else 'article',
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_source': 'api',
                'channel': 'news'
            }
            
            # 获取来源信息
            node_info = news_data.get('nodeInfo', {})
            if node_info:
                news_item['source'] = node_info.get('name', '')
                news_item['source_id'] = node_info.get('nodeId', '')
                news_item['source_desc'] = node_info.get('desc', '')
            
            # 获取标签信息
            tag_list = news_data.get('tagList', [])
            if tag_list:
                news_item['tags'] = [tag.get('tag') for tag in tag_list if tag.get('tag')]
                news_item['tags_str'] = ','.join(news_item['tags'])
            
            return news_item
            
        except Exception as e:
            print(f"解析时事API新闻数据失败: {e}")
            return None
    
    def is_today_news(self, pub_time_long, debug=False):
        """检查新闻是否是今天发布的"""
        try:
            if not pub_time_long:
                return True  # 如果没有时间戳，默认认为是今天的

            # 将时间戳转换为日期
            news_datetime = datetime.fromtimestamp(pub_time_long / 1000)
            news_date = news_datetime.date()
            today = datetime.now().date()

            is_today = news_date == today

            if debug:
                hours_ago = (datetime.now() - news_datetime).total_seconds() / 3600
                print(f"    时间戳调试: {pub_time_long} -> {news_datetime} -> {news_date} (距现在{hours_ago:.1f}小时) -> 今日:{is_today}")

            return is_today
        except Exception as e:
            if debug:
                print(f"    时间戳解析失败: {pub_time_long}, 错误: {e}")
            return True  # 出错时默认认为是今天的

    def get_news_detail(self, news_id):
        """获取新闻详细内容（与深度板块相同）"""
        try:
            detail_url = self.detail_api_url.format(news_id)

            # 设置详情页请求头
            detail_headers = self.headers.copy()
            detail_headers.update({
                'Referer': f'https://m.thepaper.cn/newsDetail_forward_{news_id}',
                'Accept': 'application/json'
            })

            print(f"获取时事新闻详情: {news_id}")
            # 添加超时和重试机制
            response = self.session.get(detail_url, headers=detail_headers, timeout=15)
            response.raise_for_status()

            data = response.json()

            # 解析详情数据
            page_props = data.get('pageProps', {})
            detail_data = page_props.get('detailData', {})
            content_detail = detail_data.get('contentDetail', {})

            if content_detail:
                detail_info = {
                    'news_id': news_id,
                    'title': content_detail.get('name', ''),
                    'summary': content_detail.get('summary', ''),
                    'author': content_detail.get('author', ''),
                    'publish_time': content_detail.get('pubTime', ''),
                    'content_html': content_detail.get('content', ''),
                    'tags': content_detail.get('tags', ''),
                    'track_keyword': content_detail.get('trackKeyword', ''),
                }

                # 提取纯文本内容
                if detail_info['content_html']:
                    soup = BeautifulSoup(detail_info['content_html'], 'html.parser')
                    # 移除图片标签
                    for img in soup.find_all('img'):
                        img.decompose()

                    # 提取段落文本
                    paragraphs = soup.find_all('p')
                    content_text = []
                    for p in paragraphs:
                        text = p.get_text().strip()
                        if text:
                            content_text.append(text)

                    detail_info['content'] = '\n\n'.join(content_text)
                    detail_info['content_length'] = len(detail_info['content'])
                else:
                    detail_info['content'] = detail_info['summary']
                    detail_info['content_length'] = len(detail_info['content'])

                return detail_info
            else:
                print(f"未找到详情数据: {news_id}")
                return None

        except requests.exceptions.Timeout:
            print(f"获取新闻详情超时 {news_id}")
            return None
        except requests.exceptions.RequestException as e:
            print(f"获取新闻详情网络错误 {news_id}: {e}")
            return None
        except Exception as e:
            print(f"获取新闻详情失败 {news_id}: {e}")
            return None

    def crawl_news_continuously(self, get_detail=False, save_interval=50):
        """持续爬取时事板块今日新闻，不设置页数限制"""
        all_news = []
        page = 1
        current_start_time = None
        consecutive_failures = 0
        max_consecutive_failures = 3

        print("=== 开始持续爬取时事板块今日新闻 ===")
        print(f"目标: 爬取今天({datetime.now().strftime('%Y-%m-%d')})的所有时事新闻")
        print("=" * 60)

        # 1. 先获取首页HTML数据
        print("=== 第1步: 获取时事板块首页HTML数据 ===")
        html_news = self.get_first_page_html()
        today_html_news = []

        for news in html_news:
            # HTML数据没有精确时间戳，默认认为是今天的
            today_html_news.append(news)

        all_news.extend(today_html_news)
        print(f"首页获取到今日时事新闻: {len(today_html_news)} 条")

        # 打印首页新闻信息用于调试
        if today_html_news:
            print("\n首页新闻列表:")
            for i, news in enumerate(today_html_news[:10]):  # 只显示前10条
                print(f"  {i+1:2d}. ID:{news.get('news_id', 'N/A')} - {news.get('title', 'N/A')[:50]}...")
            if len(today_html_news) > 10:
                print(f"  ... 还有 {len(today_html_news) - 10} 条")

            print(f"\n首页新闻ID列表: {[news.get('news_id') for news in today_html_news[:5]]}...")  # 显示前5个ID

        # 2. 通过API持续获取数据
        print("\n=== 第2步: 持续通过时事API获取今日新闻 ===")

        while True:
            page += 1
            print(f"\n--- 获取第 {page} 页 ---")

            try:
                api_result = self.get_news_by_api(page, current_start_time)

                if api_result and api_result['news_items']:
                    # 筛选今日新闻
                    today_news = []
                    non_today_count = 0

                    for news in api_result['news_items']:
                        pub_time_long = news.get('pub_time_long')
                        # 当页数较高时启用调试模式
                        debug_mode = page >= 7
                        if self.is_today_news(pub_time_long, debug=debug_mode):
                            today_news.append(news)
                        else:
                            non_today_count += 1
                            print(f"跳过非今日新闻: {news.get('title', '')[:50]}... (发布时间: {news.get('pub_time', 'N/A')})")
                            if debug_mode:
                                print(f"  -> 新闻ID: {news.get('news_id')}, 时间戳: {pub_time_long}")

                    if today_news:
                        all_news.extend(today_news)
                        print(f"第 {page} 页获取到今日时事新闻: {len(today_news)} 条")
                        consecutive_failures = 0  # 重置失败计数

                        # 更新startTime用于下一页请求
                        current_start_time = api_result.get('start_time')

                        # 定期保存数据
                        if len(all_news) % save_interval == 0:
                            self.save_to_csv(all_news, f"thepaper_news_today_temp_{len(all_news)}.csv")
                            print(f"已保存临时数据: {len(all_news)} 条时事新闻")

                    # 如果这一页全部都不是今日新闻，说明已经到了昨天的新闻
                    if non_today_count > 0 and len(today_news) == 0:
                        print(f"第 {page} 页全部为非今日新闻，已获取完今日所有时事新闻")
                        print(f"本页非今日新闻数量: {non_today_count}")
                        break

                    # 如果非今日新闻比例过高，也考虑停止（避免API数据乱序问题）
                    if non_today_count > 7 and len(today_news) <= 2:
                        print(f"第 {page} 页非今日新闻过多({non_today_count}条)，今日新闻过少({len(today_news)}条)，可能已接近时间边界")
                        print("继续获取下一页以确认...")
                        # 不立即停止，再试一页

                    # 检查是否还有下一页
                    if not api_result['has_next']:
                        print("API显示已到达最后一页")
                        break

                else:
                    consecutive_failures += 1
                    print(f"第 {page} 页获取失败 ({consecutive_failures}/{max_consecutive_failures})")

                    if consecutive_failures >= max_consecutive_failures:
                        print(f"连续 {max_consecutive_failures} 页获取失败，停止爬取")
                        break

                # 显示进度
                print(f"当前进度: 总计 {len(all_news)} 条今日时事新闻")

                # 延时避免请求过快
                time.sleep(1.5)

            except KeyboardInterrupt:
                print("\n用户中断爬取")
                break
            except Exception as e:
                consecutive_failures += 1
                print(f"第 {page} 页出现异常: {e}")
                if consecutive_failures >= max_consecutive_failures:
                    print(f"连续 {max_consecutive_failures} 次异常，停止爬取")
                    break
                time.sleep(3)  # 异常时延时更长

        print(f"\n=== 时事板块爬取完成 ===")
        print(f"总共获取到今日时事新闻: {len(all_news)} 条")
        print(f"爬取页数: {page} 页")

        # 3. 获取详细内容（可选）
        if get_detail and all_news:
            print(f"\n=== 第3步: 获取时事新闻详细内容 ===")
            print(f"准备获取 {len(all_news)} 条时事新闻的详细内容...")

            success_count = 0
            for i, news in enumerate(all_news):
                if news.get('news_id'):
                    print(f"获取详情 {i+1}/{len(all_news)}: {news['title'][:30]}...")
                    detail = self.get_news_detail(news['news_id'])
                    if detail:
                        news.update(detail)
                        success_count += 1
                        if (i + 1) % 10 == 0:  # 每10条显示一次进度
                            print(f"详情获取进度: {i+1}/{len(all_news)} (成功: {success_count})")
                    time.sleep(0.8)  # 控制请求频率

            print(f"详情获取完成: {success_count}/{len(all_news)}")

        return all_news

    def save_to_csv(self, news_list, filename=None):
        """保存数据到CSV文件"""
        if not filename:
            filename = f"thepaper_news_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        if not news_list:
            print("没有数据可保存")
            return

        # 定义CSV字段
        fieldnames = [
            'news_id', 'title', 'url', 'source', 'pub_time', 'content_type',
            'praise_times', 'tags_str', 'summary', 'content', 'content_length',
            'author', 'data_source', 'channel', 'crawl_time'
        ]

        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for news in news_list:
                    # 只写入存在的字段
                    row = {field: news.get(field, '') for field in fieldnames}
                    writer.writerow(row)

            print(f"时事新闻数据已保存到: {filename}")

        except Exception as e:
            print(f"保存CSV文件失败: {e}")

def main():
    """主函数"""
    crawler = ThepaperNewsCrawler()

    print("澎湃新闻时事板块爬虫启动")
    print("=" * 60)
    print("模式说明:")
    print("1. 持续爬取时事板块今日所有新闻，不设置页数限制")
    print("2. 自动过滤非今日新闻")
    print("3. 定期保存临时数据，防止数据丢失")
    print("4. 支持中途中断和恢复")
    print("=" * 60)

    # 询问是否获取详细内容
    print("\n是否获取时事新闻详细内容？")
    print("注意: 获取详情会显著增加爬取时间")
    print("1. 是 - 获取完整新闻内容")
    print("2. 否 - 只获取新闻列表信息")

    try:
        choice = input("请选择 (1/2): ").strip()
        get_detail = choice == '1'

        if get_detail:
            print("将获取完整时事新闻内容（包括正文）")
        else:
            print("只获取时事新闻列表信息")

        print("\n开始爬取时事板块...")
        print("提示: 按 Ctrl+C 可以随时中断爬取")

        # 持续爬取今日时事新闻
        news_list = crawler.crawl_news_continuously(get_detail=get_detail)

        if news_list:
            # 保存最终数据
            final_filename = f"thepaper_news_final_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            crawler.save_to_csv(news_list, final_filename)

            # 打印统计信息
            print(f"\n" + "=" * 60)
            print(f"时事板块爬取完成!")
            print(f"总新闻数: {len(news_list)}")
            print(f"HTML来源: {len([n for n in news_list if n.get('data_source') == 'html'])}")
            print(f"API来源: {len([n for n in news_list if n.get('data_source') == 'api'])}")
            print(f"有详细内容: {len([n for n in news_list if n.get('content')])}")
            print(f"最终数据已保存到: {final_filename}")

            # 显示前10条新闻标题
            print(f"\n前10条时事新闻:")
            for i, news in enumerate(news_list[:10]):
                pub_time = news.get('pub_time', 'N/A')
                source = news.get('source', 'N/A')
                print(f"{i+1:2d}. {news.get('title', 'N/A')} ({source} - {pub_time})")

            if len(news_list) > 10:
                print(f"... 还有 {len(news_list) - 10} 条时事新闻")

        else:
            print("未获取到任何时事新闻数据")

    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        print("已获取的数据已保存到临时文件")
    except Exception as e:
        print(f"\n程序出现异常: {e}")
        print("请检查网络连接或稍后重试")

if __name__ == "__main__":
    main()
