#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的显示效果
"""

from huanqiu_universal_spider import get_news

def test_display():
    """测试不同数量设置的显示效果"""
    
    print("=== 测试1: 限制数量模式 ===")
    news_data1 = get_news(max_news=3, get_detail=False, section='world')
    print(f"结果: 获取到 {len(news_data1)} 条新闻\n")
    
    print("=== 测试2: 当天全部新闻模式 ===")
    news_data2 = get_news(max_news=200, get_detail=False, section='finance')
    print(f"结果: 获取到 {len(news_data2)} 条新闻\n")

if __name__ == "__main__":
    test_display()
