#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成新闻系统快速启动脚本
提供简单的命令行界面来运行系统
"""

import sys
import os
import argparse
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config import print_config_status
from init_database import init_database, test_connection
from main_pipeline import IntegratedNewsPipeline


def show_banner():
    """显示系统横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    集成新闻系统 v1.0                          ║
║                Integrated News System                        ║
║                                                              ║
║  🚀 热点爬取 → 🔄 话题合并 → 🔍 新闻检索 → 📝 文章生成        ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查配置文件
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ 未找到.env配置文件")
        print("💡 请复制.env.example为.env并配置相关参数")
        return False
    
    # 检查配置状态
    if not print_config_status():
        print("❌ 配置检查失败")
        return False
    
    print("✅ 环境检查通过")
    return True


def setup_database():
    """设置数据库"""
    print("\n🗄️ 设置数据库...")
    
    try:
        if init_database():
            print("✅ 数据库初始化成功")
            return True
        else:
            print("❌ 数据库初始化失败")
            return False
    except Exception as e:
        print(f"❌ 数据库设置失败: {e}")
        return False


def run_pipeline(max_topics: int = None, max_articles: int = None):
    """运行主流水线"""
    print(f"\n🚀 启动主流水线...")
    print(f"   话题处理: {'处理所有合并话题' if max_topics is None else f'最多{max_topics}个话题'}")
    print(f"   文章生成: {'为所有话题生成文章' if max_articles is None else f'最多{max_articles}篇文章'}")
    
    try:
        pipeline = IntegratedNewsPipeline()
        result = pipeline.run_full_pipeline(
            max_topics=max_topics,
            max_articles=max_articles
        )
        
        return result['success']
        
    except Exception as e:
        print(f"❌ 流水线执行失败: {e}")
        return False


def interactive_mode():
    """交互模式"""
    show_banner()
    
    print("🎯 欢迎使用集成新闻系统!")
    print("请选择要执行的操作:\n")
    
    while True:
        print("📋 可用操作:")
        print("  1. 检查环境配置")
        print("  2. 初始化数据库")
        print("  3. 测试数据库连接")
        print("  4. 运行完整流水线")
        print("  5. 运行流水线(自定义参数)")
        print("  0. 退出")
        
        choice = input("\n请输入选项编号: ").strip()
        
        if choice == "0":
            print("👋 再见!")
            break
        elif choice == "1":
            check_environment()
        elif choice == "2":
            setup_database()
        elif choice == "3":
            test_connection()
        elif choice == "4":
            if check_environment():
                success = run_pipeline()
                if success:
                    print("\n🎉 流水线执行成功!")
                else:
                    print("\n❌ 流水线执行失败")
        elif choice == "5":
            if check_environment():
                try:
                    topics_input = input("请输入最大话题数 (回车=处理所有话题): ").strip()
                    max_topics = int(topics_input) if topics_input else None

                    articles_input = input("请输入最大文章数 (回车=为所有话题生成文章): ").strip()
                    max_articles = int(articles_input) if articles_input else None
                    
                    success = run_pipeline(max_topics, max_articles)
                    if success:
                        print("\n🎉 流水线执行成功!")
                    else:
                        print("\n❌ 流水线执行失败")
                except ValueError:
                    print("❌ 请输入有效的数字")
        else:
            print("❌ 无效选项，请重新选择")
        
        print("\n" + "-" * 60 + "\n")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="集成新闻系统快速启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python quick_start.py                    # 交互模式
  python quick_start.py --check            # 检查环境
  python quick_start.py --init-db          # 初始化数据库
  python quick_start.py --run              # 运行流水线
  python quick_start.py --run --topics 10  # 自定义参数运行
        """
    )
    
    parser.add_argument("--check", action="store_true", help="检查环境配置")
    parser.add_argument("--init-db", action="store_true", help="初始化数据库")
    parser.add_argument("--test-db", action="store_true", help="测试数据库连接")
    parser.add_argument("--run", action="store_true", help="运行完整流水线")
    parser.add_argument("--topics", type=int, default=None, help="最大话题数 (默认: 处理所有话题)")
    parser.add_argument("--articles", type=int, default=None, help="最大文章数 (默认: 为所有话题生成文章)")
    parser.add_argument("--auto", action="store_true", help="自动化模式，无交互运行完整流水线")
    parser.add_argument("--interactive", action="store_true", help="交互模式")
    
    args = parser.parse_args()
    
    # 如果没有参数，默认进入交互模式
    if len(sys.argv) == 1:
        interactive_mode()
        return
    
    show_banner()
    
    # 执行指定操作
    if args.check:
        check_environment()
    
    elif args.init_db:
        setup_database()
    
    elif args.test_db:
        test_connection()
    
    elif args.run:
        if check_environment():
            success = run_pipeline(args.topics, args.articles)
            if success:
                print("\n🎉 流水线执行成功!")
                sys.exit(0)
            else:
                print("\n❌ 流水线执行失败")
                sys.exit(1)
        else:
            print("\n❌ 环境检查失败")
            sys.exit(1)
    
    elif args.interactive:
        interactive_mode()

    elif args.auto:
        # 自动化模式：无交互运行完整流水线
        print("🤖 自动化模式启动...")
        print("📋 执行完整文章生成流水线...")
        if check_environment():
            success = run_pipeline(args.topics, args.articles)
            if success:
                print("\n🎉 自动化流水线执行成功!")
                sys.exit(0)
            else:
                print("\n❌ 自动化流水线执行失败")
                sys.exit(1)
        else:
            print("\n❌ 环境检查失败")
            sys.exit(1)

    else:
        parser.print_help()


if __name__ == "__main__":
    main()
