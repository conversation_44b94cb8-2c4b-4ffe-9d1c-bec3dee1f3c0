# 集成新闻系统 (Integrated News System)

一个完整的新闻热点分析和文章生成系统，集成了热点爬取、话题合并、新闻检索和智能文章生成功能。

## 🚀 系统特性

### 核心功能
- **🕷️ 多平台热点爬取**: 支持微博、百度、知乎、今日头条、B站、V2EX等主流平台
- **🔄 智能话题合并**: 使用LLM技术自动识别和合并相似话题
- **🔍 向量新闻检索**: 基于向量数据库的语义新闻搜索
- **📝 智能文章生成**: 结合LLM知识和新闻要点生成深度分析文章
- **💾 数据库存储**: MySQL数据库存储文章和处理记录

### 技术架构
- **前端爬虫**: 多平台新闻热点抓取
- **话题处理**: LLM驱动的话题分析和合并
- **向量检索**: 基于embedding的新闻相似度搜索
- **文章生成**: 深度分析报告自动生成
- **数据存储**: MySQL + 向量数据库双重存储

## 📋 系统要求

### 环境依赖
- Python 3.8+
- MySQL 5.7+ 或 8.0+
- 4GB+ 内存
- 稳定的网络连接

### Python依赖包
```bash
pip install requests beautifulsoup4 feedparser numpy mysql-connector-python
```

## 🛠️ 安装配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd integrated_news_system
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境变量
复制配置文件模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下参数：

```env
# LLM API 配置 (支持多个API Key)
LLM_API_KEY_1=sk-your-api-key-1
LLM_API_KEY_2=sk-your-api-key-2
LLM_API_KEY_3=sk-your-api-key-3

# Embedding API 配置
EMBEDDING_API_KEY=your-embedding-api-key
EMBEDDING_PROJECT_ID=your-project-id
EMBEDDING_EASYLLM_ID=your-easyllm-id

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=news_system

# 邮件配置 (可选)
EMAIL_FROM_ADDR=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_TO_ADDR=<EMAIL>
```

### 4. 初始化数据库
```bash
python init_database.py
```

## 🚀 快速开始

### 方式一：交互模式 (推荐)
```bash
python quick_start.py
```

### 方式二：命令行模式
```bash
# 检查环境配置
python quick_start.py --check

# 初始化数据库
python quick_start.py --init-db

# 运行完整流水线
python quick_start.py --run

# 自定义参数运行
python quick_start.py --run --topics 10 --articles 8
```

### 方式三：直接运行主流水线
```bash
python main_pipeline.py
```

## 📊 系统流程

### 完整流水线
```
📰 热点爬取 → 🔄 话题合并 → 🔍 话题分析 → 🔍 新闻检索 → 📝 文章生成 → 💾 数据库存储
```

### 详细步骤

1. **热点爬取** (`news_crawler.py`)
   - 从多个平台抓取热门话题
   - 支持微博、百度、知乎、今日头条、B站、V2EX
   - 自动去重和数据清洗

2. **话题合并** (`topic_merger.py`)
   - 使用LLM识别相似话题
   - 智能合并和分类
   - 重要性评分

3. **话题分析** (`topic_processor.py`)
   - 提取关键词和搜索查询词
   - 分析背景信息和关键要点
   - 识别相关实体

4. **新闻检索** (`news_search.py`)
   - 基于向量数据库的语义搜索
   - 多查询词并发检索
   - 相似度排序和去重

5. **文章生成** (`article_generator.py`)
   - 结合LLM知识和新闻要点
   - 生成结构化深度分析文章
   - 自动生成标题和摘要

6. **数据存储** (`database_manager.py`)
   - MySQL存储文章和元数据
   - 完整的处理记录追踪
   - 支持状态管理

## 📁 项目结构

```
integrated_news_system/
├── config.py              # 配置管理
├── database_manager.py    # 数据库管理
├── news_crawler.py        # 新闻爬虫
├── topic_merger.py        # 话题合并
├── topic_processor.py     # 话题处理
├── news_search.py         # 新闻搜索
├── article_generator.py   # 文章生成
├── vector_database.py     # 向量数据库
├── llm_client.py          # LLM客户端
├── main_pipeline.py       # 主流水线
├── quick_start.py         # 快速启动
├── init_database.py       # 数据库初始化
├── .env.example           # 配置模板
└── README.md              # 说明文档
```

## 🔧 配置说明

### LLM配置
- 支持多个API Key轮询使用
- 自动错误处理和重试
- 并发请求支持

### 数据库配置
- MySQL存储文章和元数据
- 向量数据库存储embedding
- 自动索引和优化

### 爬虫配置
- 支持代理设置
- 请求频率控制
- 自动重试机制

## 📈 使用示例

### 基本使用
```python
from main_pipeline import IntegratedNewsPipeline

# 创建流水线实例
pipeline = IntegratedNewsPipeline()

# 运行完整流水线
result = pipeline.run_full_pipeline(
    max_topics=8,    # 最多处理8个话题
    max_articles=5   # 最多生成5篇文章
)

print(f"执行结果: {result['success']}")
print(f"生成文章: {result['final_stats']['articles_generated']} 篇")
```

### 单独使用组件
```python
# 只爬取新闻
from news_crawler import NewsCrawler
crawler = NewsCrawler()
news_data = crawler.get_all_news()

# 只合并话题
from topic_merger import TopicMerger
merger = TopicMerger()
merged_topics = merger.merge_topics(topics_data)

# 只生成文章
from article_generator import ArticleGenerator
generator = ArticleGenerator()
article = generator.generate_article(topic_analysis, news_results)
```

## 🔍 监控和日志

### 执行日志
系统会自动记录详细的执行日志，包括：
- 各步骤执行时间
- 处理数据量统计
- 错误信息和重试记录
- API调用统计

### 数据库监控
```python
from database_manager import DatabaseManager

db = DatabaseManager()
db.connect()
stats = db.get_database_stats()
print(f"文章总数: {stats['total_articles']}")
print(f"话题总数: {stats['total_topics']}")
```

## ⚠️ 注意事项

1. **API配额**: 确保LLM和Embedding API有足够的配额
2. **网络稳定**: 爬虫需要稳定的网络连接
3. **数据库权限**: 确保MySQL用户有创建数据库和表的权限
4. **存储空间**: 向量数据库需要足够的磁盘空间
5. **并发控制**: 根据API限制调整并发数量

## 🐛 故障排除

### 常见问题

1. **配置文件错误**
   ```bash
   python quick_start.py --check
   ```

2. **数据库连接失败**
   ```bash
   python quick_start.py --test-db
   ```

3. **API调用失败**
   - 检查API Key是否正确
   - 确认API配额是否充足
   - 检查网络连接

4. **向量数据库为空**
   - 需要先运行新闻爬取和向量化流程
   - 检查embedding API配置

## 📞 技术支持

如有问题，请检查：
1. 配置文件是否正确
2. 依赖包是否完整安装
3. 数据库是否正常运行
4. API服务是否可用

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
