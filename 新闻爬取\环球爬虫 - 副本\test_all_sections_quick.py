#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试所有板块爬虫功能（每个板块只爬取1条新闻）
"""

from huanqiu_universal_spider import get_all_sections_news, SECTIONS

def test_all_sections_quick():
    """快速测试所有板块（每个板块1条新闻）"""
    print("=== 快速测试所有板块爬虫功能 ===")
    print("每个板块只爬取1条新闻进行测试")
    
    # 测试所有板块，每个板块只爬1条新闻
    news_data = get_all_sections_news(max_news_per_section=1, get_detail=True)
    
    print(f"\n=== 测试结果 ===")
    print(f"总共获取到 {len(news_data)} 条新闻")
    
    if news_data:
        # 按板块统计
        section_stats = {}
        for news in news_data:
            category = news['category']
            if category not in section_stats:
                section_stats[category] = 0
            section_stats[category] += 1
        
        print(f"\n各板块新闻数量:")
        for category, count in sorted(section_stats.items()):
            print(f"  {category}: {count} 条")
        
        # 显示前3篇文章预览
        print(f"\n=== 前3篇文章预览 ===")
        for i, news in enumerate(news_data[:3], 1):
            print(f"\n第{i}条:")
            print(f"标题: {news['title']}")
            print(f"分类: {news['category']}")
            print(f"时间: {news['publish_time']}")
            print(f"来源: {news['source']}")
            print(f"内容长度: {len(news['content'])}字")
            print("-" * 50)
    else:
        print("未获取到任何新闻数据")

if __name__ == "__main__":
    test_all_sections_quick()
