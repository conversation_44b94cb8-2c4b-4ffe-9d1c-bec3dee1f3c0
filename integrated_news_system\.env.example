# =============================================================================
# 集成新闻系统环境配置文件
# 基于现有项目配置整合，可直接使用
# =============================================================================

# ================================
# LLM API 配置
# ================================
# 支持多个API Key，用于并发处理 - 使用您现有的可用密钥
LLM_API_KEY_1=cNFp15XM0U0vuciTLLkA9AzhRrXtP2bAFR5EO5ze7_wrxDpS_Ter1vsKEr-iuXsg9P4tsgfp_4LnsheTIvDj5A
LLM_API_KEY_2=rW5pZyQF6nrTE3evSFB3zIDMQT1jQL2rhfH-B4bU46tpoH_aM_DMeHvxarAbRZ-YSgI5dCAeWaPRPgIrQxN5zw
LLM_API_KEY_3=1a1ghGwas57o_FwcPuZj9tWkiN-32iMJtSiFVNT-TPyFJd_J7alXtVI_U4RTzCGLcAUpb3etnSpQSrmQtmRL6w
LLM_API_KEY_4=fycjIVJO4kCs46RYxZkTKF-YicnhJqDD1fmlEZfIXxSwaKxF6jWC_xU27AnMjs5uwJGjdsUuC5cZ_WbZqG-A0A
LLM_API_KEY_5=WuQKTB7C1WP48jyTMXeBmDzisLfzB7KnUa2Wq91FQsGAqfszw12RYbDI_o9Ri5QXddHZyDs9sbTzgZmlEjjhFQ

# 备用密钥（来自新闻爬取项目）
# LLM_API_KEY_6=r0283fxCH9SB7UUKqgZq2wybCdND3MEQ8m4ZX9vdoxeYexDL24KICZxtN7gcSRxT24GtXdfF13WHJI2CqbWqLQ
# LLM_API_KEY_7=aSHiOk6FguzVA4th6-GFgZ59P_SNE5kl24b7m9L861MOdw4BYeFnVxiqrEiUnJ4o46AYrg2OUIdlUn05cPtFqg
# LLM_API_KEY_8=Ow_rBGPwNCSkBXT4e0geAmaXRLMhV09vD2P2DIeFT3behy4bD4PPXuei-YyOFJjzgGPod_OIIjlfaPCg0wji7w

# LLM API 基础配置
LLM_BASE_URL=https://www.sophnet.com/api/open-apis/chat/completions
LLM_MODEL=DeepSeek-V3-Fast

# ================================
# Embedding API 配置
# ================================
# 使用您现有的embedding配置
EMBEDDING_API_KEY=X3J8VrVdbMXh8bz6Y6QTOLGSm3czmYPGDhsCzCE0Wq0YZ5OqTbxb7rqpaILXAqLoLdVvxJkx-UQCfF_qhVUIIg
EMBEDDING_PROJECT_ID=7SmJNRxKTmI2Z4xDXbhbsA
EMBEDDING_EASYLLM_ID=2hVYnwTzZgm6gt0HWfSLiH

# ================================
# 数据库配置
# ================================
# 选项1: 使用您的云数据库（推荐用于生产）
# DB_HOST=mysql-24e746d3-kbxy2365806687-c9eb.b.aivencloud.com
# DB_PORT=13304
# DB_USER=avnadmin
# DB_PASSWORD=AVNS_kX8_YlNEfE3RquyPaG6
# DB_NAME=defaultdb
# DB_SSL_MODE=REQUIRED

# 选项2: 使用本地数据库（按您的要求配置）
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=news_system
DB_SSL_MODE=DISABLED

# ================================
# 邮件配置 (可选)
# ================================
# 使用您现有的邮件配置
EMAIL_FROM_ADDR=<EMAIL>
EMAIL_PASSWORD=yuafiecxiggrdhib
EMAIL_TO_ADDR=<EMAIL>
EMAIL_SMTP_SERVER=smtp.qq.com
EMAIL_SMTP_PORT=465

# ================================
# 系统配置
# ================================
# 向量数据库路径（使用现有的向量数据库）
VECTOR_DB_PATH=../新闻爬取/news_vectors

# 默认爬取数量
DEFAULT_MAX_NEWS=30

# 批处理大小
BATCH_SIZE=10

# 请求超时
REQUEST_TIMEOUT=10

# 质量阈值
MIN_CONTENT_LENGTH=50
SIMILARITY_THRESHOLD=0.3

# 文章生成配置
ARTICLE_MIN_LENGTH=500
ARTICLE_MAX_LENGTH=3000

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# =============================================================================
# 配置说明
# =============================================================================
#
# 1. 本配置文件基于您现有的两个项目配置整合而成
# 2. LLM API Keys 使用您已验证可用的密钥（5个并发）
# 3. Embedding API 使用您现有的配置
# 4. 数据库配置提供两个选项：
#    - 云数据库：您现有的Aiven MySQL数据库（注释掉）
#    - 本地数据库：按您要求的localhost:3306配置（当前启用）
# 5. 向量数据库路径指向您现有的news_vectors目录
#
# 使用方法：
# 1. 复制此文件为 .env：cp .env.example .env
# 2. 根据需要调整数据库配置（云端或本地）
# 3. 运行系统：python quick_start.py
#
# 注意事项：
# - 确保MySQL服务已启动（如使用本地数据库）
# - 确保API密钥有足够余额
# - 向量数据库路径需要指向现有的news_vectors目录
#
# =============================================================================
