#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复unified_news_crawler.py中的Unicode编码问题
"""

import re

def fix_unicode_issues():
    """修复Unicode问题"""
    
    # 读取文件
    with open('unified_news_crawler.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义替换规则 - 移除所有emoji字符
    replacements = [
        (r'📰', '[新闻]'),
        (r'📋', '[列表]'),
        (r'🔗', '[链接]'),
        (r'📅', '[时间]'),
        (r'🏷️', '[标签]'),
        (r'📝', '[内容]'),
        (r'❌', '[错误]'),
        (r'✅', '[成功]'),
        (r'⚠️', '[警告]'),
        (r'🎯', '[统计]'),
        (r'⏱️', '[耗时]'),
        (r'📈', '[速度]'),
        (r'📊', '[结果]'),
        (r'📁', '[文件]'),
        (r'💡', '[提示]'),
        (r'🎉', '[完成]'),
        (r'🚀', '[启动]'),
        (r'⚡', '[线程]'),
        (r'🔄', '[进程]'),
        (r'⏳', '[等待]'),
        (r'📄', '[详情]'),
        (r'🔍', '[搜索]'),
        (r'📦', '[归档]'),
        (r'🧹', '[清理]'),
        (r'🗑️', '[删除]'),
        (r'💾', '[保存]'),
        (r'🔧', '[优化]'),
        (r'📈', '[平均]'),
        (r'📰', '[媒体]'),
        (r'💻', '[系统]'),
        # 更多emoji字符
        (r'[\U0001F600-\U0001F64F]', ''),  # emoticons
        (r'[\U0001F300-\U0001F5FF]', ''),  # symbols & pictographs
        (r'[\U0001F680-\U0001F6FF]', ''),  # transport & map symbols
        (r'[\U0001F1E0-\U0001F1FF]', ''),  # flags
        (r'[\u2600-\u26FF]', ''),  # misc symbols
        (r'[\u2700-\u27BF]', ''),  # dingbats
    ]
    
    # 应用替换
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # 写回文件
    with open('unified_news_crawler.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Unicode问题修复完成！")

if __name__ == "__main__":
    fix_unicode_issues()