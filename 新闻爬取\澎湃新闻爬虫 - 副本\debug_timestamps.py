#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试澎湃新闻API时间戳的脚本
"""

import requests
import json
import time
from datetime import datetime

def debug_api_timestamps():
    """调试API返回的时间戳"""
    
    api_url = "https://api.thepaper.cn/contentapi/nodeCont/getByChannelId"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://m.thepaper.cn/channel_25950',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Origin': 'https://m.thepaper.cn'
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    start_time = int(time.time() * 1000)
    current_time = datetime.now()
    today = current_time.date()
    
    print(f"当前时间: {current_time}")
    print(f"今天日期: {today}")
    print(f"当前时间戳: {start_time}")
    print("=" * 60)
    
    # 测试第7-8页的数据（问题出现的地方）
    for page in [7, 8]:
        print(f"\n=== 调试第 {page} 页时间戳 ===")
        
        payload = {
            "channelId": "25950",
            "excludeContIds": [],
            "listRecommendIds": [],
            "pageSize": 10,
            "startTime": start_time,
            "pageNum": page
        }
        
        try:
            response = session.post(api_url, json=payload, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data.get('code') == 200:
                api_data = data.get('data', {})
                news_list = api_data.get('list', [])
                start_time = api_data.get('startTime')  # 更新startTime
                
                print(f"获取到 {len(news_list)} 条新闻")
                print(f"返回的startTime: {start_time}")
                
                for i, news in enumerate(news_list):
                    news_id = news.get('contId', '')
                    title = news.get('name', '')[:50]
                    pub_time = news.get('pubTime', '')
                    pub_time_long = news.get('pubTimeLong', '')
                    
                    # 分析时间戳
                    if pub_time_long:
                        try:
                            news_datetime = datetime.fromtimestamp(pub_time_long / 1000)
                            news_date = news_datetime.date()
                            hours_ago = (current_time - news_datetime).total_seconds() / 3600
                            
                            is_today = news_date == today
                            
                            print(f"  {i+1:2d}. ID:{news_id}")
                            print(f"      标题: {title}...")
                            print(f"      相对时间: {pub_time}")
                            print(f"      时间戳: {pub_time_long}")
                            print(f"      绝对时间: {news_datetime}")
                            print(f"      新闻日期: {news_date}")
                            print(f"      距现在: {hours_ago:.1f}小时")
                            print(f"      是否今日: {is_today}")
                            print()
                        except Exception as e:
                            print(f"  {i+1:2d}. ID:{news_id} - 时间戳解析失败: {e}")
                    else:
                        print(f"  {i+1:2d}. ID:{news_id} - 无时间戳")
                        print(f"      标题: {title}...")
                        print(f"      相对时间: {pub_time}")
                        print()
                
            else:
                print(f"API返回错误: {data}")
                
        except Exception as e:
            print(f"请求失败: {e}")

if __name__ == "__main__":
    debug_api_timestamps()
