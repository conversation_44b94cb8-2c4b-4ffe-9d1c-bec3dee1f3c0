# 澎湃新闻板块爬虫开发指南

## 概述
本指南教你如何为澎湃新闻的任意板块开发爬虫。澎湃新闻使用统一的架构，所以大部分板块的爬虫开发流程相似。

## 需要收集的信息

### 1. 基本信息
- **板块名称**: 如"要闻"、"深度"、"财经"等
- **板块URL**: 如 `https://m.thepaper.cn/channel_143064`
- **板块描述**: 用于代码注释和文档

### 2. API信息（最重要）
通过浏览器开发者工具抓包获取：

#### 2.1 API接口地址
- **要闻板块**: `https://api.thepaper.cn/contentapi/nodeCont/getByChannelId`
- **深度板块**: `https://api.thepaper.cn/contentapi/channel/depth`
- **其他板块**: 需要抓包确定

#### 2.2 请求参数
不同板块的参数可能不同：

**要闻板块参数示例:**
```json
{
    "channelId": "",
    "excludeContIds": [],
    "listRecommendIds": [],
    "pageSize": 10,
    "startTime": 1752722830037,
    "pageNum": 3
}
```

**深度板块参数示例:**
```json
{
    "cardMode": 152,
    "pageNum": 2,
    "pageSize": 10,
    "startTime": 1752631505608
}
```

#### 2.3 响应数据结构
不同板块的响应结构可能不同：

**要闻板块响应:**
```json
{
    "code": 200,
    "data": {
        "hasNext": true,
        "startTime": 1752721718466,
        "list": [...]
    }
}
```

**深度板块响应:**
```json
{
    "code": 200,
    "data": {
        "pageInfo": {
            "list": [...]
        }
    }
}
```

### 3. HTML结构信息

#### 3.1 首页容器选择器
- **要闻板块**: `div.index_wrapper__9rz3z`
- **深度板块**: `div.index_depth_content__OARY0` > `div.index_wrapper__JCwkF`
- **其他板块**: 需要检查HTML结构

#### 3.2 新闻项选择器
- 标题: `h3.index_title__xxxxx`
- 链接: `a[href*="/newsDetail_forward_"]`
- 图片: `img`
- 来源: `div.index_extra__xxxxx a`
- 时间: `span` (包含"前"、"小时"、"分钟"等)

## 抓包步骤详解

### 1. 打开浏览器开发者工具
1. 访问目标板块页面
2. 按F12打开开发者工具
3. 切换到"Network"(网络)标签
4. 勾选"Preserve log"(保留日志)

### 2. 触发API请求
1. 刷新页面或滚动到底部
2. 查找XHR/Fetch请求
3. 找到返回新闻列表的API请求

### 3. 分析API请求
记录以下信息：
- **请求URL**
- **请求方法** (GET/POST)
- **请求头** (特别是Content-Type)
- **请求参数** (JSON格式)
- **响应数据结构**

### 4. 测试API参数
- 修改`pageNum`测试分页
- 修改`startTime`测试时间过滤
- 确认必需参数和可选参数

## HTML结构分析

### 1. 检查页面源码
```bash
# 保存页面HTML用于分析
curl "https://m.thepaper.cn/channel_xxxxx" > page.html
```

### 2. 查找__NEXT_DATA__
澎湃新闻使用Next.js，首页数据通常在：
```html
<script id="__NEXT_DATA__" type="application/json">
{"props":{"pageProps":{"data":{"list":[...]}}}}
</script>
```

### 3. 确定新闻容器
使用浏览器检查元素功能：
1. 右键点击新闻项 → "检查元素"
2. 找到包含新闻的最外层容器
3. 记录CSS类名

## 开发新板块爬虫的步骤

### 1. 复制现有爬虫模板
```bash
cp thepaper_depth_crawler.py thepaper_新板块_crawler.py
```

### 2. 修改基本配置
```python
class ThePaper新板块Crawler:
    def __init__(self):
        self.base_url = "https://m.thepaper.cn/channel_新板块ID"
        self.api_url = "https://api.thepaper.cn/contentapi/新板块API路径"
        self.detail_api_url = "https://m.thepaper.cn/_next/data/eiysMZXyiUe6qMsiQkGMm/detail/{}.json"
```

### 3. 修改API请求参数
```python
def get_news_by_api(self, page_num=2, start_time=None):
    payload = {
        # 根据抓包结果修改参数
        "特定参数": "特定值",
        "pageNum": page_num,
        "pageSize": 10,
        "startTime": start_time
    }
```

### 4. 修改响应数据解析
```python
# 根据实际响应结构修改
api_data = data.get('data', {})
news_list = api_data.get('list', [])  # 或者 pageInfo.list
has_next = api_data.get('hasNext', False)  # 或者其他判断方式
```

### 5. 修改HTML解析
```python
# 修改容器选择器
wrappers = soup.find_all('div', class_='新板块的CSS类名')

# 修改标题选择器
title_elem = wrapper.find('h3', class_='新板块标题CSS类名')
```

## 常见问题和解决方案

### 1. API返回空数据
- 检查请求参数是否正确
- 检查请求头是否完整
- 确认API地址是否正确

### 2. HTML解析失败
- 检查CSS选择器是否正确
- 确认页面是否为动态加载
- 尝试从__NEXT_DATA__中提取数据

### 3. 时间过滤不准确
- 检查时间戳字段名称
- 确认时间戳单位(秒/毫秒)
- 调整时间比较逻辑

### 4. 分页逻辑错误
- 确认startTime的使用方式
- 检查hasNext字段
- 测试不同pageNum的返回结果

## 重要错误案例和解决方案

### 错误1: 大量重复新闻问题 ⚠️

**问题描述:**
爬虫运行时显示大量"跳过重复新闻"信息，但实际网站上并没有这么多重复新闻。

**错误现象:**
```
跳过重复新闻: 第1现场｜伊拉克一大型超市突发火灾，近50人遇难
跳过重复新闻: 暴雨+大风+雷电+高温，上海四预警高挂
跳过重复新闻: 宁夏师范大学党委书记王宏伟出任宁夏大学党委书记
```

**根本原因:**
1. **HTML和API数据重复**: 程序先从HTML获取首页数据，然后API返回的数据包含相同新闻
2. **API分页重复**: 澎湃新闻API在不同页面间可能有重叠数据
3. **excludeContIds参数未使用**: 代码中设置为空数组，没有排除已获取的新闻ID

**解决方案:**
```python
# 修改前 (错误)
payload = {
    "channelId": "25950",
    "excludeContIds": [],  # 空数组，不排除任何ID
    "listRecommendIds": [],
    "pageSize": 10,
    "startTime": start_time,
    "pageNum": page_num
}

# 修改后 (正确)
payload = {
    "channelId": "25950",
    "excludeContIds": self.collected_news_ids,  # 排除已获取的新闻ID
    "listRecommendIds": [],
    "pageSize": 10,
    "startTime": start_time,
    "pageNum": page_num
}
```

**验证方法:**
```python
# 添加调试信息
print(f"排除的新闻ID数量: {len(self.collected_news_ids)}")

# 统计重复数量
duplicate_count = 0
for news_data in news_list:
    news_item = self.parse_api_news_item(news_data)
    if news_item:
        news_id = news_item.get('news_id')
        if news_id and int(news_id) not in self.collected_news_ids:
            news_items.append(news_item)
            self.collected_news_ids.append(int(news_id))
        else:
            duplicate_count += 1
            print(f"跳过重复新闻: ID:{news_id} - {news_item.get('title', '')[:50]}...")

if duplicate_count > 0:
    print(f"本页发现 {duplicate_count} 条重复新闻，新增 {len(news_items)} 条")
```

### 错误2: 时间边界判断过早停止 ⚠️

**问题描述:**
爬虫在第7-8页就停止，提示"全部为非今日新闻"，但网站上还有很多今日新闻。

**错误现象:**
```
第 8 页全部为非今日新闻，已获取完今日所有时事新闻
```

**根本原因:**
1. **时间戳解析错误**: 可能时区或时间戳格式问题
2. **API数据时序混乱**: API返回的数据不是严格按时间排序
3. **日期边界判断过于严格**: 按严格的日期(年-月-日)判断，忽略了时间分布

**诊断方法:**
```python
def debug_timestamps(self):
    """调试时间戳问题的专用函数"""
    current_time = datetime.now()
    today = current_time.date()

    print(f"当前时间: {current_time}")
    print(f"今天日期: {today}")

    # 测试特定页面的时间戳
    for page in [7, 8]:
        api_result = self.get_news_by_api(page)
        if api_result:
            for news in api_result['news_items']:
                pub_time_long = news.get('pub_time_long')
                if pub_time_long:
                    news_datetime = datetime.fromtimestamp(pub_time_long / 1000)
                    news_date = news_datetime.date()
                    hours_ago = (current_time - news_datetime).total_seconds() / 3600
                    is_today = news_date == today

                    print(f"ID:{news.get('news_id')} - {news_datetime} - 今日:{is_today} - {hours_ago:.1f}小时前")
```

**解决方案:**
```python
def is_today_news(self, pub_time_long, debug=False):
    """改进的时间判断函数"""
    try:
        if not pub_time_long:
            return True

        news_datetime = datetime.fromtimestamp(pub_time_long / 1000)
        news_date = news_datetime.date()
        today = datetime.now().date()

        is_today = news_date == today

        if debug:
            hours_ago = (datetime.now() - news_datetime).total_seconds() / 3600
            print(f"    时间戳调试: {pub_time_long} -> {news_datetime} -> {news_date} (距现在{hours_ago:.1f}小时) -> 今日:{is_today}")

        return is_today
    except Exception as e:
        if debug:
            print(f"    时间戳解析失败: {pub_time_long}, 错误: {e}")
        return True

# 在主循环中启用调试
for news in api_result['news_items']:
    pub_time_long = news.get('pub_time_long')
    debug_mode = page >= 7  # 第7页开始启用调试
    if self.is_today_news(pub_time_long, debug=debug_mode):
        today_news.append(news)
    else:
        non_today_count += 1
        print(f"跳过非今日新闻: {news.get('title', '')[:50]}... (发布时间: {news.get('pub_time', 'N/A')})")
        if debug_mode:
            print(f"  -> 新闻ID: {news.get('news_id')}, 时间戳: {pub_time_long}")
```

**改进停止条件:**
```python
# 原来的停止条件 (过于严格)
if non_today_count > 0 and len(today_news) == 0:
    print(f"第 {page} 页全部为非今日新闻，已获取完今日所有时事新闻")
    break

# 改进的停止条件 (更智能)
if non_today_count > 0 and len(today_news) == 0:
    print(f"第 {page} 页全部为非今日新闻，已获取完今日所有时事新闻")
    print(f"本页非今日新闻数量: {non_today_count}")
    break

# 如果非今日新闻比例过高，也考虑停止（避免API数据乱序问题）
if non_today_count > 7 and len(today_news) <= 2:
    print(f"第 {page} 页非今日新闻过多({non_today_count}条)，今日新闻过少({len(today_news)}条)，可能已接近时间边界")
    print("继续获取下一页以确认...")
    # 不立即停止，再试一页

### 错误3: API参数配置错误 ⚠️

**问题描述:**
API请求返回空数据或错误数据，导致爬虫无法正常工作。

**常见错误:**
1. **channelId错误**: 使用了错误的板块ID
2. **参数名称错误**: 不同板块的参数名可能不同
3. **参数格式错误**: 数据类型不匹配

**诊断方法:**
```python
def test_api_parameters(self):
    """测试API参数的专用函数"""
    test_payloads = [
        # 测试基本参数
        {
            "channelId": "25950",
            "pageNum": 1,
            "pageSize": 10,
            "startTime": int(time.time() * 1000)
        },
        # 测试不同参数组合
        {
            "channelId": "25950",
            "excludeContIds": [],
            "listRecommendIds": [],
            "pageNum": 1,
            "pageSize": 10,
            "startTime": int(time.time() * 1000)
        }
    ]

    for i, payload in enumerate(test_payloads):
        print(f"\n测试参数组合 {i+1}:")
        print(f"参数: {json.dumps(payload, indent=2)}")

        try:
            response = self.session.post(self.api_url, json=payload, timeout=10)
            data = response.json()
            print(f"响应状态: {data.get('code')}")
            print(f"数据长度: {len(data.get('data', {}).get('list', []))}")
        except Exception as e:
            print(f"请求失败: {e}")
```

**解决方案:**
```python
# 1. 确保channelId正确
# 从浏览器抓包中获取正确的channelId
payload = {
    "channelId": "25950",  # 确保这是正确的板块ID
    # ...
}

# 2. 检查参数名称和格式
# 深度板块示例
payload = {
    "cardMode": 152,        # 注意：不是channelId
    "pageNum": page_num,
    "pageSize": 10,
    "startTime": start_time
}

# 3. 添加参数验证
def validate_api_response(self, data):
    """验证API响应数据"""
    if not data:
        raise ValueError("API返回空数据")

    if data.get('code') != 200:
        raise ValueError(f"API返回错误码: {data.get('code')}")

    api_data = data.get('data', {})
    if not api_data:
        raise ValueError("API数据为空")

    news_list = api_data.get('list', [])
    if not news_list:
        print("警告: 当前页面无新闻数据")

    return True
```

### 错误4: HTML选择器失效 ⚠️

**问题描述:**
HTML解析无法获取到新闻数据，或者获取的数据不完整。

**常见原因:**
1. **CSS类名变化**: 澎湃新闻更新了页面结构
2. **选择器过于具体**: 使用了动态生成的类名
3. **页面结构调整**: 新闻容器层级发生变化

**诊断方法:**
```python
def debug_html_structure(self):
    """调试HTML结构的专用函数"""
    try:
        response = self.session.get(self.base_url)
        soup = BeautifulSoup(response.text, 'html.parser')

        # 查找所有可能的新闻容器
        possible_containers = [
            'div[class*="wrapper"]',
            'div[class*="item"]',
            'div[class*="news"]',
            'div[class*="content"]'
        ]

        for selector in possible_containers:
            elements = soup.select(selector)
            print(f"选择器 '{selector}' 找到 {len(elements)} 个元素")

            if elements:
                # 分析第一个元素的结构
                first_elem = elements[0]
                print(f"第一个元素的类名: {first_elem.get('class')}")
                print(f"第一个元素的HTML: {str(first_elem)[:200]}...")

    except Exception as e:
        print(f"HTML结构调试失败: {e}")
```

**解决方案:**
```python
# 1. 使用更通用的选择器
# 错误：过于具体的类名
wrappers = soup.find_all('div', class_='index_wrapper__9rz3z')

# 正确：使用部分匹配
wrappers = soup.find_all('div', class_=lambda x: x and 'wrapper' in x)

# 2. 多重选择器备选方案
def get_news_containers(self, soup):
    """获取新闻容器，支持多种选择器"""
    selectors = [
        'div.index_wrapper__9rz3z',           # 主选择器
        'div[class*="wrapper"]',              # 备选1：包含wrapper的类
        'div[class*="item"]',                 # 备选2：包含item的类
        'article',                            # 备选3：article标签
        'div[class*="news"]'                  # 备选4：包含news的类
    ]

    for selector in selectors:
        try:
            if '.' in selector:
                class_name = selector.split('.')[1]
                containers = soup.find_all('div', class_=class_name)
            else:
                containers = soup.select(selector)

            if containers and len(containers) > 5:  # 至少要有5个新闻项
                print(f"使用选择器: {selector}, 找到 {len(containers)} 个容器")
                return containers
        except Exception as e:
            print(f"选择器 {selector} 失败: {e}")
            continue

    raise ValueError("所有HTML选择器都失效，需要更新选择器")

# 3. 动态适应类名变化
def find_title_element(self, container):
    """查找标题元素，支持多种可能的结构"""
    title_selectors = [
        'h3[class*="title"]',
        'h2[class*="title"]',
        'div[class*="title"]',
        'a[class*="title"]',
        'h3',
        'h2'
    ]

    for selector in title_selectors:
        title_elem = container.select_one(selector)
        if title_elem and title_elem.get_text().strip():
            return title_elem

    return None
```

### 错误5: 网络请求失败和重试机制 ⚠️

**问题描述:**
网络不稳定导致请求失败，爬虫中断或数据丢失。

**常见错误:**
1. **连接超时**: 网络延迟高
2. **请求频率过快**: 被服务器限制
3. **没有重试机制**: 偶发错误导致爬虫停止

**解决方案:**
```python
import time
import random
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class ImprovedCrawler:
    def __init__(self):
        # 配置重试策略
        retry_strategy = Retry(
            total=3,                    # 总重试次数
            backoff_factor=1,           # 重试间隔倍数
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session = requests.Session()
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置超时
        self.session.timeout = 15

    def safe_request(self, url, method='GET', **kwargs):
        """安全的网络请求，包含重试和错误处理"""
        max_retries = 3
        base_delay = 1

        for attempt in range(max_retries):
            try:
                if method.upper() == 'GET':
                    response = self.session.get(url, **kwargs)
                else:
                    response = self.session.post(url, **kwargs)

                response.raise_for_status()
                return response

            except requests.exceptions.Timeout:
                print(f"请求超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    time.sleep(delay)

            except requests.exceptions.RequestException as e:
                print(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    time.sleep(delay)

        raise Exception(f"请求失败，已重试 {max_retries} 次")

    def get_news_by_api_safe(self, page_num=2, start_time=None):
        """安全的API请求方法"""
        try:
            payload = {
                "channelId": "25950",
                "excludeContIds": self.collected_news_ids,
                "listRecommendIds": [],
                "pageSize": 10,
                "startTime": start_time,
                "pageNum": page_num
            }

            response = self.safe_request(
                self.api_url,
                method='POST',
                json=payload,
                timeout=15
            )

            data = response.json()

            # 验证响应数据
            if data.get('code') != 200:
                raise ValueError(f"API返回错误: {data}")

            return self.parse_api_response(data)

        except Exception as e:
            print(f"API请求失败: {e}")
            return None

    def crawl_with_error_recovery(self):
        """带错误恢复的爬取方法"""
        consecutive_failures = 0
        max_consecutive_failures = 3

        while True:
            try:
                # 执行爬取逻辑
                result = self.get_news_by_api_safe(page, current_start_time)

                if result:
                    consecutive_failures = 0  # 重置失败计数
                    # 处理成功的结果
                else:
                    consecutive_failures += 1
                    print(f"获取失败 ({consecutive_failures}/{max_consecutive_failures})")

                if consecutive_failures >= max_consecutive_failures:
                    print("连续失败次数过多，停止爬取")
                    break

                # 添加随机延时，避免请求过快
                delay = random.uniform(1.0, 2.0)
                time.sleep(delay)

            except KeyboardInterrupt:
                print("用户中断爬取")
                break
            except Exception as e:
                consecutive_failures += 1
                print(f"爬取异常: {e}")

                if consecutive_failures >= max_consecutive_failures:
                    print("异常次数过多，停止爬取")
                    break

                # 异常时延时更长
                time.sleep(5)

### 错误6: 数据保存和恢复问题 ⚠️

**问题描述:**
爬虫中断后数据丢失，或者CSV文件格式错误。

**常见问题:**
1. **中断数据丢失**: 没有定期保存临时数据
2. **CSV编码问题**: 中文字符显示乱码
3. **字段不一致**: 不同来源的数据字段不匹配

**解决方案:**
```python
import csv
import json
from datetime import datetime

class DataManager:
    def __init__(self):
        self.temp_save_interval = 50  # 每50条保存一次
        self.backup_dir = "backup"

    def save_to_csv_safe(self, news_list, filename=None):
        """安全的CSV保存方法"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"thepaper_news_{timestamp}.csv"

        if not news_list:
            print("没有数据可保存")
            return False

        # 标准化字段名
        standard_fields = [
            'news_id', 'title', 'url', 'source', 'pub_time', 'content_type',
            'praise_times', 'tags_str', 'summary', 'content', 'content_length',
            'author', 'data_source', 'channel', 'crawl_time'
        ]

        try:
            # 使用utf-8-sig编码，确保Excel能正确显示中文
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=standard_fields)
                writer.writeheader()

                for news in news_list:
                    # 标准化数据，确保所有字段都存在
                    row = {}
                    for field in standard_fields:
                        value = news.get(field, '')
                        # 处理特殊字符和换行符
                        if isinstance(value, str):
                            value = value.replace('\n', ' ').replace('\r', ' ')
                        row[field] = value

                    writer.writerow(row)

            print(f"数据已保存到: {filename}")
            return True

        except Exception as e:
            print(f"保存CSV文件失败: {e}")
            # 尝试保存为JSON备份
            self.save_to_json_backup(news_list, filename.replace('.csv', '.json'))
            return False

    def save_to_json_backup(self, news_list, filename):
        """保存JSON备份"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(news_list, f, ensure_ascii=False, indent=2)
            print(f"JSON备份已保存到: {filename}")
        except Exception as e:
            print(f"保存JSON备份失败: {e}")

    def auto_save_progress(self, all_news, current_count):
        """自动保存进度"""
        if current_count % self.temp_save_interval == 0:
            temp_filename = f"temp_progress_{current_count}.csv"
            if self.save_to_csv_safe(all_news, temp_filename):
                print(f"已保存临时进度: {current_count} 条新闻")

                # 同时保存状态信息
                state_info = {
                    'total_count': current_count,
                    'last_save_time': datetime.now().isoformat(),
                    'collected_ids': self.collected_news_ids[-10:] if hasattr(self, 'collected_news_ids') else []
                }

                with open(f"temp_state_{current_count}.json", 'w', encoding='utf-8') as f:
                    json.dump(state_info, f, ensure_ascii=False, indent=2)

    def load_progress(self, temp_filename):
        """加载之前的进度"""
        try:
            news_list = []
            with open(temp_filename, 'r', encoding='utf-8-sig') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    news_list.append(dict(row))

            print(f"已加载进度: {len(news_list)} 条新闻")
            return news_list

        except Exception as e:
            print(f"加载进度失败: {e}")
            return []

# 在主爬虫中使用
class ImprovedNewsCrawler:
    def __init__(self):
        self.data_manager = DataManager()
        # ... 其他初始化代码

    def crawl_with_auto_save(self):
        """带自动保存的爬取方法"""
        all_news = []

        try:
            while True:
                # 爬取逻辑
                new_items = self.get_page_news()
                if new_items:
                    all_news.extend(new_items)

                    # 自动保存进度
                    self.data_manager.auto_save_progress(all_news, len(all_news))

                # ... 其他逻辑

        except KeyboardInterrupt:
            print("\n用户中断，保存当前进度...")
            self.data_manager.save_to_csv_safe(all_news, "interrupted_progress.csv")
        except Exception as e:
            print(f"爬取异常: {e}")
            self.data_manager.save_to_csv_safe(all_news, "error_recovery.csv")
        finally:
            # 最终保存
            if all_news:
                final_filename = f"final_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                self.data_manager.save_to_csv_safe(all_news, final_filename)
```

### 错误7: 内存使用和性能问题 ⚠️

**问题描述:**
长时间运行时内存占用过高，或者爬取速度过慢。

**常见原因:**
1. **数据累积**: 所有数据都保存在内存中
2. **无效的重复检查**: 使用低效的数据结构
3. **没有清理临时数据**: 中间变量没有及时释放

**解决方案:**
```python
import gc
from collections import deque

class MemoryEfficientCrawler:
    def __init__(self):
        # 使用set进行快速重复检查
        self.collected_news_ids = set()

        # 使用deque限制内存中保存的数据量
        self.recent_news = deque(maxlen=1000)  # 只保留最近1000条

        # 分批处理配置
        self.batch_size = 100
        self.memory_check_interval = 500

    def efficient_duplicate_check(self, news_id):
        """高效的重复检查"""
        news_id_int = int(news_id)
        if news_id_int in self.collected_news_ids:
            return True

        self.collected_news_ids.add(news_id_int)
        return False

    def batch_process_news(self, news_batch):
        """分批处理新闻数据"""
        processed_batch = []

        for news in news_batch:
            # 处理单条新闻
            processed_news = self.process_single_news(news)
            if processed_news:
                processed_batch.append(processed_news)
                self.recent_news.append(processed_news)

        return processed_batch

    def memory_cleanup(self, current_count):
        """定期内存清理"""
        if current_count % self.memory_check_interval == 0:
            # 清理collected_news_ids，只保留最近的ID
            if len(self.collected_news_ids) > 10000:
                # 转换为列表，保留最新的8000个ID
                recent_ids = list(self.collected_news_ids)[-8000:]
                self.collected_news_ids = set(recent_ids)
                print(f"内存清理: 保留最近 {len(recent_ids)} 个新闻ID")

            # 强制垃圾回收
            gc.collect()
            print(f"内存清理完成，当前处理: {current_count} 条")

    def stream_crawl(self):
        """流式爬取，减少内存占用"""
        page = 1
        total_processed = 0
        current_batch = []

        while True:
            try:
                # 获取一页数据
                page_news = self.get_page_news(page)
                if not page_news:
                    break

                # 添加到当前批次
                current_batch.extend(page_news)

                # 当批次达到指定大小时处理
                if len(current_batch) >= self.batch_size:
                    processed_batch = self.batch_process_news(current_batch)

                    # 保存批次数据
                    if processed_batch:
                        batch_filename = f"batch_{total_processed // self.batch_size + 1}.csv"
                        self.data_manager.save_to_csv_safe(processed_batch, batch_filename)

                    total_processed += len(processed_batch)
                    current_batch = []  # 清空当前批次

                    # 定期内存清理
                    self.memory_cleanup(total_processed)

                page += 1

            except Exception as e:
                print(f"流式爬取异常: {e}")
                break

        # 处理剩余数据
        if current_batch:
            processed_batch = self.batch_process_news(current_batch)
            if processed_batch:
                final_filename = f"final_batch.csv"
                self.data_manager.save_to_csv_safe(processed_batch, final_filename)

        print(f"流式爬取完成，总计处理: {total_processed} 条新闻")
```

## 错误预防最佳实践 ✅

### 1. 开发前检查清单
- [ ] 确认目标板块的API接口地址
- [ ] 抓包获取正确的请求参数
- [ ] 验证响应数据结构
- [ ] 测试HTML选择器的有效性
- [ ] 确认时间字段的格式和含义

### 2. 代码质量保证
```python
# 添加详细的日志记录
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 在关键位置添加日志
logger = logging.getLogger(__name__)

def get_news_by_api(self, page_num, start_time):
    logger.info(f"开始获取第 {page_num} 页数据，startTime: {start_time}")

    try:
        # API请求逻辑
        response = self.session.post(self.api_url, json=payload)
        logger.info(f"API响应状态码: {response.status_code}")

        data = response.json()
        logger.info(f"API返回数据: code={data.get('code')}, 新闻数量={len(data.get('data', {}).get('list', []))}")

        return data

    except Exception as e:
        logger.error(f"API请求失败: {e}")
        raise
```

### 3. 测试和验证
```python
def run_comprehensive_test(self):
    """运行综合测试"""
    print("开始综合测试...")

    # 1. 测试API连接
    try:
        result = self.get_news_by_api(1)
        assert result is not None, "API连接失败"
        print("✅ API连接测试通过")
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

    # 2. 测试HTML解析
    try:
        html_news = self.get_first_page_html()
        assert len(html_news) > 0, "HTML解析无数据"
        print(f"✅ HTML解析测试通过，获取到 {len(html_news)} 条新闻")
    except Exception as e:
        print(f"❌ HTML解析测试失败: {e}")
        return False

    # 3. 测试时间过滤
    try:
        test_timestamp = int(time.time() * 1000)
        is_today = self.is_today_news(test_timestamp)
        assert is_today == True, "时间过滤逻辑错误"
        print("✅ 时间过滤测试通过")
    except Exception as e:
        print(f"❌ 时间过滤测试失败: {e}")
        return False

    # 4. 测试数据保存
    try:
        test_data = [{'news_id': '123', 'title': '测试新闻', 'url': 'http://test.com'}]
        result = self.data_manager.save_to_csv_safe(test_data, 'test_save.csv')
        assert result == True, "数据保存失败"
        print("✅ 数据保存测试通过")
    except Exception as e:
        print(f"❌ 数据保存测试失败: {e}")
        return False

    print("🎉 所有测试通过，爬虫可以正常运行")
    return True
```

### 4. 监控和告警
```python
def setup_monitoring(self):
    """设置监控和告警"""
    self.start_time = time.time()
    self.last_success_time = time.time()
    self.error_count = 0
    self.max_error_rate = 0.1  # 最大错误率10%

def check_health_status(self, total_requests, error_count):
    """检查健康状态"""
    current_time = time.time()

    # 检查错误率
    if total_requests > 0:
        error_rate = error_count / total_requests
        if error_rate > self.max_error_rate:
            print(f"⚠️ 警告: 错误率过高 {error_rate:.2%}")

    # 检查是否长时间无成功请求
    time_since_success = current_time - self.last_success_time
    if time_since_success > 300:  # 5分钟
        print(f"⚠️ 警告: 已有 {time_since_success:.0f} 秒无成功请求")

    # 检查运行时间
    total_runtime = current_time - self.start_time
    if total_runtime > 3600:  # 1小时
        print(f"ℹ️ 信息: 已运行 {total_runtime/3600:.1f} 小时")

## 错误快速诊断表 🔍

| 错误现象 | 可能原因 | 快速检查 | 解决方案 |
|---------|---------|---------|---------|
| 大量"跳过重复新闻" | excludeContIds参数未使用 | 检查API请求参数 | 设置`excludeContIds: self.collected_news_ids` |
| 程序过早停止 | 时间判断逻辑错误 | 启用调试模式检查时间戳 | 修改`is_today_news`函数，添加调试信息 |
| API返回空数据 | 参数配置错误 | 对比抓包数据和代码参数 | 修正channelId、参数名称和格式 |
| HTML解析无数据 | CSS选择器失效 | 检查页面源码结构 | 更新选择器，使用通用匹配 |
| 网络请求频繁失败 | 缺少重试机制 | 查看网络连接和请求频率 | 添加重试逻辑和延时 |
| 中断后数据丢失 | 没有自动保存 | 检查是否有临时文件 | 实现定期保存和断点续传 |
| 内存占用过高 | 数据全部保存在内存 | 监控内存使用情况 | 实现流式处理和分批保存 |
| CSV文件乱码 | 编码问题 | 用Excel打开检查 | 使用`utf-8-sig`编码 |

## 紧急故障处理流程 🚨

### 1. 爬虫完全无法运行
```bash
# 步骤1: 检查网络连接
curl -I https://m.thepaper.cn/channel_25950

# 步骤2: 测试API接口
python debug_timestamps.py

# 步骤3: 检查HTML结构
python -c "
import requests
from bs4 import BeautifulSoup
response = requests.get('https://m.thepaper.cn/channel_25950')
soup = BeautifulSoup(response.text, 'html.parser')
print('找到的wrapper数量:', len(soup.find_all('div', class_=lambda x: x and 'wrapper' in x)))
"
```

### 2. 数据质量问题
```python
# 快速数据验证脚本
def quick_data_check(csv_file):
    import pandas as pd

    df = pd.read_csv(csv_file)
    print(f"总数据量: {len(df)}")
    print(f"重复ID数量: {df['news_id'].duplicated().sum()}")
    print(f"空标题数量: {df['title'].isna().sum()}")
    print(f"数据来源分布: {df['data_source'].value_counts()}")

    # 检查时间分布
    print(f"发布时间样本: {df['pub_time'].head()}")
```

### 3. 性能问题诊断
```python
# 性能监控脚本
import psutil
import time

def monitor_performance():
    process = psutil.Process()

    while True:
        cpu_percent = process.cpu_percent()
        memory_mb = process.memory_info().rss / 1024 / 1024

        print(f"CPU: {cpu_percent:.1f}%, 内存: {memory_mb:.1f}MB")
        time.sleep(10)
```

## 版本更新和维护指南 🔄

### 1. 定期检查项目
- **每周**: 检查API接口是否正常
- **每月**: 验证HTML选择器是否有效
- **每季度**: 更新User-Agent和请求头
- **发现问题时**: 立即更新相关配置

### 2. 版本控制最佳实践
```bash
# 创建新版本前的检查
git status
git add .
git commit -m "修复重复新闻问题 - 使用excludeContIds参数"

# 标记重要版本
git tag -a v1.1.0 -m "修复重复新闻和时间判断问题"
git push origin v1.1.0
```

### 3. 配置文件管理
```python
# config.py - 集中管理配置
class CrawlerConfig:
    # API配置
    API_URLS = {
        'news': 'https://api.thepaper.cn/contentapi/nodeCont/getByChannelId',
        'depth': 'https://api.thepaper.cn/contentapi/channel/depth'
    }

    # 板块配置
    CHANNEL_IDS = {
        'news': '25950',
        'depth': '25426'
    }

    # HTML选择器配置
    SELECTORS = {
        'news': {
            'container': 'div.index_wrapper__9rz3z',
            'title': 'h3.index_title__aGAqD'
        },
        'depth': {
            'container': 'div.index_wrapper__JCwkF',
            'title': 'h3.index_title__xxxxx'
        }
    }

    # 请求配置
    REQUEST_CONFIG = {
        'timeout': 15,
        'retry_times': 3,
        'delay_range': (1.0, 2.0)
    }

# 在爬虫中使用配置
from config import CrawlerConfig

class ThepaperNewsCrawler:
    def __init__(self):
        self.config = CrawlerConfig()
        self.api_url = self.config.API_URLS['news']
        self.channel_id = self.config.CHANNEL_IDS['news']
```

## 总结 📋

通过本指南中的错误案例和解决方案，您可以：

1. **预防常见错误**: 了解典型问题的根本原因
2. **快速诊断问题**: 使用诊断表和检查脚本
3. **实施最佳实践**: 采用经过验证的代码模式
4. **建立监控机制**: 及时发现和处理问题
5. **维护代码质量**: 保持爬虫的长期稳定运行

**关键要点:**
- ✅ 始终使用`excludeContIds`参数避免重复
- ✅ 实现详细的调试和日志记录
- ✅ 添加重试机制和错误处理
- ✅ 定期保存数据，支持断点续传
- ✅ 监控性能和内存使用
- ✅ 保持配置的灵活性和可维护性

遵循这些指导原则，您的澎湃新闻爬虫将能够稳定、高效地运行，并且能够快速应对各种突发问题。
```
```
```

## 板块特定配置示例

### 财经板块 (假设)
```python
# API配置
self.api_url = "https://api.thepaper.cn/contentapi/finance/list"
payload = {
    "categoryId": "finance",
    "pageNum": page_num,
    "pageSize": 10,
    "timestamp": start_time
}

# HTML配置
wrappers = soup.find_all('div', class_='finance_item__xxxxx')
```

### 科技板块 (假设)
```python
# API配置
self.api_url = "https://api.thepaper.cn/contentapi/tech/articles"
payload = {
    "section": "tech",
    "page": page_num,
    "limit": 10,
    "before": start_time
}

# HTML配置
wrappers = soup.find_all('div', class_='tech_wrapper__xxxxx')
```

## 测试和验证

### 1. 单元测试
```python
# 测试API请求
def test_api_request():
    crawler = ThePaper新板块Crawler()
    result = crawler.get_news_by_api(1)
    assert result is not None
    assert len(result['news_items']) > 0

# 测试HTML解析
def test_html_parsing():
    crawler = ThePaper新板块Crawler()
    news_list = crawler.get_first_page_html()
    assert len(news_list) > 0
```

### 2. 数据验证
- 检查新闻ID是否唯一
- 验证时间过滤是否正确
- 确认去重逻辑是否有效

## 部署和维护

### 1. 定期检查
- API接口是否变更
- HTML结构是否调整
- 参数格式是否修改

### 2. 错误处理
- 添加重试机制
- 记录详细日志
- 设置异常告警

### 3. 性能优化
- 控制请求频率
- 使用连接池
- 实现断点续传

## AI开发指导模板

当你需要让AI开发新板块爬虫时，使用以下标准模板：

### 标准指导模板

```
请参照 thepaper_depth_crawler.py 的代码结构，为澎湃新闻[板块名称]开发爬虫。

参考文件：thepaper_depth_crawler.py
目标：开发 thepaper_[板块名]_crawler.py

需要修改的关键部分：

1. 类名和基础配置：
   - 类名：ThepaperDepthCrawler → Thepaper[板块名]Crawler
   - base_url：修改为新板块URL
   - api_url：修改为新板块API地址

2. API请求参数（重点）：
   请求URL：[你抓包得到的API地址]
   请求参数：
   ```json
   {
     [你抓包得到的参数]
   }
   ```

3. 响应数据解析：
   响应结构：
   ```json
   {
     [你抓包得到的响应结构]
   }
   ```

4. HTML选择器：
   - 新闻容器：[CSS选择器]
   - 标题选择器：[CSS选择器]

要求：
- 严格保持 thepaper_depth_crawler.py 的所有功能特性
- 保持相同的代码结构和错误处理
- 保持相同的日志输出格式
- 只修改必要的配置和参数部分
- 确保持续爬取和时间过滤功能正常
```

### 具体示例

```
请参照 thepaper_depth_crawler.py 的代码结构，为澎湃新闻财经板块开发爬虫。

参考文件：thepaper_depth_crawler.py
目标：开发 thepaper_finance_crawler.py

需要修改的关键部分：

1. 类名和基础配置：
   - 类名：ThepaperDepthCrawler → ThepaperFinanceCrawler
   - base_url：https://m.thepaper.cn/channel_25950
   - api_url：https://api.thepaper.cn/contentapi/finance/list

2. API请求参数（重点）：
   请求URL：https://api.thepaper.cn/contentapi/finance/list
   请求参数：
   ```json
   {
     "channelId": "25950",
     "pageNum": 2,
     "pageSize": 10,
     "startTime": 1752722830037
   }
   ```

3. 响应数据解析：
   响应结构：
   ```json
   {
     "code": 200,
     "data": {
       "hasNext": true,
       "list": [...]
     }
   }
   ```

4. HTML选择器：
   - 新闻容器：div.finance_wrapper__xxxxx
   - 标题选择器：h3.finance_title__xxxxx

要求：
- 严格保持 thepaper_depth_crawler.py 的所有功能特性
- 保持相同的代码结构和错误处理
- 保持相同的日志输出格式
- 只修改必要的配置和参数部分
- 确保持续爬取和时间过滤功能正常
```

### 关键强调点

给AI指导时必须强调：

1. **"严格参照现有代码结构"** - 不要重新设计架构
2. **"只修改配置部分，保持逻辑不变"** - 确保功能一致性
3. **"重点关注API参数差异"** - 这是最容易出错的地方
4. **"保持所有现有功能"** - 持续爬取、时间过滤、错误处理等
5. **"测试前先对比两个文件的差异"** - 确保修改正确

### AI开发验证清单

完成开发后，要求AI确认：
- [ ] 类名和文件名正确修改
- [ ] API请求参数完全正确
- [ ] 响应数据解析逻辑正确
- [ ] HTML选择器适配新板块
- [ ] 保持原有的错误处理机制
- [ ] 保持原有的日志输出格式
- [ ] 时间过滤功能正常工作
- [ ] 持续爬取逻辑完整

## 总结

开发新板块爬虫的核心是：
1. **抓包分析API** - 最重要的步骤
2. **HTML结构分析** - 确定选择器
3. **参数适配** - 根据板块特点调整
4. **AI指导** - 使用标准模板确保质量
5. **测试验证** - 确保功能正常

按照这个指南和AI指导模板，你可以高效地为澎湃新闻的任意板块开发爬虫。
