"""
数据库管理模块
负责MySQL数据库的连接、表创建和数据操作
"""

import mysql.connector
from mysql.connector import Error
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from config import get_database_config


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.config = get_database_config()
        self.connection = None
        self.cursor = None
    
    def connect(self) -> bool:
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset=self.config['charset'],
                autocommit=True
            )
            
            if self.connection.is_connected():
                self.cursor = self.connection.cursor(dictionary=True)
                print(f"✅ 数据库连接成功: {self.config['user']}@{self.config['host']}:{self.config['port']}/{self.config['database']}")
                return True
                
        except Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("✅ 数据库连接已关闭")
    
    def create_database_if_not_exists(self) -> bool:
        """创建数据库（如果不存在）"""
        try:
            # 先连接到MySQL服务器（不指定数据库）
            temp_connection = mysql.connector.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                charset=self.config['charset']
            )
            
            temp_cursor = temp_connection.cursor()
            
            # 创建数据库
            create_db_query = f"CREATE DATABASE IF NOT EXISTS {self.config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
            temp_cursor.execute(create_db_query)
            
            print(f"✅ 数据库 {self.config['database']} 已确保存在")
            
            temp_cursor.close()
            temp_connection.close()
            
            return True
            
        except Error as e:
            print(f"❌ 创建数据库失败: {e}")
            return False
    
    def create_tables(self) -> bool:
        """创建所有必要的数据表"""
        if not self.connection or not self.connection.is_connected():
            print("❌ 数据库未连接")
            return False
        
        try:
            # 创建文章表
            create_articles_table = """
            CREATE TABLE IF NOT EXISTS articles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(500) NOT NULL COMMENT '文章标题',
                content TEXT NOT NULL COMMENT '文章内容',
                summary TEXT COMMENT '文章摘要',
                category VARCHAR(100) COMMENT '文章分类',
                keywords JSON COMMENT '关键词列表',
                source_topics JSON COMMENT '来源话题信息',
                related_news JSON COMMENT '相关新闻信息',
                importance_score DECIMAL(3,2) COMMENT '重要性评分',
                word_count INT COMMENT '字数统计',
                status ENUM('draft', 'published', 'archived') DEFAULT 'draft' COMMENT '文章状态',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                published_at TIMESTAMP NULL COMMENT '发布时间',
                INDEX idx_category (category),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at),
                INDEX idx_importance_score (importance_score)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生成的文章表'
            """
            
            # 创建话题处理记录表
            create_topics_table = """
            CREATE TABLE IF NOT EXISTS topic_processing_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                topic_title VARCHAR(500) NOT NULL COMMENT '话题标题',
                topic_category VARCHAR(100) COMMENT '话题分类',
                source_platforms JSON COMMENT '来源平台',
                source_titles JSON COMMENT '原始标题列表',
                keywords JSON COMMENT '提取的关键词',
                search_queries JSON COMMENT '搜索查询词',
                background_context TEXT COMMENT '背景信息',
                key_points JSON COMMENT '关键要点',
                related_entities JSON COMMENT '相关实体',
                importance_score DECIMAL(3,2) COMMENT '重要性评分',
                processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '处理状态',
                article_id INT COMMENT '关联的文章ID',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_category (topic_category),
                INDEX idx_status (processing_status),
                INDEX idx_created_at (created_at),
                INDEX idx_importance_score (importance_score),
                FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话题处理记录表'
            """
            
            # 创建新闻检索记录表
            create_news_retrieval_table = """
            CREATE TABLE IF NOT EXISTS news_retrieval_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                topic_id INT NOT NULL COMMENT '话题ID',
                search_query VARCHAR(500) NOT NULL COMMENT '搜索查询',
                retrieved_count INT DEFAULT 0 COMMENT '检索到的新闻数量',
                news_data JSON COMMENT '检索到的新闻数据',
                similarity_threshold DECIMAL(3,2) COMMENT '相似度阈值',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                INDEX idx_topic_id (topic_id),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (topic_id) REFERENCES topic_processing_records(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻检索记录表'
            """
            
            # 创建系统日志表
            create_logs_table = """
            CREATE TABLE IF NOT EXISTS system_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR') NOT NULL COMMENT '日志级别',
                module VARCHAR(100) NOT NULL COMMENT '模块名称',
                operation VARCHAR(200) NOT NULL COMMENT '操作名称',
                message TEXT NOT NULL COMMENT '日志消息',
                details JSON COMMENT '详细信息',
                execution_time DECIMAL(10,3) COMMENT '执行时间(秒)',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                INDEX idx_level (log_level),
                INDEX idx_module (module),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表'
            """
            
            # 执行创建表的SQL
            tables = [
                ("articles", create_articles_table),
                ("topic_processing_records", create_topics_table),
                ("news_retrieval_records", create_news_retrieval_table),
                ("system_logs", create_logs_table)
            ]
            
            for table_name, create_sql in tables:
                self.cursor.execute(create_sql)
                print(f"✅ 表 {table_name} 已创建或已存在")
            
            print("✅ 所有数据表创建完成")
            return True
            
        except Error as e:
            print(f"❌ 创建数据表失败: {e}")
            return False
    
    def insert_article(self, article_data: Dict[str, Any]) -> Optional[int]:
        """插入文章记录"""
        try:
            insert_query = """
            INSERT INTO articles (
                title, content, summary, category, keywords, source_topics,
                related_news, importance_score, word_count, status
            ) VALUES (
                %(title)s, %(content)s, %(summary)s, %(category)s, %(keywords)s,
                %(source_topics)s, %(related_news)s, %(importance_score)s,
                %(word_count)s, %(status)s
            )
            """
            
            # 准备数据
            data = {
                'title': article_data.get('title', ''),
                'content': article_data.get('content', ''),
                'summary': article_data.get('summary', ''),
                'category': article_data.get('category', ''),
                'keywords': json.dumps(article_data.get('keywords', []), ensure_ascii=False),
                'source_topics': json.dumps(article_data.get('source_topics', {}), ensure_ascii=False),
                'related_news': json.dumps(article_data.get('related_news', []), ensure_ascii=False),
                'importance_score': article_data.get('importance_score', 0.0),
                'word_count': len(article_data.get('content', '')),
                'status': article_data.get('status', 'draft')
            }
            
            self.cursor.execute(insert_query, data)
            article_id = self.cursor.lastrowid
            
            print(f"✅ 文章已保存，ID: {article_id}")
            return article_id
            
        except Error as e:
            print(f"❌ 插入文章失败: {e}")
            return None
    
    def insert_topic_record(self, topic_data: Dict[str, Any]) -> Optional[int]:
        """插入话题处理记录"""
        try:
            insert_query = """
            INSERT INTO topic_processing_records (
                topic_title, topic_category, source_platforms, source_titles,
                keywords, search_queries, background_context, key_points,
                related_entities, importance_score, processing_status
            ) VALUES (
                %(topic_title)s, %(topic_category)s, %(source_platforms)s,
                %(source_titles)s, %(keywords)s, %(search_queries)s,
                %(background_context)s, %(key_points)s, %(related_entities)s,
                %(importance_score)s, %(processing_status)s
            )
            """
            
            # 准备数据
            data = {
                'topic_title': topic_data.get('topic_title', ''),
                'topic_category': topic_data.get('topic_category', ''),
                'source_platforms': json.dumps(topic_data.get('source_platforms', []), ensure_ascii=False),
                'source_titles': json.dumps(topic_data.get('source_titles', []), ensure_ascii=False),
                'keywords': json.dumps(topic_data.get('keywords', []), ensure_ascii=False),
                'search_queries': json.dumps(topic_data.get('search_queries', []), ensure_ascii=False),
                'background_context': topic_data.get('background_context', ''),
                'key_points': json.dumps(topic_data.get('key_points', []), ensure_ascii=False),
                'related_entities': json.dumps(topic_data.get('related_entities', {}), ensure_ascii=False),
                'importance_score': topic_data.get('importance_score', 0.0),
                'processing_status': topic_data.get('processing_status', 'pending')
            }
            
            self.cursor.execute(insert_query, data)
            topic_id = self.cursor.lastrowid
            
            print(f"✅ 话题记录已保存，ID: {topic_id}")
            return topic_id
            
        except Error as e:
            print(f"❌ 插入话题记录失败: {e}")
            return None
    
    def get_articles(self, limit: int = 10, status: str = None) -> List[Dict]:
        """获取文章列表"""
        try:
            query = "SELECT * FROM articles"
            params = []
            
            if status:
                query += " WHERE status = %s"
                params.append(status)
            
            query += " ORDER BY created_at DESC LIMIT %s"
            params.append(limit)
            
            self.cursor.execute(query, params)
            articles = self.cursor.fetchall()
            
            # 解析JSON字段
            for article in articles:
                if article['keywords']:
                    article['keywords'] = json.loads(article['keywords'])
                if article['source_topics']:
                    article['source_topics'] = json.loads(article['source_topics'])
                if article['related_news']:
                    article['related_news'] = json.loads(article['related_news'])
            
            return articles
            
        except Error as e:
            print(f"❌ 获取文章列表失败: {e}")
            return []
    
    def update_article_status(self, article_id: int, status: str) -> bool:
        """更新文章状态"""
        try:
            update_query = "UPDATE articles SET status = %s WHERE id = %s"
            self.cursor.execute(update_query, (status, article_id))
            
            if status == 'published':
                # 更新发布时间
                publish_query = "UPDATE articles SET published_at = CURRENT_TIMESTAMP WHERE id = %s"
                self.cursor.execute(publish_query, (article_id,))
            
            print(f"✅ 文章 {article_id} 状态已更新为: {status}")
            return True
            
        except Error as e:
            print(f"❌ 更新文章状态失败: {e}")
            return False
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            stats = {}
            
            # 文章统计
            self.cursor.execute("SELECT status, COUNT(*) as count FROM articles GROUP BY status")
            article_stats = {row['status']: row['count'] for row in self.cursor.fetchall()}
            stats['articles'] = article_stats
            
            # 话题统计
            self.cursor.execute("SELECT processing_status, COUNT(*) as count FROM topic_processing_records GROUP BY processing_status")
            topic_stats = {row['processing_status']: row['count'] for row in self.cursor.fetchall()}
            stats['topics'] = topic_stats
            
            # 总数统计
            self.cursor.execute("SELECT COUNT(*) as total FROM articles")
            stats['total_articles'] = self.cursor.fetchone()['total']
            
            self.cursor.execute("SELECT COUNT(*) as total FROM topic_processing_records")
            stats['total_topics'] = self.cursor.fetchone()['total']
            
            return stats
            
        except Error as e:
            print(f"❌ 获取数据库统计失败: {e}")
            return {}


def init_database() -> bool:
    """初始化数据库"""
    print("🚀 开始初始化数据库...")
    
    db_manager = DatabaseManager()
    
    # 创建数据库
    if not db_manager.create_database_if_not_exists():
        return False
    
    # 连接数据库
    if not db_manager.connect():
        return False
    
    # 创建表
    if not db_manager.create_tables():
        db_manager.disconnect()
        return False
    
    db_manager.disconnect()
    print("✅ 数据库初始化完成")
    return True


if __name__ == "__main__":
    # 测试数据库初始化
    init_database()
