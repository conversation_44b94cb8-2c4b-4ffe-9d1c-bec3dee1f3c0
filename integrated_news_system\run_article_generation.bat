@echo off
REM 文章生成流水线定时任务脚本 (Windows版本)
REM 每4小时执行一次

setlocal enabledelayedexpansion

REM 设置工作目录
cd /d "%~dp0"

REM 设置日志目录
if not exist "logs" mkdir logs
set LOG_FILE=logs\article_generation_%date:~0,4%%date:~5,2%%date:~8,2%.log

REM 日志函数
set TIMESTAMP=%date% %time%
echo [%TIMESTAMP%] 🚀 开始文章生成流水线任务... >> %LOG_FILE%

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo [%TIMESTAMP%] ❌ Python 未找到 >> %LOG_FILE%
    exit /b 1
)

REM 检查必要文件
if not exist "quick_start.py" (
    echo [%TIMESTAMP%] ❌ quick_start.py 文件不存在 >> %LOG_FILE%
    exit /b 1
)

REM 检查是否有新闻数据
for /f %%i in ('dir /b news_output\*.json 2^>nul ^| find /c /v ""') do set NEWS_FILES=%%i
if "!NEWS_FILES!"=="0" (
    echo [%TIMESTAMP%] ⚠️ 警告: news_output目录没有新闻数据文件 >> %LOG_FILE%
)

REM 执行文章生成流水线 (--auto 参数表示自动化无交互模式)
echo [%TIMESTAMP%] 📝 执行文章生成流水线... >> %LOG_FILE%
echo [%TIMESTAMP%] 🔄 包含: 话题合并 → 深度分析 → 新闻检索 → 文章生成 >> %LOG_FILE%

python quick_start.py --auto >> %LOG_FILE% 2>&1

if errorlevel 1 (
    echo [%TIMESTAMP%] ❌ 文章生成流水线失败 >> %LOG_FILE%
    exit /b 1
) else (
    echo [%TIMESTAMP%] ✅ 文章生成流水线完成 >> %LOG_FILE%
    echo [%TIMESTAMP%] 📊 请检查数据库中的新文章 >> %LOG_FILE%
    exit /b 0
)
