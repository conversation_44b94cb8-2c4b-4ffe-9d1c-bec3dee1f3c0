#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试澎湃新闻API重复问题的脚本
"""

import requests
import json
import time

def test_api_duplicates():
    """测试API是否返回重复数据"""
    
    api_url = "https://api.thepaper.cn/contentapi/nodeCont/getByChannelId"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://m.thepaper.cn/channel_25950',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Origin': 'https://m.thepaper.cn'
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    # 测试连续两页的数据
    all_news_ids = []
    start_time = int(time.time() * 1000)
    
    for page in [1, 2]:
        print(f"\n=== 测试第 {page} 页 ===")
        
        payload = {
            "channelId": "25950",
            "excludeContIds": [],  # 不排除任何ID
            "listRecommendIds": [],
            "pageSize": 10,
            "startTime": start_time,
            "pageNum": page
        }
        
        try:
            response = session.post(api_url, json=payload, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data.get('code') == 200:
                api_data = data.get('data', {})
                news_list = api_data.get('list', [])
                start_time = api_data.get('startTime')  # 更新startTime
                
                print(f"获取到 {len(news_list)} 条新闻")
                print(f"返回的startTime: {start_time}")
                
                page_news_ids = []
                for news in news_list:
                    news_id = str(news.get('contId', ''))
                    title = news.get('name', '')[:50]
                    page_news_ids.append(news_id)
                    
                    if news_id in all_news_ids:
                        print(f"  重复: ID:{news_id} - {title}...")
                    else:
                        print(f"  新增: ID:{news_id} - {title}...")
                
                # 检查本页内部是否有重复
                if len(page_news_ids) != len(set(page_news_ids)):
                    print(f"  警告: 本页内部有重复ID!")
                
                all_news_ids.extend(page_news_ids)
                
            else:
                print(f"API返回错误: {data}")
                
        except Exception as e:
            print(f"请求失败: {e}")
    
    print(f"\n=== 总结 ===")
    print(f"总共获取到 {len(all_news_ids)} 条新闻")
    print(f"去重后有 {len(set(all_news_ids))} 条唯一新闻")
    print(f"重复数量: {len(all_news_ids) - len(set(all_news_ids))}")

if __name__ == "__main__":
    test_api_duplicates()
